.PHONY: help install dev dev-backend dev-frontend stop clean

# Default target
help:
	@echo "Available commands:"
	@echo "  make install     - Install all dependencies"
	@echo "  make dev         - Start both backend and frontend in development mode"
	@echo "  make dev-backend - Start only the backend server"
	@echo "  make dev-frontend- Start only the frontend server"
	@echo "  make stop        - Stop all running services"
	@echo "  make clean       - Clean all dependencies and build files"

# Install dependencies
install:
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "All dependencies installed!"

# Start both services
dev:
	@echo "Starting AlertAI development servers..."
	@echo "Backend will be available at http://localhost:8001"
	@echo "Frontend will be available at http://localhost:3000"
	@echo "Press Ctrl+C to stop all services"
	@make -j2 dev-backend dev-frontend

# Start backend only
dev-backend:
	@echo "Starting FastAPI backend..."
	cd backend && python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8001

# Start frontend only
dev-frontend:
	@echo "Starting Next.js frontend..."
	cd frontend && npm run dev

# Stop services (for manual cleanup if needed)
stop:
	@echo "Stopping services..."
	@pkill -f "uvicorn main:app" || true
	@pkill -f "next-server" || true
	@echo "Services stopped"

# Clean dependencies and build files
clean:
	@echo "Cleaning backend..."
	cd backend && find . -type d -name "__pycache__" -exec rm -rf {} + || true
	cd backend && find . -name "*.pyc" -delete || true
	@echo "Cleaning frontend..."
	cd frontend && rm -rf node_modules .next || true
	@echo "Cleanup complete!"
