"""
Tests for authentication and authorization system
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os

from app.main import create_application
from app.db.base import Base
from app.api.deps import get_db
from app.core.auth import AuthService, RoleManager
from app.services.user import UserService


# Test database setup
@pytest.fixture
def test_db():
    """Create a test database"""
    # Create temporary database file
    db_fd, db_path = tempfile.mkstemp()
    engine = create_engine(f"sqlite:///{db_path}", connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    def override_get_db():
        try:
            db = TestingSessionLocal()
            yield db
        finally:
            db.close()
    
    # Create app with test database
    app = create_application()
    app.dependency_overrides[get_db] = override_get_db
    
    yield TestingSessionLocal()
    
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)
    app.dependency_overrides.clear()


@pytest.fixture
def client(test_db):
    """Create a test client"""
    app = create_application()
    
    def override_get_db():
        try:
            yield test_db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    return TestClient(app)


@pytest.fixture
def test_user(test_db):
    """Create a test user"""
    # Initialize roles and permissions
    RoleManager.create_default_roles(test_db)
    
    # Create test user
    user = UserService.create_user(
        db=test_db,
        email="<EMAIL>",
        password="TestPassword123",
        full_name="Test User"
    )
    
    # Assign analyst role
    UserService.assign_role_to_user(test_db, user.id, "analyst")
    
    return user


@pytest.fixture
def admin_user(test_db):
    """Create an admin user"""
    # Initialize roles and permissions
    RoleManager.create_default_roles(test_db)
    
    # Create admin user
    admin = RoleManager.create_default_admin_user(
        db=test_db,
        email="<EMAIL>",
        password="AdminPassword123"
    )
    
    return admin


class TestAuthService:
    """Test authentication service functions"""
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "TestPassword123"
        hashed = AuthService.get_password_hash(password)
        
        assert hashed != password
        assert AuthService.verify_password(password, hashed)
        assert not AuthService.verify_password("wrong_password", hashed)
    
    def test_jwt_token_creation_and_verification(self):
        """Test JWT token creation and verification"""
        data = {"sub": 123, "email": "<EMAIL>"}
        token = AuthService.create_access_token(data)
        
        assert token is not None
        assert isinstance(token, str)
        
        # Verify token
        payload = AuthService.verify_token(token)
        assert payload["sub"] == 123
        assert payload["email"] == "<EMAIL>"
    
    def test_user_authentication(self, test_db, test_user):
        """Test user authentication"""
        # Valid credentials
        authenticated_user = AuthService.authenticate_user(
            test_db, "<EMAIL>", "TestPassword123"
        )
        assert authenticated_user is not None
        assert authenticated_user.id == test_user.id
        
        # Invalid password
        invalid_user = AuthService.authenticate_user(
            test_db, "<EMAIL>", "wrong_password"
        )
        assert invalid_user is None
        
        # Invalid email
        invalid_user = AuthService.authenticate_user(
            test_db, "<EMAIL>", "TestPassword123"
        )
        assert invalid_user is None
    
    def test_permission_checking(self, test_db, test_user, admin_user):
        """Test permission checking"""
        # Admin user should have all permissions
        assert AuthService.check_permission(admin_user, "alerts:read")
        assert AuthService.check_permission(admin_user, "system:admin")
        
        # Regular user should have limited permissions
        assert AuthService.check_permission(test_user, "alerts:read")
        assert not AuthService.check_permission(test_user, "system:admin")


class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_system_initialization(self, client):
        """Test system initialization endpoint"""
        response = client.post("/api/auth/init-system", json={
            "email": "<EMAIL>",
            "password": "AdminPassword123",
            "full_name": "System Administrator"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "admin_user_id" in data
        assert data["admin_email"] == "<EMAIL>"
        
        # Should not allow initialization again
        response = client.post("/api/auth/init-system", json={
            "email": "<EMAIL>",
            "password": "Password123",
            "full_name": "Another Admin"
        })
        
        assert response.status_code == 400
        assert "already initialized" in response.json()["detail"]
    
    def test_user_registration(self, client, test_db):
        """Test user registration"""
        # Initialize system first
        RoleManager.create_default_roles(test_db)
        
        response = client.post("/api/auth/register", json={
            "email": "<EMAIL>",
            "password": "NewPassword123",
            "full_name": "New User",
            "department": "Security"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "New User"
        assert "analyst" in data["roles"]
    
    def test_login_and_token_generation(self, client, test_user):
        """Test login and token generation"""
        response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "TestPassword123"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    def test_login_with_invalid_credentials(self, client, test_user):
        """Test login with invalid credentials"""
        response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "wrong_password"
        })
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_get_current_user_info(self, client, test_user):
        """Test getting current user information"""
        # Login first
        login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "TestPassword123"
        })
        token = login_response.json()["access_token"]
        
        # Get user info
        response = client.get("/api/auth/me", headers={
            "Authorization": f"Bearer {token}"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "Test User"
        assert "analyst" in data["roles"]
    
    def test_password_change(self, client, test_user):
        """Test password change"""
        # Login first
        login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "TestPassword123"
        })
        token = login_response.json()["access_token"]
        
        # Change password
        response = client.post("/api/auth/change-password", 
            headers={"Authorization": f"Bearer {token}"},
            json={
                "current_password": "TestPassword123",
                "new_password": "NewPassword123",
                "confirm_password": "NewPassword123"
            }
        )
        
        assert response.status_code == 200
        assert "Password changed successfully" in response.json()["message"]
        
        # Test login with new password
        new_login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "NewPassword123"
        })
        
        assert new_login_response.status_code == 200
    
    def test_logout(self, client, test_user):
        """Test logout functionality"""
        # Login first
        login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "TestPassword123"
        })
        token = login_response.json()["access_token"]
        
        # Logout
        response = client.post("/api/auth/logout", headers={
            "Authorization": f"Bearer {token}"
        })
        
        assert response.status_code == 200
        assert "Successfully logged out" in response.json()["message"]


class TestRoleBasedAccess:
    """Test role-based access control"""
    
    def test_admin_access(self, client, admin_user):
        """Test admin access to protected endpoints"""
        # Login as admin
        login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "AdminPassword123"
        })
        token = login_response.json()["access_token"]
        
        # Access admin endpoint (when implemented)
        response = client.get("/api/auth/me", headers={
            "Authorization": f"Bearer {token}"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_superuser"] == True
    
    def test_regular_user_access(self, client, test_user):
        """Test regular user access limitations"""
        # Login as regular user
        login_response = client.post("/api/auth/token", data={
            "username": "<EMAIL>",
            "password": "TestPassword123"
        })
        token = login_response.json()["access_token"]
        
        # Access user info (should work)
        response = client.get("/api/auth/me", headers={
            "Authorization": f"Bearer {token}"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_superuser"] == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
