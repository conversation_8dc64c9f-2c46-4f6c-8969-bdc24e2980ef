#!/usr/bin/env python3
"""
Simple API test script for AlertAI authentication
"""

import requests
import json

BASE_URL = "http://localhost:8002"

def test_api():
    print("🧪 Testing AlertAI Authentication API")
    print("=" * 50)
    
    # Test 1: Root endpoint
    print("1. Testing root endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        print("   ✅ Root endpoint working")
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: Login
    print("\n2. Testing login...")
    try:
        login_data = {
            "username": "<EMAIL>",
            "password": "AdminPassword123"
        }
        response = requests.post(
            f"{BASE_URL}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print(f"   ✅ Login successful")
            print(f"   Token: {token[:50]}...")
            
            # Test 3: Get user info
            print("\n3. Testing user info...")
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                user_data = response.json()
                print(f"   ✅ User info retrieved")
                print(f"   User: {user_data.get('email')} ({user_data.get('full_name')})")
                print(f"   Roles: {user_data.get('roles', [])}")
                print(f"   Permissions: {len(user_data.get('permissions', []))} permissions")
            else:
                print(f"   ❌ User info failed: {response.text}")
                return False
            
            # Test 4: Test alerts endpoint
            print("\n4. Testing alerts endpoint...")
            response = requests.get(f"{BASE_URL}/api/alerts", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                alerts_data = response.json()
                print(f"   ✅ Alerts endpoint working")
                print(f"   Total alerts: {alerts_data.get('total', 0)}")
            else:
                print(f"   ⚠️  Alerts endpoint response: {response.text}")
            
            # Test 5: Test without authentication
            print("\n5. Testing alerts without authentication...")
            response = requests.get(f"{BASE_URL}/api/alerts")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 401:
                print("   ✅ Authentication required (as expected)")
            else:
                print(f"   ⚠️  Unexpected response: {response.text}")
            
        else:
            print(f"   ❌ Login failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All authentication tests completed!")
    print("✅ AlertAI authentication system is working!")
    return True

if __name__ == "__main__":
    success = test_api()
    if success:
        print("\n🚀 Ready for production use!")
        print("Next steps:")
        print("1. Register new users: POST /api/auth/register")
        print("2. Create alerts: POST /api/alerts")
        print("3. Add comments: POST /api/alerts/{id}/comments")
        print("4. Use AI chat: POST /api/chat")
    else:
        print("\n❌ Some tests failed. Check the server logs.")
