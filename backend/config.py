"""
Configuration management for AlertAI backend
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
import secrets


class Settings(BaseSettings):
    """Application settings with validation"""
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    api_base_url: str = "http://localhost:8001"
    
    # Frontend URLs for CORS
    frontend_urls: str = "http://localhost:3000,http://localhost:3001"
    
    # Database Configuration
    database_url: str = "sqlite:///./alertai.db"
    database_echo: bool = False
    
    # Security Configuration
    secret_key: str = secrets.token_urlsafe(32)
    access_token_expire_minutes: int = 30
    
    # File Upload Configuration
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_file_types: str = "image/jpeg,image/png,image/gif,text/plain,application/pdf,application/json"
    upload_directory: str = "uploads"
    
    # AI Service Configuration
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "deepseek-r1:8b"
    ai_request_timeout: int = 120  # seconds
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    # Logging Configuration
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Pagination Defaults
    default_page_size: int = 100
    max_page_size: int = 1000
    
    # Cache Configuration
    cache_ttl: int = 300  # 5 minutes
    
    # Development/Production flags
    debug: bool = False
    testing: bool = False
    
    @validator('frontend_urls')
    def validate_frontend_urls(cls, v):
        """Validate frontend URLs"""
        urls = [url.strip() for url in v.split(',') if url.strip()]
        if not urls:
            raise ValueError("At least one frontend URL must be provided")
        return v
    
    @validator('allowed_file_types')
    def validate_file_types(cls, v):
        """Validate file types"""
        types = [t.strip() for t in v.split(',') if t.strip()]
        if not types:
            raise ValueError("At least one file type must be allowed")
        return v
    
    @validator('max_file_size')
    def validate_file_size(cls, v):
        """Validate max file size"""
        if v <= 0:
            raise ValueError("Max file size must be positive")
        if v > 100 * 1024 * 1024:  # 100MB
            raise ValueError("Max file size cannot exceed 100MB")
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    def get_frontend_urls(self) -> List[str]:
        """Get list of frontend URLs"""
        return [url.strip() for url in self.frontend_urls.split(',') if url.strip()]
    
    def get_allowed_file_types(self) -> List[str]:
        """Get list of allowed file types"""
        return [t.strip() for t in self.allowed_file_types.split(',') if t.strip()]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Validation patterns
VALIDATION_PATTERNS = {
    'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'ip_address': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$',
    'domain': r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'hash_md5': r'^[a-fA-F0-9]{32}$',
    'hash_sha1': r'^[a-fA-F0-9]{40}$',
    'hash_sha256': r'^[a-fA-F0-9]{64}$',
    'hash_any': r'^[a-fA-F0-9]{32,64}$',
    'username': r'^[a-zA-Z0-9_-]{3,50}$',
    'filename': r'^[a-zA-Z0-9._-]+$',
}

# Security constants
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
}

# API Response codes and messages
API_RESPONSES = {
    'SUCCESS': {'code': 200, 'message': 'Success'},
    'CREATED': {'code': 201, 'message': 'Created successfully'},
    'BAD_REQUEST': {'code': 400, 'message': 'Bad request'},
    'UNAUTHORIZED': {'code': 401, 'message': 'Unauthorized'},
    'FORBIDDEN': {'code': 403, 'message': 'Forbidden'},
    'NOT_FOUND': {'code': 404, 'message': 'Resource not found'},
    'CONFLICT': {'code': 409, 'message': 'Resource conflict'},
    'VALIDATION_ERROR': {'code': 422, 'message': 'Validation error'},
    'INTERNAL_ERROR': {'code': 500, 'message': 'Internal server error'},
    'SERVICE_UNAVAILABLE': {'code': 503, 'message': 'Service unavailable'},
}

# Database configuration
DATABASE_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600,
    'echo': settings.database_echo,
}

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': settings.log_format,
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': settings.log_level,
            'formatter': 'default',
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'INFO',
            'formatter': 'detailed',
            'filename': 'alertai.log',
        },
    },
    'loggers': {
        'alertai': {
            'level': settings.log_level,
            'handlers': ['console', 'file'],
            'propagate': False,
        },
        'uvicorn': {
            'level': 'INFO',
            'handlers': ['console'],
            'propagate': False,
        },
    },
    'root': {
        'level': settings.log_level,
        'handlers': ['console'],
    },
}

# Feature flags
FEATURE_FLAGS = {
    'enable_ai_chat': True,
    'enable_file_uploads': True,
    'enable_threat_intel': True,
    'enable_correlation': True,
    'enable_webhooks': True,
    'enable_system_logs': True,
    'enable_rate_limiting': not settings.debug,
    'enable_security_headers': not settings.debug,
}

# Cache keys
CACHE_KEYS = {
    'alerts': 'alerts:list',
    'alert_detail': 'alert:{}',
    'comments': 'alert:{}:comments',
    'ai_findings': 'alert:{}:ai_findings',
    'analysts': 'analysts:list',
    'system_stats': 'system:stats',
}

def get_cache_key(template: str, *args) -> str:
    """Generate cache key from template and arguments"""
    return template.format(*args)

def is_production() -> bool:
    """Check if running in production environment"""
    return not settings.debug and not settings.testing

def is_development() -> bool:
    """Check if running in development environment"""
    return settings.debug

def is_testing() -> bool:
    """Check if running in test environment"""
    return settings.testing
