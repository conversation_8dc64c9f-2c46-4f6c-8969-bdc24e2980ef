from sqlalchemy.orm import Session
from database import DBAlert, DBComment, DBAIFinding, DBFileResource, DBAnalyst, DBCommentMention, DBChatSession, DBChatMessage, DBWebhookSource, DBWebhookAlert, DBAlertChangeLog, DBSystemLog
from models import Alert, Comment, AIFinding, FileResource, Analyst, CommentMention, ChatSession, ChatMessage, AlertSeverity, AlertStatus, AIFindingType, WebhookSource, WebhookSourceUpdate, AuthenticationType, AlertChangeLog, SystemLog, SystemLogFilter
from typing import List, Optional
from datetime import datetime
import time


class DatabaseService:
    """Service layer for database operations"""

    @staticmethod
    def create_alert(db: Session, title: str, description: str, severity: str, source: str,
                    status: str = "open", created_by: str = "System",
                    ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                    created_at: Optional[datetime] = None, updated_at: Optional[datetime] = None) -> DBAlert:
        """Create a new alert and log the creation"""
        db_alert = DBAlert(
            title=title,
            description=description,
            severity=severity,
            status=status,
            source=source
        )

        # Set custom timestamps if provided
        if created_at:
            db_alert.created_at = created_at
        if updated_at:
            db_alert.updated_at = updated_at

        db.add(db_alert)
        db.commit()
        db.refresh(db_alert)

        # Log the creation
        DatabaseService.create_change_log(
            db=db,
            alert_id=db_alert.id,
            changed_by=created_by,
            change_type="created",
            description=f"Alert created: {title}",
            ip_address=ip_address,
            user_agent=user_agent
        )

        return db_alert

    @staticmethod
    def get_alerts(db: Session, include_deleted: bool = False, limit: int = 100, offset: int = 0) -> List[DBAlert]:
        """Get alerts with pagination"""
        query = db.query(DBAlert)
        if not include_deleted:
            query = query.filter(DBAlert.is_deleted == False)
        return query.order_by(DBAlert.id.desc()).limit(limit).offset(offset).all()

    @staticmethod
    def get_alerts_count(db: Session, include_deleted: bool = False) -> int:
        """Get total count of alerts"""
        query = db.query(DBAlert)
        if not include_deleted:
            query = query.filter(DBAlert.is_deleted == False)
        return query.count()

    @staticmethod
    def get_alert_by_id(db: Session, alert_id: int) -> Optional[DBAlert]:
        """Get alert by ID"""
        return db.query(DBAlert).filter(DBAlert.id == alert_id, DBAlert.is_deleted == False).first()

    @staticmethod
    def update_alert(db: Session, alert_id: int, changed_by: str = "System",
                    ip_address: Optional[str] = None, user_agent: Optional[str] = None, **kwargs) -> Optional[DBAlert]:
        """Update an alert with provided fields and log changes"""
        db_alert = db.query(DBAlert).filter(DBAlert.id == alert_id, DBAlert.is_deleted == False).first()
        if not db_alert:
            return None

        # Create a copy of the original alert for change logging
        original_alert = DBAlert(
            id=db_alert.id,
            title=db_alert.title,
            description=db_alert.description,
            severity=db_alert.severity,
            status=db_alert.status,
            source=db_alert.source,
            assigned_analyst_id=db_alert.assigned_analyst_id,
            assigned_analyst_name=db_alert.assigned_analyst_name,
            tags=db_alert.tags,
            due_date=db_alert.due_date,
            priority=db_alert.priority,
            investigation_notes=db_alert.investigation_notes,
            mitre_techniques=db_alert.mitre_techniques,
            escalation_level=db_alert.escalation_level,
            sla_deadline=db_alert.sla_deadline,
            is_deleted=db_alert.is_deleted
        )

        # Update only provided fields
        for key, value in kwargs.items():
            if hasattr(db_alert, key) and value is not None:
                setattr(db_alert, key, value)

        # Always update the timestamp
        db_alert.updated_at = datetime.now()

        db.commit()
        db.refresh(db_alert)

        # Log the changes
        DatabaseService.log_alert_change(
            db=db,
            alert_id=alert_id,
            changed_by=changed_by,
            change_type="updated",
            old_alert=original_alert,
            new_alert=db_alert,
            ip_address=ip_address,
            user_agent=user_agent
        )

        return db_alert

    @staticmethod
    def delete_alert(db: Session, alert_id: int, soft_delete: bool = True,
                    changed_by: str = "System", ip_address: Optional[str] = None,
                    user_agent: Optional[str] = None) -> bool:
        """Delete an alert (soft delete by default) and log the change"""
        db_alert = db.query(DBAlert).filter(DBAlert.id == alert_id).first()
        if not db_alert:
            return False

        if soft_delete:
            db_alert.is_deleted = True
            db_alert.updated_at = datetime.now()
            db.commit()

            # Log the soft delete
            DatabaseService.create_change_log(
                db=db,
                alert_id=alert_id,
                changed_by=changed_by,
                change_type="soft_deleted",
                description=f"Alert moved to trash by {changed_by}",
                ip_address=ip_address,
                user_agent=user_agent
            )
        else:
            # Log the hard delete before actually deleting
            DatabaseService.create_change_log(
                db=db,
                alert_id=alert_id,
                changed_by=changed_by,
                change_type="hard_deleted",
                description=f"Alert permanently deleted by {changed_by}",
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Hard delete - also delete related records
            db.query(DBComment).filter(DBComment.alert_id == alert_id).delete()
            db.query(DBAIFinding).filter(DBAIFinding.alert_id == alert_id).delete()
            db.query(DBFileResource).filter(DBFileResource.alert_id == alert_id).delete()
            db.query(DBAlertChangeLog).filter(DBAlertChangeLog.alert_id == alert_id).delete()
            db.delete(db_alert)
            db.commit()

        return True

    @staticmethod
    def create_change_log(db: Session, alert_id: int, changed_by: str, change_type: str,
                         field_name: Optional[str] = None, old_value: Optional[str] = None,
                         new_value: Optional[str] = None, description: Optional[str] = None,
                         ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> DBAlertChangeLog:
        """Create a new change log entry"""
        db_change_log = DBAlertChangeLog(
            alert_id=alert_id,
            changed_by=changed_by,
            change_type=change_type,
            field_name=field_name,
            old_value=old_value,
            new_value=new_value,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.add(db_change_log)
        db.commit()
        db.refresh(db_change_log)
        return db_change_log

    @staticmethod
    def get_change_logs_by_alert_id(db: Session, alert_id: int) -> List[DBAlertChangeLog]:
        """Get all change logs for an alert"""
        return db.query(DBAlertChangeLog).filter(
            DBAlertChangeLog.alert_id == alert_id
        ).order_by(DBAlertChangeLog.changed_at.desc()).all()

    @staticmethod
    def log_alert_change(db: Session, alert_id: int, changed_by: str, change_type: str,
                        old_alert: Optional[DBAlert] = None, new_alert: Optional[DBAlert] = None,
                        ip_address: Optional[str] = None, user_agent: Optional[str] = None):
        """Log changes to an alert by comparing old and new values"""

        if change_type == "created":
            DatabaseService.create_change_log(
                db=db,
                alert_id=alert_id,
                changed_by=changed_by,
                change_type="created",
                description=f"Alert created with title: {new_alert.title if new_alert else 'Unknown'}",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return

        if change_type == "deleted":
            DatabaseService.create_change_log(
                db=db,
                alert_id=alert_id,
                changed_by=changed_by,
                change_type="deleted",
                description=f"Alert deleted (soft delete: {old_alert.is_deleted if old_alert else True})",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return

        # For updates, compare fields
        if old_alert and new_alert:
            fields_to_check = [
                ('title', 'Title'),
                ('description', 'Description'),
                ('severity', 'Severity'),
                ('status', 'Status'),
                ('source', 'Source'),
                ('assigned_analyst_id', 'Assigned Analyst ID'),
                ('assigned_analyst_name', 'Assigned Analyst'),
                ('priority', 'Priority'),
                ('escalation_level', 'Escalation Level'),
                ('due_date', 'Due Date'),
                ('investigation_notes', 'Investigation Notes'),
                ('tags', 'Tags'),
                ('mitre_techniques', 'MITRE Techniques')
            ]

            for field_name, display_name in fields_to_check:
                old_value = getattr(old_alert, field_name, None)
                new_value = getattr(new_alert, field_name, None)

                # Convert to string for comparison
                old_str = str(old_value) if old_value is not None else None
                new_str = str(new_value) if new_value is not None else None

                if old_str != new_str:
                    DatabaseService.create_change_log(
                        db=db,
                        alert_id=alert_id,
                        changed_by=changed_by,
                        change_type="updated",
                        field_name=field_name,
                        old_value=old_str,
                        new_value=new_str,
                        description=f"{display_name} changed from '{old_str}' to '{new_str}'",
                        ip_address=ip_address,
                        user_agent=user_agent
                    )

    @staticmethod
    def create_comment(db: Session, alert_id: int, author: str, content: str, author_id: Optional[int] = None,
                      created_at: Optional[datetime] = None, changed_by: str = "System",
                      ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> DBComment:
        """Create a new comment and log the activity"""
        db_comment = DBComment(
            alert_id=alert_id,
            author=author,
            author_id=author_id,
            content=content
        )

        # Set custom timestamp if provided
        if created_at:
            db_comment.created_at = created_at

        db.add(db_comment)
        db.commit()
        db.refresh(db_comment)

        # Log the comment creation
        DatabaseService.create_change_log(
            db=db,
            alert_id=alert_id,
            changed_by=changed_by,
            change_type="comment_added",
            description=f"Comment added by {author}: {content[:100]}{'...' if len(content) > 100 else ''}",
            ip_address=ip_address,
            user_agent=user_agent
        )

        return db_comment

    @staticmethod
    def get_comments_by_alert_id(db: Session, alert_id: int) -> List[DBComment]:
        """Get all comments for an alert"""
        return db.query(DBComment).filter(DBComment.alert_id == alert_id).order_by(DBComment.created_at).all()

    @staticmethod
    def create_ai_finding(db: Session, alert_id: int, finding_type: str, title: str, content: str, confidence: float,
                         created_at: Optional[datetime] = None) -> DBAIFinding:
        """Create a new AI finding"""
        db_finding = DBAIFinding(
            alert_id=alert_id,
            type=finding_type,
            title=title,
            content=content,
            confidence=confidence
        )

        # Set custom timestamp if provided
        if created_at:
            db_finding.created_at = created_at

        db.add(db_finding)
        db.commit()
        db.refresh(db_finding)
        return db_finding

    @staticmethod
    def get_ai_findings_by_alert_id(db: Session, alert_id: int) -> List[DBAIFinding]:
        """Get all AI findings for an alert"""
        return db.query(DBAIFinding).filter(DBAIFinding.alert_id == alert_id).order_by(DBAIFinding.created_at).all()

    @staticmethod
    def create_file_resource(db: Session, alert_id: int, filename: str, file_path: str, file_type: str,
                           file_size: int, is_image: bool, uploaded_by: str, uploaded_by_id: Optional[int] = None,
                           thumbnail_path: Optional[str] = None, metadata: Optional[dict] = None,
                           changed_by: str = "System", ip_address: Optional[str] = None,
                           user_agent: Optional[str] = None) -> DBFileResource:
        """Create a new file resource and log the activity"""
        db_resource = DBFileResource(
            alert_id=alert_id,
            filename=filename,
            file_path=file_path,
            file_type=file_type,
            file_size=file_size,
            is_image=is_image,
            uploaded_by=uploaded_by,
            uploaded_by_id=uploaded_by_id,
            thumbnail_path=thumbnail_path,
            file_metadata=metadata or {}
        )
        db.add(db_resource)
        db.commit()
        db.refresh(db_resource)

        # Log the file upload
        file_type_desc = "image" if is_image else "file"
        DatabaseService.create_change_log(
            db=db,
            alert_id=alert_id,
            changed_by=changed_by,
            change_type="file_uploaded",
            description=f"{file_type_desc.title()} uploaded by {uploaded_by}: {filename} ({file_size} bytes)",
            ip_address=ip_address,
            user_agent=user_agent
        )

        return db_resource

    @staticmethod
    def get_file_resources_by_alert_id(db: Session, alert_id: int) -> List[DBFileResource]:
        """Get all file resources for an alert"""
        return db.query(DBFileResource).filter(DBFileResource.alert_id == alert_id).order_by(DBFileResource.uploaded_at).all()

    @staticmethod
    def create_analyst(db: Session, username: str, full_name: str, email: str, role: str, 
                      avatar_url: Optional[str] = None, is_active: bool = True) -> DBAnalyst:
        """Create a new analyst"""
        db_analyst = DBAnalyst(
            username=username,
            full_name=full_name,
            email=email,
            role=role,
            avatar_url=avatar_url,
            is_active=is_active
        )
        db.add(db_analyst)
        db.commit()
        db.refresh(db_analyst)
        return db_analyst

    @staticmethod
    def get_analysts(db: Session) -> List[DBAnalyst]:
        """Get all analysts"""
        return db.query(DBAnalyst).filter(DBAnalyst.is_active == True).order_by(DBAnalyst.full_name).all()

    @staticmethod
    def get_analyst_by_id(db: Session, analyst_id: int) -> Optional[DBAnalyst]:
        """Get analyst by ID"""
        return db.query(DBAnalyst).filter(DBAnalyst.id == analyst_id).first()

    # Conversion methods from DB models to Pydantic models
    @staticmethod
    def db_alert_to_pydantic(db_alert: DBAlert) -> Alert:
        """Convert database alert to Pydantic model"""
        return Alert(
            id=db_alert.id,
            title=db_alert.title,
            description=db_alert.description,
            severity=AlertSeverity(db_alert.severity),
            status=AlertStatus(db_alert.status),
            source=db_alert.source,
            assigned_analyst_id=db_alert.assigned_analyst_id,
            assigned_analyst_name=db_alert.assigned_analyst_name,
            tags=db_alert.tags or [],
            due_date=db_alert.due_date,
            priority=db_alert.priority,
            investigation_notes=db_alert.investigation_notes,
            mitre_techniques=db_alert.mitre_techniques or [],
            escalation_level=db_alert.escalation_level or 0,
            sla_deadline=db_alert.sla_deadline,
            is_deleted=db_alert.is_deleted or False,
            created_at=db_alert.created_at,
            updated_at=db_alert.updated_at
        )

    @staticmethod
    def db_comment_to_pydantic(db_comment: DBComment) -> Comment:
        """Convert database comment to Pydantic model"""
        return Comment(
            id=db_comment.id,
            alert_id=db_comment.alert_id,
            author=db_comment.author,
            content=db_comment.content,
            created_at=db_comment.created_at
        )

    @staticmethod
    def db_ai_finding_to_pydantic(db_finding: DBAIFinding) -> AIFinding:
        """Convert database AI finding to Pydantic model"""
        return AIFinding(
            id=db_finding.id,
            alert_id=db_finding.alert_id,
            type=AIFindingType(db_finding.type),
            title=db_finding.title,
            content=db_finding.content,
            confidence=db_finding.confidence,
            created_at=db_finding.created_at
        )

    @staticmethod
    def db_file_resource_to_pydantic(db_resource: DBFileResource) -> FileResource:
        """Convert database file resource to Pydantic model"""
        # Convert file path to full URL if it's a relative path
        file_path = db_resource.file_path
        if file_path and not file_path.startswith('http'):
            # If it's a relative path like "uploads/3/image_abc123.png", convert to full URL
            if file_path.startswith('uploads/'):
                file_path = f"http://localhost:8001/{file_path}"
            else:
                # Extract the actual filename from the stored path
                import os
                actual_filename = os.path.basename(file_path) if file_path else db_resource.filename
                file_path = f"http://localhost:8001/uploads/{db_resource.alert_id}/{actual_filename}"

        return FileResource(
            id=db_resource.id,
            alert_id=db_resource.alert_id,
            filename=db_resource.filename,
            file_path=file_path,
            file_type=db_resource.file_type,
            file_size=db_resource.file_size,
            is_image=db_resource.is_image,
            thumbnail_path=db_resource.thumbnail_path,
            uploaded_by=db_resource.uploaded_by,
            uploaded_by_id=db_resource.uploaded_by_id or 1,  # Default to 1 if None
            uploaded_at=db_resource.uploaded_at,
            metadata=db_resource.file_metadata or {}
        )

    @staticmethod
    def db_analyst_to_pydantic(db_analyst: DBAnalyst) -> Analyst:
        """Convert database analyst to Pydantic model"""
        return Analyst(
            id=db_analyst.id,
            username=db_analyst.username,
            full_name=db_analyst.full_name,
            email=db_analyst.email,
            avatar_url=db_analyst.avatar_url,
            role=db_analyst.role,
            is_active=db_analyst.is_active
        )

    @staticmethod
    def db_change_log_to_pydantic(db_change_log: DBAlertChangeLog) -> AlertChangeLog:
        """Convert database change log to Pydantic model"""
        return AlertChangeLog(
            id=db_change_log.id,
            alert_id=db_change_log.alert_id,
            changed_by=db_change_log.changed_by,
            changed_at=db_change_log.changed_at,
            change_type=db_change_log.change_type,
            field_name=db_change_log.field_name,
            old_value=db_change_log.old_value,
            new_value=db_change_log.new_value,
            description=db_change_log.description,
            ip_address=db_change_log.ip_address,
            user_agent=db_change_log.user_agent
        )

    # System Logs Methods
    @staticmethod
    def get_system_logs(db: Session, filters: SystemLogFilter) -> tuple[List[DBSystemLog], int]:
        """Get system logs with filtering and pagination"""
        query = db.query(DBSystemLog)

        # Apply filters
        if filters.start_date:
            query = query.filter(DBSystemLog.timestamp >= filters.start_date)
        if filters.end_date:
            query = query.filter(DBSystemLog.timestamp <= filters.end_date)
        if filters.event_types:
            query = query.filter(DBSystemLog.event_type.in_(filters.event_types))
        if filters.user_ids:
            query = query.filter(DBSystemLog.user_id.in_(filters.user_ids))
        if filters.usernames:
            query = query.filter(DBSystemLog.username.in_(filters.usernames))
        if filters.ip_addresses:
            query = query.filter(DBSystemLog.ip_address.in_(filters.ip_addresses))
        if filters.resource_types:
            query = query.filter(DBSystemLog.resource_type.in_(filters.resource_types))
        if filters.resource_ids:
            query = query.filter(DBSystemLog.resource_id.in_(filters.resource_ids))
        if filters.actions:
            query = query.filter(DBSystemLog.action.in_(filters.actions))
        if filters.success is not None:
            query = query.filter(DBSystemLog.success == filters.success)
        if filters.log_levels:
            query = query.filter(DBSystemLog.log_level.in_(filters.log_levels))

        # Search query across text fields
        if filters.search_query:
            search_term = f"%{filters.search_query}%"
            query = query.filter(
                DBSystemLog.event_type.ilike(search_term) |
                DBSystemLog.username.ilike(search_term) |
                DBSystemLog.action.ilike(search_term) |
                DBSystemLog.resource_type.ilike(search_term) |
                DBSystemLog.error_message.ilike(search_term)
            )

        # Get total count before pagination
        total_count = query.count()

        # Apply pagination and ordering
        logs = query.order_by(DBSystemLog.timestamp.desc()).offset(filters.offset).limit(filters.limit).all()

        return logs, total_count

    @staticmethod
    def db_system_log_to_pydantic(db_log: DBSystemLog) -> SystemLog:
        """Convert database system log to Pydantic model"""
        return SystemLog(
            id=db_log.id,
            timestamp=db_log.timestamp,
            event_type=db_log.event_type,
            user_id=db_log.user_id,
            username=db_log.username,
            ip_address=db_log.ip_address,
            user_agent=db_log.user_agent,
            session_id=db_log.session_id,
            resource_type=db_log.resource_type,
            resource_id=db_log.resource_id,
            action=db_log.action,
            details=db_log.details,
            success=db_log.success,
            error_message=db_log.error_message,
            response_time_ms=db_log.response_time_ms,
            request_size_bytes=db_log.request_size_bytes,
            response_size_bytes=db_log.response_size_bytes,
            log_level=db_log.log_level,
            checksum=db_log.checksum
        )

    @staticmethod
    def get_system_log_stats(db: Session) -> dict:
        """Get system log statistics"""
        from sqlalchemy import func, and_
        from datetime import datetime, timedelta

        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_start = today_start - timedelta(days=1)

        # Total logs
        total_logs = db.query(DBSystemLog).count()

        # Logs today
        logs_today = db.query(DBSystemLog).filter(DBSystemLog.timestamp >= today_start).count()

        # Error rate in last 24 hours
        total_24h = db.query(DBSystemLog).filter(DBSystemLog.timestamp >= yesterday_start).count()
        errors_24h = db.query(DBSystemLog).filter(
            and_(DBSystemLog.timestamp >= yesterday_start, DBSystemLog.success == False)
        ).count()
        error_rate_24h = (errors_24h / total_24h * 100) if total_24h > 0 else 0.0

        # Top event types
        top_event_types = db.query(
            DBSystemLog.event_type,
            func.count(DBSystemLog.id).label('count')
        ).group_by(DBSystemLog.event_type).order_by(func.count(DBSystemLog.id).desc()).limit(10).all()

        # Top users
        top_users = db.query(
            DBSystemLog.username,
            func.count(DBSystemLog.id).label('count')
        ).filter(DBSystemLog.username.isnot(None)).group_by(DBSystemLog.username).order_by(func.count(DBSystemLog.id).desc()).limit(10).all()

        # Top IP addresses
        top_ips = db.query(
            DBSystemLog.ip_address,
            func.count(DBSystemLog.id).label('count')
        ).filter(DBSystemLog.ip_address.isnot(None)).group_by(DBSystemLog.ip_address).order_by(func.count(DBSystemLog.id).desc()).limit(10).all()

        # Average response time
        avg_response_time = db.query(func.avg(DBSystemLog.response_time_ms)).filter(
            DBSystemLog.response_time_ms.isnot(None)
        ).scalar()

        return {
            'total_logs': total_logs,
            'logs_today': logs_today,
            'error_rate_24h': round(error_rate_24h, 2),
            'top_event_types': [{'event_type': et[0], 'count': et[1]} for et in top_event_types],
            'top_users': [{'username': u[0], 'count': u[1]} for u in top_users],
            'top_ip_addresses': [{'ip_address': ip[0], 'count': ip[1]} for ip in top_ips],
            'avg_response_time_ms': round(avg_response_time, 2) if avg_response_time else None
        }

    @staticmethod
    def get_unique_log_values(db: Session, field: str) -> List[str]:
        """Get unique values for a specific log field for filtering"""
        if field == 'event_types':
            results = db.query(DBSystemLog.event_type).distinct().all()
        elif field == 'usernames':
            results = db.query(DBSystemLog.username).filter(DBSystemLog.username.isnot(None)).distinct().all()
        elif field == 'ip_addresses':
            results = db.query(DBSystemLog.ip_address).filter(DBSystemLog.ip_address.isnot(None)).distinct().all()
        elif field == 'resource_types':
            results = db.query(DBSystemLog.resource_type).filter(DBSystemLog.resource_type.isnot(None)).distinct().all()
        elif field == 'actions':
            results = db.query(DBSystemLog.action).distinct().all()
        elif field == 'log_levels':
            results = db.query(DBSystemLog.log_level).distinct().all()
        else:
            return []

        return [r[0] for r in results if r[0] is not None]

    @staticmethod
    def export_system_logs(db: Session, filters: SystemLogFilter, format: str = 'json') -> str:
        """Export system logs in specified format"""
        logs, _ = DatabaseService.get_system_logs(db, filters)

        if format.lower() == 'json':
            import json
            log_data = [DatabaseService.db_system_log_to_pydantic(log).model_dump() for log in logs]
            return json.dumps(log_data, indent=2, default=str)

        elif format.lower() == 'csv':
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow([
                'id', 'timestamp', 'event_type', 'user_id', 'username', 'ip_address',
                'session_id', 'resource_type', 'resource_id', 'action', 'success',
                'error_message', 'response_time_ms', 'log_level'
            ])

            # Write data
            for log in logs:
                writer.writerow([
                    log.id, log.timestamp, log.event_type, log.user_id, log.username,
                    log.ip_address, log.session_id, log.resource_type, log.resource_id,
                    log.action, log.success, log.error_message, log.response_time_ms, log.log_level
                ])

            return output.getvalue()

        else:
            raise ValueError(f"Unsupported export format: {format}")

    # ===== CHAT SESSION METHODS =====

    @staticmethod
    def create_chat_session(db: Session, user_id: str, alert_id: Optional[int] = None,
                           title: Optional[str] = None, is_incognito: bool = False) -> DBChatSession:
        """Create a new chat session"""
        # Auto-generate title if not provided
        if not title:
            if alert_id:
                alert = DatabaseService.get_alert_by_id(db, alert_id)
                title = f"Chat about Alert #{alert_id}: {alert.title[:50]}..." if alert else f"Alert #{alert_id} Chat"
            else:
                title = f"General Chat - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        db_session = DBChatSession(
            user_id=user_id,
            alert_id=alert_id,
            title=title,
            is_incognito=is_incognito
        )
        db.add(db_session)
        db.commit()
        db.refresh(db_session)
        return db_session

    @staticmethod
    def get_chat_session_by_id(db: Session, session_id: int) -> Optional[DBChatSession]:
        """Get chat session by ID"""
        return db.query(DBChatSession).filter(DBChatSession.id == session_id).first()

    @staticmethod
    def get_user_chat_sessions(db: Session, user_id: str, include_incognito: bool = False,
                              alert_id: Optional[int] = None) -> List[DBChatSession]:
        """Get all chat sessions for a user"""
        query = db.query(DBChatSession).filter(DBChatSession.user_id == user_id)

        if not include_incognito:
            query = query.filter(DBChatSession.is_incognito == False)

        if alert_id is not None:
            query = query.filter(DBChatSession.alert_id == alert_id)

        return query.order_by(DBChatSession.updated_at.desc()).all()

    @staticmethod
    def end_chat_session(db: Session, session_id: int) -> bool:
        """End a chat session"""
        db_session = DatabaseService.get_chat_session_by_id(db, session_id)
        if db_session:
            db_session.is_active = False
            db_session.ended_at = datetime.now()
            db.commit()
            return True
        return False

    @staticmethod
    def delete_chat_session(db: Session, session_id: int) -> bool:
        """Delete a chat session and all its messages"""
        db_session = DatabaseService.get_chat_session_by_id(db, session_id)
        if db_session:
            db.delete(db_session)
            db.commit()
            return True
        return False

    # ===== CHAT MESSAGE METHODS =====

    @staticmethod
    def create_chat_message(db: Session, session_id: int, user_message: str, ai_response: str,
                           response_time_ms: Optional[int] = None, context_data: Optional[dict] = None) -> DBChatMessage:
        """Create a new chat message"""
        # Get current message count for ordering
        message_count = db.query(DBChatMessage).filter(DBChatMessage.session_id == session_id).count()

        db_message = DBChatMessage(
            session_id=session_id,
            user_message=user_message,
            ai_response=ai_response,
            message_order=message_count + 1,
            response_time_ms=response_time_ms,
            context_data=context_data or {}
        )
        db.add(db_message)

        # Update session's updated_at timestamp
        db_session = DatabaseService.get_chat_session_by_id(db, session_id)
        if db_session:
            db_session.updated_at = datetime.now()

        db.commit()
        db.refresh(db_message)
        return db_message

    @staticmethod
    def get_chat_messages_by_session(db: Session, session_id: int) -> List[DBChatMessage]:
        """Get all messages for a chat session"""
        return db.query(DBChatMessage).filter(
            DBChatMessage.session_id == session_id
        ).order_by(DBChatMessage.message_order).all()

    @staticmethod
    def get_recent_chat_messages(db: Session, session_id: int, limit: int = 10) -> List[DBChatMessage]:
        """Get recent messages for context (for AI)"""
        return db.query(DBChatMessage).filter(
            DBChatMessage.session_id == session_id
        ).order_by(DBChatMessage.message_order.desc()).limit(limit).all()

    @staticmethod
    def update_chat_message(db: Session, message_id: int, new_user_message: str) -> Optional[DBChatMessage]:
        """Update a chat message's user message content"""
        db_message = db.query(DBChatMessage).filter(DBChatMessage.id == message_id).first()
        if db_message:
            db_message.user_message = new_user_message
            db.commit()
            db.refresh(db_message)
            return db_message
        return None

    @staticmethod
    def delete_messages_after_order(db: Session, session_id: int, message_order: int) -> bool:
        """Delete all messages in a session after a specific message order"""
        try:
            db.query(DBChatMessage).filter(
                DBChatMessage.session_id == session_id,
                DBChatMessage.message_order > message_order
            ).delete()
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error deleting messages after order {message_order}: {e}")
            return False

    @staticmethod
    def get_chat_message_by_id(db: Session, message_id: int) -> Optional[DBChatMessage]:
        """Get a specific chat message by ID"""
        return db.query(DBChatMessage).filter(DBChatMessage.id == message_id).first()

    # ===== CONVERSION METHODS =====

    @staticmethod
    def db_chat_session_to_pydantic(db_session: DBChatSession) -> ChatSession:
        """Convert database chat session to Pydantic model"""
        # Count messages in session
        message_count = len(db_session.messages) if db_session.messages else 0

        return ChatSession(
            id=db_session.id,
            user_id=db_session.user_id,
            alert_id=db_session.alert_id,
            title=db_session.title,
            is_incognito=db_session.is_incognito,
            created_at=db_session.created_at,
            updated_at=db_session.updated_at,
            ended_at=db_session.ended_at,
            is_active=db_session.is_active,
            message_count=message_count
        )

    @staticmethod
    def db_chat_message_to_pydantic(db_message: DBChatMessage) -> ChatMessage:
        """Convert database chat message to Pydantic model"""
        return ChatMessage(
            id=db_message.id,
            session_id=db_message.session_id,
            user_message=db_message.user_message,
            ai_response=db_message.ai_response,
            message_order=db_message.message_order,
            created_at=db_message.created_at,
            response_time_ms=db_message.response_time_ms,
            context_data=db_message.context_data or {}
        )

    # ===== WEBHOOK SOURCE METHODS =====

    @staticmethod
    def create_webhook_source(db: Session, name: str, webhook_secret: str, auth_type: str,
                                  json_mapping: dict, description: Optional[str] = None,
                                  auth_header_name: Optional[str] = None, auth_header_value: Optional[str] = None,
                                  auth_bearer_token: Optional[str] = None) -> DBWebhookSource:
        """Create a new webhook source"""
        db_source = DBWebhookSource(
            name=name,
            description=description,
            webhook_secret=webhook_secret,
            auth_type=auth_type,
            auth_header_name=auth_header_name,
            auth_header_value=auth_header_value,
            auth_bearer_token=auth_bearer_token,
            json_mapping=json_mapping
        )
        db.add(db_source)
        db.commit()
        db.refresh(db_source)
        return db_source

    @staticmethod
    def get_webhook_sources(db: Session) -> List[DBWebhookSource]:
        """Get all webhook sources"""
        return db.query(DBWebhookSource).order_by(DBWebhookSource.created_at.desc()).all()

    @staticmethod
    def get_webhook_source_by_id(db: Session, source_id: int) -> Optional[DBWebhookSource]:
        """Get webhook source by ID"""
        return db.query(DBWebhookSource).filter(DBWebhookSource.id == source_id).first()

    @staticmethod
    def get_webhook_source_by_secret(db: Session, webhook_secret: str) -> Optional[DBWebhookSource]:
        """Get webhook source by secret"""
        return db.query(DBWebhookSource).filter(DBWebhookSource.webhook_secret == webhook_secret).first()

    @staticmethod
    def update_webhook_source(db: Session, source_id: int, update_data: WebhookSourceUpdate) -> Optional[DBWebhookSource]:
        """Update webhook source"""
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if not db_source:
            return None

        # Update fields if provided
        if update_data.name is not None:
            db_source.name = update_data.name
        if update_data.description is not None:
            db_source.description = update_data.description
        if update_data.auth_type is not None:
            db_source.auth_type = update_data.auth_type.value
        if update_data.auth_header_name is not None:
            db_source.auth_header_name = update_data.auth_header_name
        if update_data.auth_header_value is not None:
            db_source.auth_header_value = update_data.auth_header_value
        if update_data.auth_bearer_token is not None:
            db_source.auth_bearer_token = update_data.auth_bearer_token
        if update_data.json_mapping is not None:
            db_source.json_mapping = update_data.json_mapping
        if update_data.is_active is not None:
            db_source.is_active = update_data.is_active

        db_source.updated_at = datetime.now()
        db.commit()
        db.refresh(db_source)
        return db_source

    @staticmethod
    def delete_webhook_source(db: Session, source_id: int) -> bool:
        """Delete webhook source"""
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if db_source:
            db.delete(db_source)
            db.commit()
            return True
        return False

    @staticmethod
    def create_webhook_alert(db: Session, source_id: int, alert_id: int, raw_payload: dict) -> DBWebhookAlert:
        """Create a webhook alert record"""
        db_webhook_alert = DBWebhookAlert(
            source_id=source_id,
            alert_id=alert_id,
            raw_payload=raw_payload
        )
        db.add(db_webhook_alert)
        db.commit()
        db.refresh(db_webhook_alert)
        return db_webhook_alert

    @staticmethod
    def update_webhook_source_stats(db: Session, source_id: int):
        """Update webhook source usage statistics"""
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if db_source:
            db_source.alert_count += 1
            db_source.last_used_at = datetime.now()
            db.commit()

    @staticmethod
    def db_webhook_source_to_pydantic(db_source: DBWebhookSource) -> WebhookSource:
        """Convert database webhook source to Pydantic model"""
        return WebhookSource(
            id=db_source.id,
            name=db_source.name,
            description=db_source.description,
            webhook_url=f"http://localhost:8001/api/webhooks/{db_source.webhook_secret}",
            webhook_secret=db_source.webhook_secret,
            auth_type=AuthenticationType(db_source.auth_type),
            auth_header_name=db_source.auth_header_name,
            auth_header_value=db_source.auth_header_value,
            auth_bearer_token=db_source.auth_bearer_token,
            json_mapping=db_source.json_mapping,
            is_active=db_source.is_active,
            created_at=db_source.created_at,
            updated_at=db_source.updated_at,
            last_used_at=db_source.last_used_at,
            alert_count=db_source.alert_count
        )
