#!/usr/bin/env python3
"""
Database migration script to add new alert management fields
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """Add new columns to the alerts table for enhanced alert management"""
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'alertai.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting database migration...")
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(alerts)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # Create alert_change_logs table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS alert_change_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_id INTEGER NOT NULL,
                changed_by TEXT NOT NULL,
                changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                change_type TEXT NOT NULL,
                field_name TEXT,
                old_value TEXT,
                new_value TEXT,
                description TEXT,
                ip_address TEXT,
                user_agent TEXT,
                FOREIGN KEY (alert_id) REFERENCES alerts (id)
            )
        """)
        print("✅ Created alert_change_logs table")

        # Create system_logs table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                user_id TEXT,
                username TEXT,
                ip_address TEXT,
                user_agent TEXT,
                session_id TEXT,
                resource_type TEXT,
                resource_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                success BOOLEAN DEFAULT 1,
                error_message TEXT,
                response_time_ms INTEGER,
                request_size_bytes INTEGER,
                response_size_bytes INTEGER,
                log_level TEXT DEFAULT 'INFO',
                checksum TEXT
            )
        """)
        print("✅ Created system_logs table")

        # Create indexes for system_logs for better query performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_event_type ON system_logs(event_type)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_username ON system_logs(username)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_ip_address ON system_logs(ip_address)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_session_id ON system_logs(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_resource_type ON system_logs(resource_type)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_resource_id ON system_logs(resource_id)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_action ON system_logs(action)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_success ON system_logs(success)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_log_level ON system_logs(log_level)"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)
        print("✅ Created system_logs indexes")

        # List of new columns to add to alerts table
        new_columns = [
            ('assigned_analyst_id', 'INTEGER'),
            ('assigned_analyst_name', 'TEXT'),
            ('tags', 'TEXT'),  # JSON array as TEXT
            ('due_date', 'DATETIME'),
            ('priority', 'INTEGER'),
            ('investigation_notes', 'TEXT'),
            ('mitre_techniques', 'TEXT'),  # JSON array as TEXT
            ('escalation_level', 'INTEGER DEFAULT 0'),
            ('sla_deadline', 'DATETIME'),
            ('is_deleted', 'BOOLEAN DEFAULT 0')
        ]
        
        # Add missing columns
        columns_added = 0
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE alerts ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    print(f"✅ Added column: {column_name}")
                    columns_added += 1
                except sqlite3.Error as e:
                    print(f"❌ Error adding column {column_name}: {e}")
                    return False
            else:
                print(f"⏭️  Column {column_name} already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify the migration
        cursor.execute("PRAGMA table_info(alerts)")
        final_columns = [column[1] for column in cursor.fetchall()]
        
        print(f"\n📊 Migration Summary:")
        print(f"   • Columns added: {columns_added}")
        print(f"   • Total columns: {len(final_columns)}")
        print(f"   • New columns: {', '.join([col for col, _ in new_columns if col in final_columns])}")
        
        # Update existing alerts with default values
        if columns_added > 0:
            print("\n🔄 Setting default values for existing alerts...")
            
            # Set default values for new columns
            updates = [
                ("priority", "3"),  # Medium priority
                ("escalation_level", "0"),  # No escalation
                ("is_deleted", "0"),  # Not deleted
                ("tags", "'[]'"),  # Empty JSON array
                ("mitre_techniques", "'[]'")  # Empty JSON array
            ]
            
            for column, default_value in updates:
                if column in [col for col, _ in new_columns]:
                    try:
                        cursor.execute(f"UPDATE alerts SET {column} = {default_value} WHERE {column} IS NULL")
                        updated_rows = cursor.rowcount
                        if updated_rows > 0:
                            print(f"   • Updated {updated_rows} rows for {column}")
                    except sqlite3.Error as e:
                        print(f"   ❌ Error updating {column}: {e}")
            
            conn.commit()
        
        # Close connection
        conn.close()
        
        print(f"\n✅ Database migration completed successfully!")
        print(f"   Database: {db_path}")
        print(f"   Timestamp: {datetime.now().isoformat()}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def verify_migration():
    """Verify that the migration was successful"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'alertai.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(alerts)")
        columns = cursor.fetchall()
        
        print("\n📋 Current alerts table structure:")
        for column in columns:
            print(f"   • {column[1]} ({column[2]})")
        
        # Check sample data
        cursor.execute("SELECT COUNT(*) FROM alerts")
        alert_count = cursor.fetchone()[0]
        
        if alert_count > 0:
            cursor.execute("SELECT id, title, priority, escalation_level, is_deleted FROM alerts LIMIT 3")
            sample_alerts = cursor.fetchall()
            
            print(f"\n📊 Sample data ({alert_count} total alerts):")
            for alert in sample_alerts:
                print(f"   • Alert #{alert[0]}: {alert[1][:50]}... (Priority: {alert[2]}, Escalation: {alert[3]}, Deleted: {alert[4]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AlertAI Database Migration Tool")
    print("=" * 50)
    
    # Run migration
    success = migrate_database()
    
    if success:
        # Verify migration
        verify_migration()
        print("\n🎉 Migration completed successfully!")
        print("   You can now start the backend server.")
    else:
        print("\n💥 Migration failed!")
        print("   Please check the errors above and try again.")
