#!/usr/bin/env python3
"""
Database migration script to create webhook integration tables
"""

from sqlalchemy import create_engine, text
from database import Base, engine
import os

def create_webhook_tables():
    """Create webhook integration tables"""
    
    # Create webhook_integrations table
    webhook_integrations_sql = """
    CREATE TABLE IF NOT EXISTS webhook_integrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        webhook_secret VARCHAR(64) UNIQUE NOT NULL,
        auth_type VARCHAR(20) NOT NULL,
        auth_header_name VARCHAR(100),
        auth_header_value TEXT,
        auth_bearer_token TEXT,
        json_mapping JSON NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP,
        last_used_at TIMESTAMP,
        alert_count INTEGER DEFAULT 0
    );
    """
    
    # Create webhook_alerts table
    webhook_alerts_sql = """
    CREATE TABLE IF NOT EXISTS webhook_alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        integration_id INTEGER NOT NULL,
        alert_id INTEGER NOT NULL,
        raw_payload JSON NOT NULL,
        processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (integration_id) REFERENCES webhook_integrations (id) ON DELETE CASCADE,
        FOREIGN KEY (alert_id) REFERENCES alerts (id) ON DELETE CASCADE
    );
    """
    
    # Create indexes
    indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_webhook_integrations_secret ON webhook_integrations(webhook_secret);",
        "CREATE INDEX IF NOT EXISTS idx_webhook_integrations_active ON webhook_integrations(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_webhook_alerts_integration ON webhook_alerts(integration_id);",
        "CREATE INDEX IF NOT EXISTS idx_webhook_alerts_alert ON webhook_alerts(alert_id);",
        "CREATE INDEX IF NOT EXISTS idx_webhook_alerts_processed ON webhook_alerts(processed_at);"
    ]
    
    try:
        with engine.connect() as connection:
            # Create tables
            print("Creating webhook_integrations table...")
            connection.execute(text(webhook_integrations_sql))
            
            print("Creating webhook_alerts table...")
            connection.execute(text(webhook_alerts_sql))
            
            # Create indexes
            print("Creating indexes...")
            for index_sql in indexes_sql:
                connection.execute(text(index_sql))
            
            connection.commit()
            print("✅ Webhook integration tables created successfully!")
            
    except Exception as e:
        print(f"❌ Error creating webhook tables: {e}")
        raise

if __name__ == "__main__":
    create_webhook_tables()
