#!/usr/bin/env python3
"""
Create security alerts with malicious IPs for testing VirusTotal integration
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db, DBAlert
from db_service import DatabaseService
from models import AlertSeverity, AlertStatus

# Known malicious IPs (mix of Tor exit nodes and other malicious IPs)
MALICIOUS_IPS = [
    "***************",  # Known Tor exit node
    "**************",   # Known Tor exit node  
    "*************",    # Known Tor exit node
    "**************",   # Known malicious IP
    "**************",   # Suspicious IP from earlier testing
    "*************",    # Known Tor exit node
    "*************",    # Known Tor exit node
    "*************",    # Known Tor exit node
]

# Security alert templates with malicious IPs
SECURITY_ALERTS = [
    {
        "title": "Suspicious Tor Exit Node Connection Detected",
        "description": "Multiple connection attempts detected from known Tor exit node {ip}. This IP has been flagged for suspicious activity including potential data exfiltration attempts. The connection was attempting to access sensitive internal resources without proper authentication.",
        "severity": AlertSeverity.HIGH,
        "source": "Network Security Monitor",
        "tags": ["tor", "suspicious-connection", "network-security", "data-exfiltration"],
        "mitre_techniques": ["T1090.003", "T1071.001"],  # Proxy: Multi-hop Proxy, Application Layer Protocol: Web Protocols
        "priority": 4,
        "escalation_level": 2
    },
    {
        "title": "Malicious IP Brute Force Attack Detected",
        "description": "Brute force attack detected from malicious IP {ip}. Over 500 failed login attempts within 10 minutes targeting multiple user accounts. The attack pattern suggests automated credential stuffing using compromised credentials. Source IP is known to be associated with cybercriminal activities.",
        "severity": AlertSeverity.CRITICAL,
        "source": "Authentication System",
        "tags": ["brute-force", "credential-stuffing", "malicious-ip", "authentication"],
        "mitre_techniques": ["T1110.001", "T1110.004"],  # Brute Force: Password Guessing, Credential Stuffing
        "priority": 5,
        "escalation_level": 3
    },
    {
        "title": "Command and Control Communication Detected",
        "description": "Outbound communication detected to known malicious IP {ip}. The traffic pattern suggests potential command and control (C2) communication from an internal host. Encrypted traffic observed with suspicious timing patterns consistent with malware beaconing behavior.",
        "severity": AlertSeverity.CRITICAL,
        "source": "Network Traffic Analysis",
        "tags": ["c2", "malware", "outbound-traffic", "encrypted-communication"],
        "mitre_techniques": ["T1071.001", "T1573.001", "T1095"],  # Application Layer Protocol, Encrypted Channel, Non-Application Layer Protocol
        "priority": 5,
        "escalation_level": 3
    },
    {
        "title": "Data Exfiltration Attempt via Malicious IP",
        "description": "Large volume data transfer detected to suspicious IP {ip}. Approximately 2.3GB of data was transmitted in encrypted form during off-hours. The destination IP has been flagged by threat intelligence feeds as associated with data theft operations and APT groups.",
        "severity": AlertSeverity.HIGH,
        "source": "Data Loss Prevention",
        "tags": ["data-exfiltration", "large-transfer", "apt", "threat-intelligence"],
        "mitre_techniques": ["T1041", "T1020", "T1030"],  # Exfiltration Over C2 Channel, Automated Exfiltration, Data Transfer Size Limits
        "priority": 4,
        "escalation_level": 2
    },
    {
        "title": "Malware Callback to Known Bad IP",
        "description": "Endpoint security detected malware attempting to communicate with known malicious IP {ip}. The malware sample appears to be a banking trojan variant attempting to establish persistence and download additional payloads. The destination IP is listed in multiple threat intelligence feeds as hosting malware infrastructure.",
        "severity": AlertSeverity.HIGH,
        "source": "Endpoint Detection and Response",
        "tags": ["malware", "banking-trojan", "callback", "persistence"],
        "mitre_techniques": ["T1071.001", "T1105", "T1547.001"],  # Application Layer Protocol, Ingress Tool Transfer, Boot or Logon Autostart Execution
        "priority": 4,
        "escalation_level": 2
    }
]

def create_malicious_ip_alerts():
    """Create 5 security alerts with malicious IPs"""
    
    print("🚨 Creating security alerts with malicious IPs...")
    
    # Get database session
    db = next(get_db())
    
    try:
        created_alerts = []
        
        for i, alert_template in enumerate(SECURITY_ALERTS):
            # Select a random malicious IP
            malicious_ip = random.choice(MALICIOUS_IPS)
            
            # Format the description with the IP
            description = alert_template["description"].format(ip=malicious_ip)
            
            # Add IP to the description for better IOC extraction
            description += f"\n\nIOCs:\n- Source IP: {malicious_ip}\n- Threat Level: High\n- Confidence: 95%"
            
            # Create the alert
            alert = DatabaseService.create_alert(
                db=db,
                title=alert_template["title"],
                description=description,
                severity=alert_template["severity"].value,
                status=AlertStatus.OPEN.value,
                source=alert_template["source"]
            )

            # Update additional fields manually since the simple create_alert doesn't support them
            alert.tags = alert_template["tags"]
            alert.mitre_techniques = alert_template["mitre_techniques"]
            alert.priority = alert_template["priority"]
            alert.escalation_level = alert_template["escalation_level"]
            
            # Set realistic timestamps (alerts from last 24 hours)
            hours_ago = random.randint(1, 24)
            minutes_ago = random.randint(0, 59)
            alert.created_at = datetime.now() - timedelta(hours=hours_ago, minutes=minutes_ago)
            
            # Some alerts might have been updated
            if random.choice([True, False]):
                alert.updated_at = alert.created_at + timedelta(minutes=random.randint(5, 60))
            
            db.commit()
            created_alerts.append(alert)
            
            print(f"✅ Created alert #{alert.id}: {alert.title}")
            print(f"   📍 Malicious IP: {malicious_ip}")
            print(f"   🚨 Severity: {alert.severity}")
            print(f"   📊 Priority: {alert.priority}")
            print()
        
        print(f"🎉 Successfully created {len(created_alerts)} security alerts with malicious IPs!")
        print("\n📋 Summary:")
        for alert in created_alerts:
            print(f"   Alert #{alert.id}: {alert.title} ({alert.severity})")
        
        print("\n🔧 These alerts contain real malicious IPs that can be tested with VirusTotal!")
        print("💡 Try asking Bitzy to investigate these IPs using VirusTotal to see real threat intelligence data.")
        
    except Exception as e:
        print(f"❌ Error creating alerts: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_malicious_ip_alerts()
