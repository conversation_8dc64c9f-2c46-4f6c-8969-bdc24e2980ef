#!/usr/bin/env python3
"""
Simple server startup script for AlertAI
"""

import sys
import os
import uvicorn

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 Starting AlertAI Server with Authentication...")
    print("=" * 50)
    
    try:
        # Test basic imports first
        print("Testing imports...")
        
        # Test configuration
        from app.core.config import settings
        print("✅ Configuration loaded")
        
        # Test authentication
        from app.core.auth import AuthService
        print("✅ Authentication system ready")
        
        # Test main app
        from app.main import create_application
        app = create_application()
        print("✅ Application created")
        
        print("\n🎉 All systems ready!")
        print(f"Starting server on http://localhost:8002")
        print("Available endpoints:")
        print("  - POST /api/auth/init-system - Initialize system")
        print("  - POST /api/auth/register - Register user")
        print("  - POST /api/auth/token - Login")
        print("  - GET /api/auth/me - Get user info")
        print("  - GET /api/alerts - Get alerts (requires auth)")
        print("  - GET /docs - API documentation")
        print("\n" + "=" * 50)
        
        # Start the server
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8002,
            reload=True,
            log_level="info"
        )
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
