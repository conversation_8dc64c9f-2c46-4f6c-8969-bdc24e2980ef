#!/usr/bin/env python3
"""
Test script for VirusTotal tool integration

This script tests the VirusTotal tool functionality and integration
with the AlertAI system.
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.tools.virustotal import VirusTotalTool
from app.tools.manager import ToolManager, get_tool_manager


def test_virustotal_tool_basic():
    """Test basic VirusTotal tool functionality"""
    print("🧪 Testing VirusTotal Tool - Basic Functionality")
    print("=" * 50)
    
    # Initialize tool
    vt_tool = VirusTotalTool()
    
    # Test with known clean hash (empty file MD5)
    test_hash = "d41d8cd98f00b204e9800998ecf8427e"
    print(f"Testing with clean hash: {test_hash}")
    
    try:
        result = vt_tool._run(test_hash)
        result_data = json.loads(result)
        
        print(f"✅ Tool executed successfully")
        print(f"Success: {result_data.get('success')}")
        print(f"Message: {result_data.get('message')}")
        
        if result_data.get('data'):
            summary = result_data['data'].get('summary', {})
            print(f"Total IOCs: {summary.get('total_iocs', 0)}")
            print(f"Clean IOCs: {summary.get('clean_iocs', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool test failed: {str(e)}")
        return False


def test_virustotal_tool_multiple_iocs():
    """Test VirusTotal tool with multiple IOCs"""
    print("\n🧪 Testing VirusTotal Tool - Multiple IOCs")
    print("=" * 50)
    
    vt_tool = VirusTotalTool()
    
    # Test with multiple IOCs
    test_input = """
    Check these IOCs:
    - IP: *******
    - Domain: google.com
    - Hash: d41d8cd98f00b204e9800998ecf8427e
    """
    
    try:
        result = vt_tool._run(test_input)
        result_data = json.loads(result)
        
        print(f"✅ Multiple IOCs test completed")
        print(f"Success: {result_data.get('success')}")
        
        if result_data.get('data'):
            results = result_data['data'].get('results', {})
            for ioc_type, ioc_results in results.items():
                if ioc_results:
                    print(f"Found {len(ioc_results)} {ioc_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multiple IOCs test failed: {str(e)}")
        return False


def test_tool_manager():
    """Test the tool manager functionality"""
    print("\n🧪 Testing Tool Manager")
    print("=" * 50)
    
    try:
        # Initialize tool manager
        manager = get_tool_manager()
        
        # Check available tools
        tools = manager.get_all_tools()
        print(f"✅ Tool manager initialized")
        print(f"Available tools: {len(tools)}")
        
        # Check tool status
        status = manager.get_tool_status()
        for tool_name, tool_status in status.items():
            print(f"- {tool_name}: {'✅' if tool_status['available'] else '❌'} Available")
            if tool_name == "virustotal":
                print(f"  API Key Configured: {'✅' if tool_status['api_key_configured'] else '❌'}")
        
        # Test VirusTotal tool
        vt_tool = manager.get_tool("virustotal")
        if vt_tool:
            print(f"✅ VirusTotal tool retrieved from manager")
            
            # Test the tool
            test_result = manager.test_tool("virustotal")
            print(f"Tool test result: {'✅' if test_result['success'] else '❌'}")
            
        return True
        
    except Exception as e:
        print(f"❌ Tool manager test failed: {str(e)}")
        return False


def test_ioc_extraction():
    """Test IOC extraction functionality"""
    print("\n🧪 Testing IOC Extraction")
    print("=" * 50)
    
    vt_tool = VirusTotalTool()
    
    test_text = """
    Alert: Suspicious network activity detected
    Source IP: ************* connected to malicious-domain.com
    File hash: a1b2c3d4e5f6789012345678901234567890123456789012345678901234
    URL accessed: https://suspicious-site.com/malware.exe
    Email: <EMAIL>
    """
    
    try:
        iocs = vt_tool._extract_and_classify_iocs(test_text)
        
        print("✅ IOC extraction completed")
        for ioc_type, ioc_list in iocs.items():
            if ioc_list:
                print(f"- {ioc_type}: {len(ioc_list)} found")
                for ioc in ioc_list[:3]:  # Show first 3
                    print(f"  • {ioc}")
        
        return True
        
    except Exception as e:
        print(f"❌ IOC extraction test failed: {str(e)}")
        return False


async def test_ollama_integration():
    """Test integration with Ollama service"""
    print("\n🧪 Testing Ollama Integration")
    print("=" * 50)
    
    try:
        # Import Ollama service
        from ollama_service import OllamaService
        
        # Create service instance
        ollama_service = OllamaService()
        
        print(f"✅ Ollama service initialized")
        print(f"Model: {ollama_service.model_name}")
        print(f"Tools enabled: {ollama_service.tools_enabled}")
        
        # Test IOC checking
        test_text = "Check this IP: ******* and hash: d41d8cd98f00b204e9800998ecf8427e"
        tool_results = await ollama_service._check_iocs_with_tools(test_text)
        
        if tool_results:
            print("✅ IOC checking with tools successful")
            print("Tool results preview:")
            print(tool_results[:200] + "..." if len(tool_results) > 200 else tool_results)
        else:
            print("ℹ️ No tool results (may be expected if API key not configured)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama integration test failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("🚀 AlertAI VirusTotal Tool Test Suite")
    print("=" * 60)
    
    # Check environment
    api_key = os.getenv("VIRUSTOTAL_API_KEY")
    if api_key:
        print(f"✅ VirusTotal API key configured")
    else:
        print(f"⚠️ VirusTotal API key not configured - will use mock data")
    
    print()
    
    # Run tests
    tests = [
        ("Basic Tool Functionality", test_virustotal_tool_basic),
        ("Multiple IOCs", test_virustotal_tool_multiple_iocs),
        ("Tool Manager", test_tool_manager),
        ("IOC Extraction", test_ioc_extraction),
        ("Ollama Integration", lambda: asyncio.run(test_ollama_integration())),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 Test Results Summary")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! VirusTotal tool is ready to use.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    # Usage instructions
    print("\n" + "=" * 60)
    print("📖 Usage Instructions")
    print("=" * 60)
    print("""
To use the VirusTotal tool with AlertAI:

1. **Configure API Key** (optional, will use mock data if not set):
   export VIRUSTOTAL_API_KEY="your-api-key-here"

2. **Start AlertAI backend**:
   cd backend && python start_server.py

3. **Chat with Bitzy** about alerts containing IOCs:
   - The AI will automatically detect IOCs in alert data
   - VirusTotal lookups will be performed automatically
   - Results will be included in the AI response

4. **Example alert data that will trigger VirusTotal lookups**:
   - IP addresses: *************, *******
   - Domains: suspicious-domain.com
   - File hashes: a1b2c3d4e5f6789...
   - URLs: https://malicious-site.com/malware.exe

The tool integrates seamlessly with the existing AI chat functionality!
""")


if __name__ == "__main__":
    main()
