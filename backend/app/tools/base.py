"""
Base classes for AlertAI tools following LangChain best practices.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union, List
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class ToolResult(BaseModel):
    """Standardized tool result format."""
    
    success: bool = Field(description="Whether the tool execution was successful")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Tool result data")
    message: str = Field(description="Human-readable message about the result")
    error: Optional[str] = Field(default=None, description="Error message if execution failed")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class ToolError(Exception):
    """Custom exception for tool errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)


class BaseTool(ABC):
    """
    Base class for AlertAI tools.

    This class provides a standardized interface for all AlertAI tools
    with proper error handling, logging, and result formatting.
    """

    # Tool metadata
    name: str = ""
    description: str = ""
    return_direct: bool = False
    handle_tool_error: bool = True

    def _run(
        self,
        query: str,
        run_manager: Optional[Any] = None,
    ) -> str:
        """
        Execute the tool with proper error handling and logging.
        
        Args:
            query: The input query for the tool
            run_manager: Optional callback manager for tool run
            
        Returns:
            JSON string containing the tool result
        """
        try:
            logger.info(f"Executing {self.name} with query: {query}")
            
            # Execute the tool-specific logic
            result = self._execute(query, run_manager)
            
            # Ensure result is a ToolResult instance
            if not isinstance(result, ToolResult):
                result = ToolResult(
                    success=True,
                    data={"result": result} if result else None,
                    message="Tool executed successfully"
                )
            
            logger.info(f"{self.name} executed successfully")
            return result.model_dump_json()
            
        except ToolError as e:
            logger.error(f"{self.name} tool error: {e.message}", extra={"details": e.details})
            error_result = ToolResult(
                success=False,
                message=f"Tool error: {e.message}",
                error=e.error_code or "TOOL_ERROR",
                metadata={"details": e.details}
            )
            return error_result.model_dump_json()
            
        except Exception as e:
            logger.error(f"Unexpected error in {self.name}: {str(e)}", exc_info=True)
            error_result = ToolResult(
                success=False,
                message=f"Unexpected error: {str(e)}",
                error="UNEXPECTED_ERROR"
            )
            return error_result.model_dump_json()
    
    @abstractmethod
    def _execute(
        self,
        query: str,
        run_manager: Optional[Any] = None,
    ) -> Union[ToolResult, Any]:
        """
        Execute the tool-specific logic.
        
        Args:
            query: The input query for the tool
            run_manager: Optional callback manager for tool run
            
        Returns:
            ToolResult or any data that will be wrapped in ToolResult
        """
        pass
    
    def _validate_input(self, query: str) -> None:
        """
        Validate tool input.
        
        Args:
            query: The input query to validate
            
        Raises:
            ToolError: If input validation fails
        """
        if not query or not query.strip():
            raise ToolError("Input query cannot be empty", "INVALID_INPUT")
    
    def _format_error(self, error: Exception, context: str = "") -> ToolResult:
        """
        Format an error into a standardized ToolResult.
        
        Args:
            error: The exception that occurred
            context: Additional context about where the error occurred
            
        Returns:
            ToolResult with error information
        """
        if isinstance(error, ToolError):
            return ToolResult(
                success=False,
                message=f"{context}: {error.message}" if context else error.message,
                error=error.error_code,
                metadata={"details": error.details}
            )
        else:
            return ToolResult(
                success=False,
                message=f"{context}: {str(error)}" if context else str(error),
                error="UNEXPECTED_ERROR"
            )


class ThreatIntelTool(BaseTool):
    """
    Base class for threat intelligence tools.
    
    Provides common functionality for tools that interact with
    threat intelligence sources.
    """
    
    def _extract_iocs(self, text: str) -> Dict[str, List[str]]:
        """
        Extract IOCs (Indicators of Compromise) from text.
        
        Args:
            text: Text to extract IOCs from
            
        Returns:
            Dictionary with IOC types as keys and lists of IOCs as values
        """
        import re
        
        iocs = {
            "ip_addresses": [],
            "domains": [],
            "urls": [],
            "file_hashes": [],
            "email_addresses": []
        }
        
        # IP addresses (IPv4)
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        iocs["ip_addresses"] = re.findall(ip_pattern, text)
        
        # Domains
        domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
        iocs["domains"] = re.findall(domain_pattern, text)
        
        # URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        iocs["urls"] = re.findall(url_pattern, text)
        
        # File hashes (MD5, SHA1, SHA256)
        hash_patterns = {
            "md5": r'\b[a-fA-F0-9]{32}\b',
            "sha1": r'\b[a-fA-F0-9]{40}\b',
            "sha256": r'\b[a-fA-F0-9]{64}\b'
        }
        
        for hash_type, pattern in hash_patterns.items():
            hashes = re.findall(pattern, text)
            iocs["file_hashes"].extend(hashes)
        
        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        iocs["email_addresses"] = re.findall(email_pattern, text)
        
        # Remove duplicates and filter out common false positives
        for ioc_type in iocs:
            iocs[ioc_type] = list(set(iocs[ioc_type]))
            iocs[ioc_type] = self._filter_false_positives(iocs[ioc_type], ioc_type)
        
        return iocs
    
    def _filter_false_positives(self, iocs: List[str], ioc_type: str) -> List[str]:
        """
        Filter out common false positives from IOC lists.
        
        Args:
            iocs: List of IOCs to filter
            ioc_type: Type of IOCs being filtered
            
        Returns:
            Filtered list of IOCs
        """
        if ioc_type == "ip_addresses":
            # Filter out private/reserved IP ranges
            private_ranges = [
                "127.", "10.", "192.168.", "172.16.", "172.17.", "172.18.",
                "172.19.", "172.20.", "172.21.", "172.22.", "172.23.",
                "172.24.", "172.25.", "172.26.", "172.27.", "172.28.",
                "172.29.", "172.30.", "172.31.", "169.254.", "224."
            ]
            return [ip for ip in iocs if not any(ip.startswith(prefix) for prefix in private_ranges)]
        
        elif ioc_type == "domains":
            # Filter out common legitimate domains
            common_domains = [
                "microsoft.com", "google.com", "apple.com", "amazon.com",
                "facebook.com", "twitter.com", "linkedin.com", "github.com",
                "stackoverflow.com", "wikipedia.org", "example.com", "localhost"
            ]
            return [domain for domain in iocs if domain.lower() not in common_domains]
        
        return iocs
    
    def _determine_ioc_type(self, ioc: str) -> str:
        """
        Determine the type of an IOC.
        
        Args:
            ioc: The IOC to classify
            
        Returns:
            IOC type string
        """
        import re
        
        # Check for IP address
        if re.match(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$', ioc):
            return "ip"
        
        # Check for file hashes
        if re.match(r'^[a-fA-F0-9]{32}$', ioc):
            return "file"  # MD5
        elif re.match(r'^[a-fA-F0-9]{40}$', ioc):
            return "file"  # SHA1
        elif re.match(r'^[a-fA-F0-9]{64}$', ioc):
            return "file"  # SHA256
        
        # Check for URL
        if ioc.startswith(('http://', 'https://')):
            return "url"
        
        # Check for email
        if '@' in ioc and '.' in ioc:
            return "email"
        
        # Default to domain
        return "domain"
