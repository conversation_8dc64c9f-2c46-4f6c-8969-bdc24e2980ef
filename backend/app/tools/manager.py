"""
Tool Manager for AlertAI

Manages and provides access to all available tools for the AI assistant.
"""

from typing import List, Dict, Any, Optional
import logging

from .virustotal import VirusTotalTool
from .base import ToolError, BaseTool

logger = logging.getLogger(__name__)


class ToolManager:
    """
    Manages all available tools for the AlertAI assistant.
    
    This class provides a centralized way to access and manage tools,
    including configuration, initialization, and tool discovery.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the tool manager.
        
        Args:
            config: Optional configuration dictionary for tools
        """
        self.config = config or {}
        self._tools: Dict[str, BaseTool] = {}
        self._initialize_tools()
    
    def _initialize_tools(self) -> None:
        """Initialize all available tools."""
        try:
            # Initialize VirusTotal tool
            vt_config = self.config.get("virustotal", {})
            self._tools["virustotal"] = VirusTotalTool(**vt_config)
            logger.info("VirusTotal tool initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing tools: {str(e)}")
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """
        Get a specific tool by name.
        
        Args:
            tool_name: Name of the tool to retrieve
            
        Returns:
            The requested tool or None if not found
        """
        return self._tools.get(tool_name)
    
    def get_all_tools(self) -> List[BaseTool]:
        """
        Get all available tools.
        
        Returns:
            List of all initialized tools
        """
        return list(self._tools.values())
    
    def get_tool_names(self) -> List[str]:
        """
        Get names of all available tools.
        
        Returns:
            List of tool names
        """
        return list(self._tools.keys())
    
    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """
        Get tools by category.
        
        Args:
            category: Tool category (e.g., 'threat_intel', 'analysis')
            
        Returns:
            List of tools in the specified category
        """
        category_tools = []
        
        if category == "threat_intel":
            if "virustotal" in self._tools:
                category_tools.append(self._tools["virustotal"])
        
        return category_tools
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions of all available tools.
        
        Returns:
            Dictionary mapping tool names to descriptions
        """
        return {
            name: tool.description 
            for name, tool in self._tools.items()
        }
    
    def is_tool_available(self, tool_name: str) -> bool:
        """
        Check if a tool is available and properly configured.
        
        Args:
            tool_name: Name of the tool to check
            
        Returns:
            True if tool is available, False otherwise
        """
        tool = self._tools.get(tool_name)
        if not tool:
            return False
        
        # Check tool-specific availability
        if tool_name == "virustotal":
            return hasattr(tool, 'api_key') and bool(tool.api_key)
        
        return True
    
    def get_tool_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status information for all tools.
        
        Returns:
            Dictionary with tool status information
        """
        status = {}
        
        for name, tool in self._tools.items():
            tool_status = {
                "available": self.is_tool_available(name),
                "name": tool.name,
                "description": tool.description
            }
            
            # Add tool-specific status information
            if name == "virustotal":
                tool_status.update({
                    "api_key_configured": bool(getattr(tool, 'api_key', '')),
                    "base_url": getattr(tool, 'base_url', ''),
                    "rate_limit_delay": getattr(tool, 'rate_limit_delay', 0)
                })
            
            status[name] = tool_status
        
        return status
    
    def configure_tool(self, tool_name: str, config: Dict[str, Any]) -> bool:
        """
        Configure a specific tool with new settings.
        
        Args:
            tool_name: Name of the tool to configure
            config: Configuration dictionary
            
        Returns:
            True if configuration was successful, False otherwise
        """
        try:
            if tool_name == "virustotal" and tool_name in self._tools:
                tool = self._tools[tool_name]
                for key, value in config.items():
                    if hasattr(tool, key):
                        setattr(tool, key, value)
                logger.info(f"Tool {tool_name} configured successfully")
                return True
            
            logger.warning(f"Tool {tool_name} not found or configuration not supported")
            return False
            
        except Exception as e:
            logger.error(f"Error configuring tool {tool_name}: {str(e)}")
            return False
    
    def test_tool(self, tool_name: str, test_input: str = "test") -> Dict[str, Any]:
        """
        Test a tool with sample input.
        
        Args:
            tool_name: Name of the tool to test
            test_input: Test input for the tool
            
        Returns:
            Test result dictionary
        """
        try:
            tool = self._tools.get(tool_name)
            if not tool:
                return {
                    "success": False,
                    "error": f"Tool {tool_name} not found"
                }
            
            # For VirusTotal, use a known clean test hash
            if tool_name == "virustotal":
                test_input = "d41d8cd98f00b204e9800998ecf8427e"  # MD5 of empty file
            
            result = tool._run(test_input)
            
            return {
                "success": True,
                "tool": tool_name,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error testing tool {tool_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# Global tool manager instance
_tool_manager: Optional[ToolManager] = None


def get_tool_manager(config: Optional[Dict[str, Any]] = None) -> ToolManager:
    """
    Get the global tool manager instance.
    
    Args:
        config: Optional configuration for tools
        
    Returns:
        ToolManager instance
    """
    global _tool_manager
    
    if _tool_manager is None:
        _tool_manager = ToolManager(config)
    
    return _tool_manager


def initialize_tools(config: Optional[Dict[str, Any]] = None) -> ToolManager:
    """
    Initialize the global tool manager with configuration.
    
    Args:
        config: Configuration dictionary for tools
        
    Returns:
        Initialized ToolManager instance
    """
    global _tool_manager
    _tool_manager = ToolManager(config)
    return _tool_manager


def get_available_tools() -> List[BaseTool]:
    """
    Get all available tools from the global manager.
    
    Returns:
        List of available tools
    """
    manager = get_tool_manager()
    return manager.get_all_tools()


def get_threat_intel_tools() -> List[BaseTool]:
    """
    Get all threat intelligence tools.
    
    Returns:
        List of threat intelligence tools
    """
    manager = get_tool_manager()
    return manager.get_tools_by_category("threat_intel")
