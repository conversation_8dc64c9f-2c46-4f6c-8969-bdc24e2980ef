"""
VirusTotal tool for AlertAI following LangChain best practices.

This tool allows the AI assistant to search IOCs (Indicators of Compromise)
in VirusTotal for threat intelligence and malware analysis.
"""

import os
import requests
import time
from typing import Dict, Any, Optional, List

from .base import Threat<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lResult, ToolError
import logging

logger = logging.getLogger(__name__)


class VirusTotalTool(ThreatIntelTool):
    """
    VirusTotal threat intelligence tool for IOC analysis.
    
    This tool can search various types of IOCs in VirusTotal including:
    - IP addresses
    - Domains
    - URLs
    - File hashes (MD5, SHA1, SHA256)
    
    The tool automatically detects IOC types and provides comprehensive
    threat intelligence information.
    """
    
    name: str = "virustotal_lookup"
    description: str = """
    Search IOCs (Indicators of Compromise) in VirusTotal for threat intelligence.
    
    Input can be:
    - A single IOC (IP, domain, URL, or file hash)
    - Multiple IOCs separated by commas or newlines
    - Text containing IOCs (will be automatically extracted)
    
    Returns comprehensive threat intelligence including:
    - Detection ratios from multiple antivirus engines
    - Threat classifications and malware families
    - Community comments and analysis
    - Related IOCs and campaigns
    - Historical analysis data
    
    Example inputs:
    - "*************"
    - "malicious-domain.com"
    - "https://suspicious-site.com/malware.exe"
    - "a1b2c3d4e5f6789..."
    - "Check these IOCs: *************, malicious.com"
    """
    
    def __init__(self, **kwargs):
        """Initialize VirusTotal tool with API configuration."""
        super().__init__(**kwargs)

        # Configuration attributes
        self.api_key = kwargs.get("api_key", "")
        self.base_url = kwargs.get("base_url", "https://www.virustotal.com/api/v3")
        self.rate_limit_delay = kwargs.get("rate_limit_delay", 15.0)
        self.max_retries = kwargs.get("max_retries", 3)

        # Get API key from environment if not provided
        if not self.api_key:
            # Ensure environment variables are loaded
            from dotenv import load_dotenv
            load_dotenv()
            self.api_key = os.getenv("VIRUSTOTAL_API_KEY", "")

        if not self.api_key:
            logger.warning("VirusTotal API key not configured. Tool will return mock data.")
    
    def _execute(
        self,
        query: str,
        run_manager: Optional[Any] = None,
    ) -> ToolResult:
        """
        Execute VirusTotal IOC lookup.
        
        Args:
            query: IOC(s) to search or text containing IOCs
            run_manager: Optional callback manager
            
        Returns:
            ToolResult with threat intelligence data
        """
        self._validate_input(query)
        
        try:
            # Extract IOCs from the query
            iocs = self._extract_and_classify_iocs(query)

            # Log IOC extraction details
            total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
            logger.info(f"🔍 VIRUSTOTAL IOC EXTRACTION - Found {total_iocs} IOCs:")
            for ioc_type, ioc_list in iocs.items():
                if ioc_list:
                    logger.info(f"🔍   - {ioc_type}: {len(ioc_list)} items - {ioc_list[:3]}{'...' if len(ioc_list) > 3 else ''}")

            if not any(iocs.values()):
                logger.warning("🔍 VIRUSTOTAL IOC EXTRACTION - No valid IOCs found in query")
                return ToolResult(
                    success=False,
                    message="No valid IOCs found in the input",
                    error="NO_IOCS_FOUND"
                )
            
            # Search each IOC in VirusTotal
            results = {}
            total_iocs = sum(len(ioc_list) for ioc_list in iocs.values())
            processed = 0
            
            for ioc_type, ioc_list in iocs.items():
                if not ioc_list:
                    continue
                    
                results[ioc_type] = {}
                
                for ioc in ioc_list:
                    try:
                        logger.info(f"🔍 VIRUSTOTAL API CALL - Searching {ioc_type}: {ioc}")
                        if run_manager:
                            run_manager.on_text(f"Searching {ioc} in VirusTotal...\n")

                        result = self._search_ioc(ioc, ioc_type)
                        results[ioc_type][ioc] = result
                        processed += 1

                        # Log individual result
                        if result.get("found"):
                            threat_level = result.get("threat_level", "unknown")
                            detection_ratio = result.get("detection_ratio", "N/A")
                            logger.info(f"🔍 VIRUSTOTAL RESULT - {ioc}: {threat_level} ({detection_ratio})")
                        else:
                            logger.info(f"🔍 VIRUSTOTAL RESULT - {ioc}: Not found")

                        # Rate limiting
                        if processed < total_iocs:
                            logger.info(f"🔍 VIRUSTOTAL RATE LIMIT - Waiting {self.rate_limit_delay}s before next request")
                            time.sleep(self.rate_limit_delay)
                            
                    except Exception as e:
                        logger.error(f"Error searching IOC {ioc}: {str(e)}")
                        results[ioc_type][ioc] = {
                            "error": str(e),
                            "searched": False
                        }
            
            # Generate summary
            summary = self._generate_summary(results)
            
            return ToolResult(
                success=True,
                data={
                    "results": results,
                    "summary": summary,
                    "total_iocs_searched": processed,
                    "search_timestamp": time.time()
                },
                message=f"Successfully searched {processed} IOCs in VirusTotal",
                metadata={
                    "tool": "virustotal",
                    "api_key_configured": bool(self.api_key),
                    "ioc_types_found": list(iocs.keys())
                }
            )
            
        except Exception as e:
            raise ToolError(f"VirusTotal search failed: {str(e)}", "VT_SEARCH_ERROR")
    
    def _extract_and_classify_iocs(self, query: str) -> Dict[str, List[str]]:
        """
        Extract and classify IOCs from the input query.
        
        Args:
            query: Input text that may contain IOCs
            
        Returns:
            Dictionary with classified IOCs
        """
        # First try to extract IOCs automatically
        iocs = self._extract_iocs(query)
        
        # If no IOCs found, treat the entire query as a single IOC
        if not any(iocs.values()):
            ioc_type = self._determine_ioc_type(query.strip())
            if ioc_type == "ip":
                iocs["ip_addresses"] = [query.strip()]
            elif ioc_type == "domain":
                iocs["domains"] = [query.strip()]
            elif ioc_type == "url":
                iocs["urls"] = [query.strip()]
            elif ioc_type == "file":
                iocs["file_hashes"] = [query.strip()]
        
        return iocs
    
    def _search_ioc(self, ioc: str, ioc_type: str) -> Dict[str, Any]:
        """
        Search a single IOC in VirusTotal.
        
        Args:
            ioc: The IOC to search
            ioc_type: Type of IOC (ip_addresses, domains, urls, file_hashes)
            
        Returns:
            Dictionary with search results
        """
        if not self.api_key:
            return self._get_mock_result(ioc, ioc_type)
        
        try:
            # Determine the appropriate VirusTotal v3 endpoint
            if ioc_type == "ip_addresses":
                endpoint = f"{self.base_url}/ip_addresses/{ioc}"
                params = {}
            elif ioc_type == "domains":
                endpoint = f"{self.base_url}/domains/{ioc}"
                params = {}
            elif ioc_type == "urls":
                # URLs need to be base64 encoded for v3 API
                import base64
                url_id = base64.urlsafe_b64encode(ioc.encode()).decode().strip("=")
                endpoint = f"{self.base_url}/urls/{url_id}"
                params = {}
            elif ioc_type == "file_hashes":
                endpoint = f"{self.base_url}/files/{ioc}"
                params = {}
            else:
                raise ToolError(f"Unsupported IOC type: {ioc_type}", "UNSUPPORTED_IOC_TYPE")
            
            # Make API request with retries (v3 uses headers for auth)
            headers = {
                "x-apikey": self.api_key,
                "Accept": "application/json"
            }

            for attempt in range(self.max_retries):
                try:
                    response = requests.get(endpoint, params=params, headers=headers, timeout=30)
                    response.raise_for_status()

                    data = response.json()

                    # Check for API errors (v3 format)
                    if "error" in data:
                        error_code = data["error"].get("code")
                        if error_code == "NotFoundError":
                            return {
                                "found": False,
                                "message": "IOC not found in VirusTotal database",
                                "searched": True
                            }
                        else:
                            return {
                                "found": False,
                                "message": f"API error: {data['error'].get('message', 'Unknown error')}",
                                "searched": True
                            }

                    # Parse and format the results (v3 format)
                    return self._parse_vt_v3_response(data, ioc, ioc_type)
                    
                except requests.exceptions.RequestException as e:
                    if attempt == self.max_retries - 1:
                        raise ToolError(f"API request failed after {self.max_retries} attempts: {str(e)}", "API_REQUEST_FAILED")
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
        except Exception as e:
            logger.error(f"Error searching IOC {ioc}: {str(e)}")
            return {
                "error": str(e),
                "searched": False
            }
    
    def _parse_vt_v3_response(self, data: Dict[str, Any], ioc: str, ioc_type: str) -> Dict[str, Any]:
        """
        Parse VirusTotal API v3 response into a standardized format.

        Args:
            data: Raw VirusTotal API v3 response
            ioc: The IOC that was searched
            ioc_type: Type of IOC

        Returns:
            Parsed and formatted results
        """
        result = {
            "found": True,
            "searched": True,
            "ioc": ioc,
            "ioc_type": ioc_type
        }

        # Extract data from v3 response structure
        attributes = data.get("data", {}).get("attributes", {})

        # Parse detection results from last_analysis_stats
        last_analysis_stats = attributes.get("last_analysis_stats", {})
        malicious = last_analysis_stats.get("malicious", 0)
        suspicious = last_analysis_stats.get("suspicious", 0)
        undetected = last_analysis_stats.get("undetected", 0)
        harmless = last_analysis_stats.get("harmless", 0)
        timeout = last_analysis_stats.get("timeout", 0)

        total_scans = malicious + suspicious + undetected + harmless + timeout
        positive_detections = malicious + suspicious

        result.update({
            "detection_ratio": f"{positive_detections}/{total_scans}",
            "positive_detections": positive_detections,
            "total_scans": total_scans,
            "malicious_detections": malicious,
            "suspicious_detections": suspicious,
            "detection_percentage": round((positive_detections / total_scans) * 100, 2) if total_scans > 0 else 0,
            "last_analysis_date": attributes.get("last_analysis_date"),
            "reputation": attributes.get("reputation", 0)
        })

        # Extract detected engines and their results
        last_analysis_results = attributes.get("last_analysis_results", {})
        malware_families = set()
        detected_engines = []

        for engine_name, engine_result in last_analysis_results.items():
            if engine_result.get("category") in ["malicious", "suspicious"]:
                detected_engines.append({
                    "engine": engine_name,
                    "category": engine_result.get("category"),
                    "result": engine_result.get("result")
                })
                if engine_result.get("result"):
                    malware_families.add(engine_result["result"])

        result["malware_families"] = list(malware_families)[:10]  # Limit to top 10
        result["detected_engines"] = detected_engines[:10]  # Limit to top 10

        # Add IOC-specific information
        if ioc_type == "ip_addresses":
            result.update({
                "country": attributes.get("country"),
                "asn": attributes.get("asn"),
                "as_owner": attributes.get("as_owner"),
                "network": attributes.get("network")
            })
        elif ioc_type == "domains":
            result.update({
                "whois_date": attributes.get("whois_date"),
                "categories": attributes.get("categories", {}),
                "creation_date": attributes.get("creation_date")
            })
        elif ioc_type == "file_hashes":
            result.update({
                "md5": attributes.get("md5"),
                "sha1": attributes.get("sha1"),
                "sha256": attributes.get("sha256"),
                "file_size": attributes.get("size"),
                "file_type": attributes.get("type_description"),
                "first_submission_date": attributes.get("first_submission_date"),
                "last_submission_date": attributes.get("last_submission_date")
            })

        # Determine threat level based on v3 results
        if malicious > 0:
            if malicious >= 10:
                result["threat_level"] = "malicious"
            elif malicious >= 3:
                result["threat_level"] = "likely_malicious"
            else:
                result["threat_level"] = "suspicious"
        elif suspicious > 0:
            result["threat_level"] = "suspicious"
        else:
            result["threat_level"] = "clean"

        return result

    def _parse_vt_response(self, data: Dict[str, Any], ioc: str, ioc_type: str) -> Dict[str, Any]:
        """
        Parse VirusTotal API response into a standardized format.
        
        Args:
            data: Raw VirusTotal API response
            ioc: The IOC that was searched
            ioc_type: Type of IOC
            
        Returns:
            Parsed and formatted results
        """
        result = {
            "found": True,
            "searched": True,
            "ioc": ioc,
            "ioc_type": ioc_type,
            "scan_date": data.get("scan_date"),
            "permalink": data.get("permalink")
        }
        
        # Parse detection results
        positive_detections = 0  # Initialize to avoid UnboundLocalError
        total_scans = 0

        if "scans" in data:
            scans = data["scans"]
            total_scans = len(scans)
            positive_detections = sum(1 for scan in scans.values() if scan.get("detected"))
            
            result.update({
                "detection_ratio": f"{positive_detections}/{total_scans}",
                "positive_detections": positive_detections,
                "total_scans": total_scans,
                "detection_percentage": round((positive_detections / total_scans) * 100, 2) if total_scans > 0 else 0
            })
            
            # Extract detected malware families
            malware_families = set()
            for scan_result in scans.values():
                if scan_result.get("detected") and scan_result.get("result"):
                    malware_families.add(scan_result["result"])
            
            result["malware_families"] = list(malware_families)[:10]  # Limit to top 10
        
        # Add IOC-specific information
        if ioc_type == "ip_addresses":
            result.update({
                "country": data.get("country"),
                "asn": data.get("asn"),
                "as_owner": data.get("as_owner")
            })
        elif ioc_type == "domains":
            result.update({
                "whois_timestamp": data.get("whois_timestamp"),
                "categories": data.get("categories", [])
            })
        elif ioc_type == "file_hashes":
            result.update({
                "md5": data.get("md5"),
                "sha1": data.get("sha1"),
                "sha256": data.get("sha256"),
                "file_size": data.get("size"),
                "file_type": data.get("type"),
                "first_seen": data.get("first_seen"),
                "last_seen": data.get("last_seen")
            })
        
        # Determine threat level
        if positive_detections == 0:
            result["threat_level"] = "clean"
        elif positive_detections <= 3:
            result["threat_level"] = "suspicious"
        elif positive_detections <= 10:
            result["threat_level"] = "likely_malicious"
        else:
            result["threat_level"] = "malicious"
        
        return result
    
    def _generate_summary(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of all VirusTotal search results.
        
        Args:
            results: Complete search results
            
        Returns:
            Summary dictionary
        """
        summary = {
            "total_iocs": 0,
            "found_iocs": 0,
            "malicious_iocs": 0,
            "suspicious_iocs": 0,
            "clean_iocs": 0,
            "threat_levels": {},
            "top_malware_families": {},
            "recommendations": []
        }
        
        for ioc_type, ioc_results in results.items():
            for ioc, result in ioc_results.items():
                summary["total_iocs"] += 1
                
                if result.get("found"):
                    summary["found_iocs"] += 1
                    
                    threat_level = result.get("threat_level", "unknown")
                    summary["threat_levels"][threat_level] = summary["threat_levels"].get(threat_level, 0) + 1
                    
                    if threat_level == "malicious":
                        summary["malicious_iocs"] += 1
                    elif threat_level == "suspicious" or threat_level == "likely_malicious":
                        summary["suspicious_iocs"] += 1
                    elif threat_level == "clean":
                        summary["clean_iocs"] += 1
                    
                    # Collect malware families
                    for family in result.get("malware_families", []):
                        summary["top_malware_families"][family] = summary["top_malware_families"].get(family, 0) + 1
        
        # Generate recommendations
        if summary["malicious_iocs"] > 0:
            summary["recommendations"].append("CRITICAL: Malicious IOCs detected. Immediate containment recommended.")
        if summary["suspicious_iocs"] > 0:
            summary["recommendations"].append("WARNING: Suspicious IOCs found. Further investigation recommended.")
        if summary["clean_iocs"] == summary["found_iocs"] and summary["found_iocs"] > 0:
            summary["recommendations"].append("INFO: All searched IOCs appear clean in VirusTotal.")
        
        return summary
    
    def _get_mock_result(self, ioc: str, ioc_type: str) -> Dict[str, Any]:
        """
        Generate mock VirusTotal results when API key is not configured.
        
        Args:
            ioc: The IOC being searched
            ioc_type: Type of IOC
            
        Returns:
            Mock result data
        """
        return {
            "found": True,
            "searched": True,
            "ioc": ioc,
            "ioc_type": ioc_type,
            "detection_ratio": "0/0",
            "positive_detections": 0,
            "total_scans": 0,
            "detection_percentage": 0,
            "threat_level": "unknown",
            "malware_families": [],
            "scan_date": "N/A",
            "permalink": "N/A",
            "note": "Mock result - VirusTotal API key not configured"
        }
