"""
Authentication and authorization system for AlertAI

Implements JWT-based authentication with role-based access control (RBAC).
"""

import jwt
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.models.database.user import DBUser, DBRole, DBPermission


# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = settings.access_token_expire_minutes


class AuthService:
    """Authentication and authorization service"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=ALGORITHM)
        
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """Verify and decode a JWT token"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
    
    @staticmethod
    def authenticate_user(db: Session, email: str, password: str) -> Optional[DBUser]:
        """Authenticate a user with email and password"""
        from app.services.user import UserService
        
        user = UserService.get_user_by_email(db, email)
        if not user:
            return None
        
        if not AuthService.verify_password(password, user.password_hash):
            return None
        
        if not user.is_active:
            return None
        
        return user
    
    @staticmethod
    def get_current_user(db: Session, token: str) -> DBUser:
        """Get current user from JWT token"""
        from app.services.user import UserService
        
        try:
            payload = AuthService.verify_token(token)
            user_id: int = payload.get("sub")
            
            if user_id is None:
                raise AuthenticationError("Invalid token payload")
            
        except AuthenticationError:
            raise
        
        user = UserService.get_user_by_id(db, user_id)
        if user is None:
            raise AuthenticationError("User not found")
        
        if not user.is_active:
            raise AuthenticationError("User account is disabled")
        
        return user
    
    @staticmethod
    def check_permission(user: DBUser, permission: str, resource: Optional[str] = None) -> bool:
        """Check if user has a specific permission"""
        # Super admin has all permissions
        if user.is_superuser:
            return True
        
        # Check user's roles and permissions
        for role in user.roles:
            if not role.is_active:
                continue
                
            for perm in role.permissions:
                if perm.name == permission:
                    # Check resource-specific permissions if specified
                    if resource and perm.resource_type and perm.resource_type != resource:
                        continue
                    return True
        
        return False
    
    @staticmethod
    def require_permission(user: DBUser, permission: str, resource: Optional[str] = None) -> None:
        """Require a specific permission, raise exception if not granted"""
        if not AuthService.check_permission(user, permission, resource):
            raise AuthorizationError(
                f"Permission '{permission}' required",
                resource=resource,
                action=permission
            )
    
    @staticmethod
    def get_user_permissions(user: DBUser) -> List[str]:
        """Get all permissions for a user"""
        if user.is_superuser:
            return ["*"]  # Super admin has all permissions
        
        permissions = set()
        for role in user.roles:
            if role.is_active:
                for perm in role.permissions:
                    permissions.add(perm.name)
        
        return list(permissions)
    
    @staticmethod
    def get_user_roles(user: DBUser) -> List[str]:
        """Get all role names for a user"""
        return [role.name for role in user.roles if role.is_active]


class RoleManager:
    """Role and permission management"""
    
    # Default roles and permissions
    DEFAULT_ROLES = {
        "admin": {
            "description": "System administrator with full access",
            "permissions": [
                "alerts:read", "alerts:write", "alerts:delete",
                "comments:read", "comments:write", "comments:delete",
                "files:read", "files:write", "files:delete",
                "users:read", "users:write", "users:delete",
                "roles:read", "roles:write", "roles:delete",
                "system:read", "system:write", "system:admin"
            ]
        },
        "analyst": {
            "description": "Security analyst with alert management access",
            "permissions": [
                "alerts:read", "alerts:write",
                "comments:read", "comments:write",
                "files:read", "files:write",
                "chat:read", "chat:write"
            ]
        },
        "viewer": {
            "description": "Read-only access to alerts and reports",
            "permissions": [
                "alerts:read",
                "comments:read",
                "files:read",
                "reports:read"
            ]
        },
        "guest": {
            "description": "Limited access for external users",
            "permissions": [
                "alerts:read"
            ]
        }
    }
    
    @staticmethod
    def create_default_roles(db: Session) -> None:
        """Create default roles and permissions"""
        from app.services.user import UserService
        
        for role_name, role_data in RoleManager.DEFAULT_ROLES.items():
            # Create role if it doesn't exist
            role = UserService.get_role_by_name(db, role_name)
            if not role:
                role = UserService.create_role(
                    db=db,
                    name=role_name,
                    description=role_data["description"]
                )
            
            # Create permissions and assign to role
            for perm_name in role_data["permissions"]:
                permission = UserService.get_permission_by_name(db, perm_name)
                if not permission:
                    # Extract resource type from permission name
                    resource_type = perm_name.split(":")[0] if ":" in perm_name else None
                    action = perm_name.split(":")[1] if ":" in perm_name else perm_name
                    
                    permission = UserService.create_permission(
                        db=db,
                        name=perm_name,
                        description=f"Permission to {action} {resource_type or 'system'}",
                        resource_type=resource_type
                    )
                
                # Assign permission to role if not already assigned
                if permission not in role.permissions:
                    role.permissions.append(permission)
        
        db.commit()
    
    @staticmethod
    def create_default_admin_user(db: Session, email: str, password: str) -> DBUser:
        """Create default admin user"""
        from app.services.user import UserService
        
        # Check if admin user already exists
        admin_user = UserService.get_user_by_email(db, email)
        if admin_user:
            return admin_user
        
        # Create admin user
        admin_user = UserService.create_user(
            db=db,
            email=email,
            password=password,
            full_name="System Administrator",
            is_superuser=True,
            is_active=True
        )
        
        # Assign admin role
        admin_role = UserService.get_role_by_name(db, "admin")
        if admin_role and admin_role not in admin_user.roles:
            admin_user.roles.append(admin_role)
            db.commit()
        
        return admin_user


# Permission decorators and utilities
def require_permissions(*permissions: str):
    """Decorator to require specific permissions for an endpoint"""
    def decorator(func):
        func._required_permissions = permissions
        return func
    return decorator


def require_roles(*roles: str):
    """Decorator to require specific roles for an endpoint"""
    def decorator(func):
        func._required_roles = roles
        return func
    return decorator
