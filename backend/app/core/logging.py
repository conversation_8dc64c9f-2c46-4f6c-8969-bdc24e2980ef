"""
Centralized logging system for AlertAI

Provides structured logging with security event tracking and audit trails.
"""

import logging
import json
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from app.core.config import settings


class SecurityLogger:
    """Security-focused logger with structured output"""
    
    def __init__(self):
        self.logger = logging.getLogger("alertai.security")
        self.setup_logger()
    
    def setup_logger(self):
        """Configure the security logger"""
        self.logger.setLevel(getattr(logging, settings.log_level.upper()))
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # File handler for security events
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / "security.log")
        file_handler.setLevel(logging.WARNING)
        
        # JSON formatter for structured logging
        formatter = JsonFormatter()
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def log_auth_event(
        self,
        event_type: str,
        user_id: Optional[int] = None,
        email: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        **kwargs
    ):
        """Log authentication events"""
        event_data = {
            "event_type": "authentication",
            "action": event_type,
            "user_id": user_id,
            "email": email,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "success": success,
            "error_message": error_message,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        level = logging.INFO if success else logging.WARNING
        self.logger.log(level, f"Auth event: {event_type}", extra={"event_data": event_data})
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        description: str,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        **kwargs
    ):
        """Log security events"""
        event_data = {
            "event_type": "security",
            "action": event_type,
            "severity": severity,
            "description": description,
            "user_id": user_id,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        level_map = {
            "low": logging.INFO,
            "medium": logging.WARNING,
            "high": logging.ERROR,
            "critical": logging.CRITICAL
        }
        level = level_map.get(severity, logging.WARNING)
        
        self.logger.log(level, f"Security event: {event_type}", extra={"event_data": event_data})
    
    def log_api_event(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        **kwargs
    ):
        """Log API access events"""
        event_data = {
            "event_type": "api_access",
            "method": method,
            "endpoint": endpoint,
            "status_code": status_code,
            "user_id": user_id,
            "ip_address": ip_address,
            "response_time_ms": response_time_ms,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        level = logging.INFO if status_code < 400 else logging.WARNING
        self.logger.log(level, f"API access: {method} {endpoint}", extra={"event_data": event_data})
    
    def log_rate_limit_event(
        self,
        client_id: str,
        endpoint: str,
        limit: int,
        window: int,
        blocked: bool = False,
        **kwargs
    ):
        """Log rate limiting events"""
        event_data = {
            "event_type": "rate_limit",
            "client_id": client_id,
            "endpoint": endpoint,
            "limit": limit,
            "window": window,
            "blocked": blocked,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        level = logging.WARNING if blocked else logging.INFO
        action = "blocked" if blocked else "tracked"
        self.logger.log(level, f"Rate limit {action}: {client_id}", extra={"event_data": event_data})


class ApplicationLogger:
    """General application logger"""
    
    def __init__(self):
        self.logger = logging.getLogger("alertai.app")
        self.setup_logger()
    
    def setup_logger(self):
        """Configure the application logger"""
        self.logger.setLevel(getattr(logging, settings.log_level.upper()))
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG if settings.debug else logging.INFO)
        
        # File handler for application logs
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / "application.log")
        file_handler.setLevel(logging.INFO)
        
        # Standard formatter for application logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, extra=kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, extra=kwargs)


class JsonFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add event data if present
        if hasattr(record, 'event_data'):
            log_entry["event_data"] = record.event_data
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)


# Global logger instances
security_logger = SecurityLogger()
app_logger = ApplicationLogger()


def log_startup_event(component: str, status: str, details: Optional[Dict[str, Any]] = None):
    """Log application startup events"""
    app_logger.info(
        f"Startup: {component} - {status}",
        component=component,
        status=status,
        details=details or {}
    )


def log_shutdown_event(component: str, status: str, details: Optional[Dict[str, Any]] = None):
    """Log application shutdown events"""
    app_logger.info(
        f"Shutdown: {component} - {status}",
        component=component,
        status=status,
        details=details or {}
    )


def log_performance_metric(
    operation: str,
    duration_ms: int,
    success: bool = True,
    details: Optional[Dict[str, Any]] = None
):
    """Log performance metrics"""
    level = "info" if success else "warning"
    getattr(app_logger, level)(
        f"Performance: {operation} took {duration_ms}ms",
        operation=operation,
        duration_ms=duration_ms,
        success=success,
        details=details or {}
    )


def log_database_event(
    operation: str,
    table: str,
    record_id: Optional[str] = None,
    success: bool = True,
    error: Optional[str] = None
):
    """Log database operations"""
    level = "info" if success else "error"
    message = f"Database: {operation} on {table}"
    if record_id:
        message += f" (ID: {record_id})"
    
    getattr(app_logger, level)(
        message,
        operation=operation,
        table=table,
        record_id=record_id,
        success=success,
        error=error
    )
