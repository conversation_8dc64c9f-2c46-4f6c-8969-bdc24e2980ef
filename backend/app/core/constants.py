"""
Application constants for AlertAI
"""

from enum import Enum


# Alert related constants
class AlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    OPEN = "open"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"


class AIFindingType(str, Enum):
    THREAT_DETECTION = "threat_detection"
    ANOMALY_DETECTION = "anomaly_detection"
    PATTERN_ANALYSIS = "pattern_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    RECOMMENDATION = "recommendation"


# Authentication types
class AuthenticationType(str, Enum):
    CUSTOM_HEADER = "custom_header"
    BEARER_TOKEN = "bearer_token"


# System log levels
class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# Event types for system logging
class EventType(str, Enum):
    ALERT_MANAGEMENT = "alert_management"
    BITZY_INTERACTION = "bitzy_interaction"
    SOURCE_MANAGEMENT = "source_management"
    USER_ACTION = "user_action"
    FILE_OPERATION = "file_operation"
    COMMENT_MANAGEMENT = "comment_management"
    SYSTEM_EVENT = "system_event"


# Action types for system logging
class ActionType(str, Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    VIEW = "view"
    SEARCH = "search"
    EXPORT = "export"
    LOGIN = "login"
    LOGOUT = "logout"
    UPLOAD = "upload"
    DOWNLOAD = "download"


# HTTP status codes
HTTP_STATUS_CODES = {
    "OK": 200,
    "CREATED": 201,
    "NO_CONTENT": 204,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "METHOD_NOT_ALLOWED": 405,
    "CONFLICT": 409,
    "UNPROCESSABLE_ENTITY": 422,
    "TOO_MANY_REQUESTS": 429,
    "INTERNAL_SERVER_ERROR": 500,
    "BAD_GATEWAY": 502,
    "SERVICE_UNAVAILABLE": 503,
    "GATEWAY_TIMEOUT": 504,
}

# Error codes
ERROR_CODES = {
    "VALIDATION_ERROR": "VALIDATION_ERROR",
    "NOT_FOUND": "NOT_FOUND",
    "AUTHENTICATION_ERROR": "AUTHENTICATION_ERROR",
    "AUTHORIZATION_ERROR": "AUTHORIZATION_ERROR",
    "DATABASE_ERROR": "DATABASE_ERROR",
    "EXTERNAL_SERVICE_ERROR": "EXTERNAL_SERVICE_ERROR",
    "FILE_UPLOAD_ERROR": "FILE_UPLOAD_ERROR",
    "RATE_LIMIT_ERROR": "RATE_LIMIT_ERROR",
    "CONFIGURATION_ERROR": "CONFIGURATION_ERROR",
}

# MIME types for file uploads
ALLOWED_MIME_TYPES = {
    "image/jpeg",
    "image/png", 
    "image/gif",
    "image/webp",
    "text/plain",
    "text/csv",
    "application/pdf",
    "application/json",
    "application/xml",
    "application/zip",
}

# File size limits (in bytes)
FILE_SIZE_LIMITS = {
    "image": 10 * 1024 * 1024,  # 10MB
    "document": 50 * 1024 * 1024,  # 50MB
    "archive": 100 * 1024 * 1024,  # 100MB
    "default": 10 * 1024 * 1024,  # 10MB
}

# Pagination limits
PAGINATION_LIMITS = {
    "min_limit": 1,
    "max_limit": 1000,
    "default_limit": 100,
}

# Cache TTL values (in seconds)
CACHE_TTL = {
    "short": 300,  # 5 minutes
    "medium": 1800,  # 30 minutes
    "long": 3600,  # 1 hour
    "very_long": 86400,  # 24 hours
}

# Rate limiting
RATE_LIMITS = {
    "api_requests": {"requests": 100, "window": 60},  # 100 requests per minute
    "file_uploads": {"requests": 10, "window": 60},   # 10 uploads per minute
    "ai_requests": {"requests": 20, "window": 60},    # 20 AI requests per minute
}

# Database table names
TABLE_NAMES = {
    "alerts": "alerts",
    "comments": "comments",
    "ai_findings": "ai_findings",
    "file_resources": "file_resources",
    "analysts": "analysts",
    "comment_mentions": "comment_mentions",
    "chat_sessions": "chat_sessions",
    "chat_messages": "chat_messages",
    "webhook_sources": "webhook_sources",
    "webhook_alerts": "webhook_alerts",
    "alert_change_logs": "alert_change_logs",
    "system_logs": "system_logs",
}

# API endpoints
API_ENDPOINTS = {
    "alerts": "/api/alerts",
    "comments": "/api/alerts/{alert_id}/comments",
    "ai_findings": "/api/alerts/{alert_id}/ai-findings",
    "file_upload": "/api/alerts/{alert_id}/upload",
    "chat": "/api/chat",
    "alert_chat": "/api/alerts/{alert_id}/chat",
    "sources": "/api/sources",
    "logs": "/api/logs",
    "health": "/api/health",
}

# Default values
DEFAULTS = {
    "alert_priority": 3,
    "escalation_level": 0,
    "page_size": 100,
    "chat_session_timeout": 3600,  # 1 hour
    "file_retention_days": 365,
    "log_retention_days": 90,
}

# Regex patterns for validation
VALIDATION_PATTERNS = {
    "email": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
    "ip_address": r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$",
    "domain": r"^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
    "hash_md5": r"^[a-fA-F0-9]{32}$",
    "hash_sha1": r"^[a-fA-F0-9]{40}$",
    "hash_sha256": r"^[a-fA-F0-9]{64}$",
    "hash_any": r"^[a-fA-F0-9]{32,64}$",
    "username": r"^[a-zA-Z0-9_-]{3,50}$",
    "filename": r"^[a-zA-Z0-9._-]+$",
    "uuid": r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
}

# Security headers
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Content-Security-Policy": "default-src 'self'",
}

# CORS settings
CORS_SETTINGS = {
    "allow_credentials": True,
    "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "allow_headers": [
        "Accept",
        "Accept-Language", 
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
    ],
    "expose_headers": ["X-Total-Count", "X-Page-Count"],
}

# AI model settings
AI_MODELS = {
    "default": "deepseek-r1:8b",
    "alternatives": ["llama2", "mistral", "codellama"],
    "timeout": 120,
    "max_tokens": 4096,
    "temperature": 0.7,
}

# Webhook settings
WEBHOOK_SETTINGS = {
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 5,
    "signature_header": "X-Webhook-Signature",
}

# Feature flags
FEATURE_FLAGS = {
    "enable_ai_chat": True,
    "enable_file_uploads": True,
    "enable_threat_intel": True,
    "enable_correlation": True,
    "enable_webhooks": True,
    "enable_system_logs": True,
    "enable_rate_limiting": True,
    "enable_security_headers": True,
    "enable_caching": True,
    "enable_metrics": True,
}
