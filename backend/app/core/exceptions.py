"""
Custom exceptions for AlertAI application
"""

from typing import Any, Dict, Optional


class AlertAIException(Exception):
    """Base exception for AlertAI application"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(self.message)


class ValidationError(AlertAIException):
    """Raised when input validation fails"""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if field:
            error_details["field"] = field
        if value is not None:
            error_details["value"] = value
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=error_details,
            status_code=422
        )


class NotFoundError(AlertAIException):
    """Raised when a requested resource is not found"""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        message = f"{resource_type} not found"
        if resource_id is not None:
            message += f" with ID: {resource_id}"
            
        error_details = details or {}
        error_details["resource_type"] = resource_type
        if resource_id is not None:
            error_details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            details=error_details,
            status_code=404
        )


class AuthenticationError(AlertAIException):
    """Raised when authentication fails"""
    
    def __init__(
        self,
        message: str = "Authentication required",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            status_code=401
        )


class AuthorizationError(AlertAIException):
    """Raised when authorization fails"""
    
    def __init__(
        self,
        message: str = "Access denied",
        resource: Optional[str] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if resource:
            error_details["resource"] = resource
        if action:
            error_details["action"] = action
            
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=error_details,
            status_code=403
        )


class DatabaseError(AlertAIException):
    """Raised when database operations fail"""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        if table:
            error_details["table"] = table
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=error_details,
            status_code=500
        )


class ExternalServiceError(AlertAIException):
    """Raised when external service calls fail"""
    
    def __init__(
        self,
        service_name: str,
        message: str,
        status_code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        error_details["service_name"] = service_name
        if status_code:
            error_details["service_status_code"] = status_code
            
        super().__init__(
            message=f"{service_name}: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            details=error_details,
            status_code=503
        )


class FileUploadError(AlertAIException):
    """Raised when file upload operations fail"""
    
    def __init__(
        self,
        message: str,
        filename: Optional[str] = None,
        file_size: Optional[int] = None,
        file_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if filename:
            error_details["filename"] = filename
        if file_size:
            error_details["file_size"] = file_size
        if file_type:
            error_details["file_type"] = file_type
            
        super().__init__(
            message=message,
            error_code="FILE_UPLOAD_ERROR",
            details=error_details,
            status_code=400
        )


class RateLimitError(AlertAIException):
    """Raised when rate limits are exceeded"""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        limit: Optional[int] = None,
        window: Optional[int] = None,
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if limit:
            error_details["limit"] = limit
        if window:
            error_details["window_seconds"] = window
        if retry_after:
            error_details["retry_after_seconds"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=error_details,
            status_code=429
        )


class ConfigurationError(AlertAIException):
    """Raised when configuration is invalid"""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if config_key:
            error_details["config_key"] = config_key
        if config_value is not None:
            error_details["config_value"] = config_value
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=error_details,
            status_code=500
        )
