"""
Security utilities and validation functions for AlertAI
"""

import re
from pathlib import Path
from fastapi import H<PERSON>P<PERSON>x<PERSON>, UploadFile, Request
from .config import settings
from .exceptions import ValidationError


def validate_file_upload(file: UploadFile) -> None:
    """Validate uploaded file for security"""
    if not file.filename:
        raise ValidationError("Filename is required")
    
    # Check file size
    if file.size and file.size > settings.max_file_size:
        raise ValidationError(
            f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )
    
    # Check file type
    allowed_types = settings.get_allowed_file_types()
    if file.content_type and file.content_type not in allowed_types:
        raise ValidationError(
            f"File type {file.content_type} not allowed"
        )
    
    # Check filename for path traversal
    if ".." in file.filename or "/" in file.filename or "\\" in file.filename:
        raise ValidationError("Invalid filename")


def sanitize_input(text: str, max_length: int = 1000) -> str:
    """Sanitize user input"""
    if not text:
        return ""
    
    # Remove null bytes and control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized.strip()


def validate_pagination(limit: int = 100, offset: int = 0) -> tuple[int, int]:
    """Validate and sanitize pagination parameters"""
    limit = max(1, min(limit, settings.max_page_size))
    offset = max(0, offset)
    return limit, offset


def get_client_ip(request: Request) -> str:
    """Extract client IP address safely"""
    # Check for forwarded headers (be careful with these in production)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # Fallback to client host
    return getattr(request.client, "host", "unknown") if request.client else "unknown"


def validate_email(email: str) -> bool:
    """Validate email format"""
    from .config import VALIDATION_PATTERNS
    pattern = re.compile(VALIDATION_PATTERNS['email'])
    return bool(pattern.match(email))


def validate_ip_address(ip: str) -> bool:
    """Validate IP address format"""
    from .config import VALIDATION_PATTERNS
    pattern = re.compile(VALIDATION_PATTERNS['ip_address'])
    return bool(pattern.match(ip))


def validate_domain(domain: str) -> bool:
    """Validate domain format"""
    from .config import VALIDATION_PATTERNS
    pattern = re.compile(VALIDATION_PATTERNS['domain'])
    return bool(pattern.match(domain))


def validate_hash(hash_value: str) -> bool:
    """Validate hash format (MD5, SHA1, SHA256)"""
    from .config import VALIDATION_PATTERNS
    pattern = re.compile(VALIDATION_PATTERNS['hash_any'])
    return bool(pattern.match(hash_value))


def generate_secure_filename(original_filename: str) -> str:
    """Generate a secure filename"""
    import uuid
    
    # Sanitize the original filename
    sanitized = sanitize_input(original_filename, 255)
    
    # Extract file extension
    file_path = Path(sanitized)
    file_extension = file_path.suffix.lower()
    file_stem = file_path.stem
    
    # Generate unique identifier
    unique_id = str(uuid.uuid4())[:8]
    
    # Create secure filename
    secure_filename = f"{file_stem}_{unique_id}{file_extension}"
    
    return secure_filename


def validate_json_structure(data: dict, required_fields: list[str]) -> None:
    """Validate JSON structure has required fields"""
    missing_fields = []
    for field in required_fields:
        if field not in data:
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        sanitized = name[:max_name_length] + ('.' + ext if ext else '')
    
    return sanitized or 'unnamed_file'


def check_file_permissions(file_path: Path) -> bool:
    """Check if file has safe permissions"""
    try:
        # Check if file exists and is readable
        return file_path.exists() and file_path.is_file()
    except (OSError, PermissionError):
        return False


def validate_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Validate webhook signature"""
    import hmac
    import hashlib
    
    # Create expected signature
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures securely
    return hmac.compare_digest(signature, expected_signature)
