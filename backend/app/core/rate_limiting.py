"""
Rate limiting implementation for AlertAI API

Provides configurable rate limiting with multiple strategies and storage backends.
"""

import time
import json
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, status
from collections import defaultdict, deque

from app.core.config import settings
from app.core.exceptions import RateLimitError
from app.core.logging import security_logger


class InMemoryRateLimiter:
    """In-memory rate limiter using sliding window algorithm"""
    
    def __init__(self):
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.blocked_until: Dict[str, float] = {}
    
    def is_allowed(
        self,
        key: str,
        limit: int,
        window_seconds: int,
        block_duration: int = 60
    ) -> Tuple[bool, Dict[str, any]]:
        """
        Check if request is allowed under rate limit
        
        Args:
            key: Unique identifier for the client
            limit: Maximum number of requests allowed
            window_seconds: Time window in seconds
            block_duration: How long to block after limit exceeded
            
        Returns:
            Tuple[bool, Dict]: (is_allowed, rate_limit_info)
        """
        current_time = time.time()
        
        # Check if client is currently blocked
        if key in self.blocked_until:
            if current_time < self.blocked_until[key]:
                remaining_block_time = int(self.blocked_until[key] - current_time)
                return False, {
                    "allowed": False,
                    "limit": limit,
                    "remaining": 0,
                    "reset_time": int(self.blocked_until[key]),
                    "retry_after": remaining_block_time,
                    "blocked": True
                }
            else:
                # Block period expired, remove from blocked list
                del self.blocked_until[key]
        
        # Clean old requests outside the window
        request_times = self.requests[key]
        cutoff_time = current_time - window_seconds
        
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        # Check if limit is exceeded
        current_requests = len(request_times)
        
        if current_requests >= limit:
            # Block the client
            self.blocked_until[key] = current_time + block_duration
            return False, {
                "allowed": False,
                "limit": limit,
                "remaining": 0,
                "reset_time": int(current_time + window_seconds),
                "retry_after": block_duration,
                "blocked": True
            }
        
        # Add current request
        request_times.append(current_time)
        
        # Calculate reset time (when oldest request will expire)
        reset_time = int(request_times[0] + window_seconds) if request_times else int(current_time + window_seconds)
        
        return True, {
            "allowed": True,
            "limit": limit,
            "remaining": limit - len(request_times),
            "reset_time": reset_time,
            "retry_after": 0,
            "blocked": False
        }
    
    def reset_client(self, key: str) -> None:
        """Reset rate limit for a specific client"""
        if key in self.requests:
            del self.requests[key]
        if key in self.blocked_until:
            del self.blocked_until[key]
    
    def get_stats(self) -> Dict[str, any]:
        """Get rate limiter statistics"""
        current_time = time.time()
        active_clients = len(self.requests)
        blocked_clients = sum(1 for block_time in self.blocked_until.values() if block_time > current_time)
        
        return {
            "active_clients": active_clients,
            "blocked_clients": blocked_clients,
            "total_tracked_clients": len(self.requests) + len(self.blocked_until)
        }


class RateLimitConfig:
    """Rate limit configuration for different endpoints"""
    
    # Default rate limits
    DEFAULT_LIMITS = {
        "global": {"requests": 1000, "window": 3600},  # 1000 requests per hour
        "auth": {"requests": 10, "window": 300},       # 10 auth requests per 5 minutes
        "api": {"requests": 100, "window": 60},        # 100 API requests per minute
        "upload": {"requests": 10, "window": 60},      # 10 uploads per minute
        "chat": {"requests": 20, "window": 60},        # 20 chat requests per minute
    }
    
    @classmethod
    def get_limit_for_endpoint(cls, endpoint: str) -> Dict[str, int]:
        """Get rate limit configuration for specific endpoint"""
        # Map endpoints to rate limit categories
        endpoint_mapping = {
            "/api/auth/": "auth",
            "/api/alerts/": "api",
            "/api/comments/": "api",
            "/api/chat/": "chat",
            "/upload": "upload",
        }
        
        # Find matching category
        for pattern, category in endpoint_mapping.items():
            if pattern in endpoint:
                return cls.DEFAULT_LIMITS.get(category, cls.DEFAULT_LIMITS["api"])
        
        return cls.DEFAULT_LIMITS["api"]


class RateLimitMiddleware:
    """Rate limiting middleware for FastAPI"""
    
    def __init__(self):
        self.limiter = InMemoryRateLimiter()
        self.enabled = not settings.debug  # Disable in debug mode
    
    def get_client_identifier(self, request: Request) -> str:
        """Get unique identifier for the client"""
        # Try to get user ID from token if available
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                from app.core.auth import AuthService
                token = auth_header.split(" ")[1]
                payload = AuthService.verify_token(token)
                user_id = payload.get("sub")
                if user_id:
                    return f"user:{user_id}"
            except:
                pass
        
        # Fallback to IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = getattr(request.client, "host", "unknown") if request.client else "unknown"
        
        return f"ip:{client_ip}"
    
    async def __call__(self, request: Request, call_next):
        """Process request with rate limiting"""
        if not self.enabled:
            return await call_next(request)
        
        # Get client identifier and endpoint
        client_id = self.get_client_identifier(request)
        endpoint = str(request.url.path)
        
        # Get rate limit configuration
        limit_config = RateLimitConfig.get_limit_for_endpoint(endpoint)
        
        # Check rate limit
        allowed, rate_info = self.limiter.is_allowed(
            key=f"{client_id}:{endpoint}",
            limit=limit_config["requests"],
            window_seconds=limit_config["window"]
        )
        
        if not allowed:
            # Log rate limit violation
            security_logger.log_rate_limit_event(
                client_id=client_id,
                endpoint=endpoint,
                limit=limit_config["requests"],
                window=limit_config["window"],
                blocked=True
            )

            # Rate limit exceeded
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "limit": rate_info["limit"],
                    "window_seconds": limit_config["window"],
                    "retry_after": rate_info["retry_after"],
                    "reset_time": rate_info["reset_time"]
                },
                headers={
                    "X-RateLimit-Limit": str(rate_info["limit"]),
                    "X-RateLimit-Remaining": str(rate_info["remaining"]),
                    "X-RateLimit-Reset": str(rate_info["reset_time"]),
                    "Retry-After": str(rate_info["retry_after"])
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset_time"])
        
        return response
    
    def reset_client_limits(self, client_id: str) -> None:
        """Reset rate limits for a specific client"""
        self.limiter.reset_client(client_id)
    
    def get_stats(self) -> Dict[str, any]:
        """Get rate limiting statistics"""
        return self.limiter.get_stats()


# Global rate limiter instance
rate_limiter = RateLimitMiddleware()


def require_rate_limit(
    limit: int,
    window_seconds: int,
    key_suffix: str = ""
):
    """
    Decorator for endpoint-specific rate limiting
    
    Args:
        limit: Maximum number of requests
        window_seconds: Time window in seconds
        key_suffix: Additional key suffix for granular limiting
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract request from kwargs
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # Look in kwargs
                request = kwargs.get('request')
            
            if request and rate_limiter.enabled:
                client_id = rate_limiter.get_client_identifier(request)
                key = f"{client_id}:{func.__name__}{key_suffix}"
                
                allowed, rate_info = rate_limiter.limiter.is_allowed(
                    key=key,
                    limit=limit,
                    window_seconds=window_seconds
                )
                
                if not allowed:
                    raise RateLimitError(
                        f"Rate limit exceeded for {func.__name__}",
                        limit=limit,
                        window=window_seconds,
                        retry_after=rate_info["retry_after"]
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator
