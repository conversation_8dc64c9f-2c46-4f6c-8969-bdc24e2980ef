"""
AlertAI FastAPI Application

Main application entry point with proper modular architecture.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from app.core.config import settings, CORS_SETTINGS, SECURITY_HEADERS
from app.core.rate_limiting import rate_limiter
from app.core.logging import log_startup_event, log_shutdown_event, app_logger
from app.api.v1.router import api_router
from app.db.connection import init_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    
    Args:
        app: FastAPI application instance
    """
    # Startup
    log_startup_event("AlertAI API", "starting")
    app_logger.info("🚀 Starting AlertAI API with authentication and security features")

    # Initialize database
    log_startup_event("Database", "initializing")
    init_db()
    log_startup_event("Database", "ready")

    # Initialize security components
    log_startup_event("Security", "initializing")
    app_logger.info("🔐 Authentication system ready")
    app_logger.info("🛡️ Rate limiting enabled")
    app_logger.info("📊 Audit logging active")
    log_startup_event("Security", "ready")

    # TODO: Initialize AI service connection
    # TODO: Initialize external service connections
    # TODO: Load any required data or cache

    log_startup_event("AlertAI API", "ready")
    app_logger.info("✅ AlertAI API startup complete")
    
    yield
    
    # Shutdown
    log_shutdown_event("AlertAI API", "shutting down")
    app_logger.info("🔄 Shutting down AlertAI API...")

    # TODO: Cleanup resources
    # TODO: Close external connections

    log_shutdown_event("AlertAI API", "complete")
    app_logger.info("✅ AlertAI API shutdown complete")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    # Create FastAPI app
    app = FastAPI(
        title="AlertAI API",
        description="AI-powered alert monitoring and management system",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Configure CORS
    allowed_origins = settings.get_frontend_urls() + [
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
    ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        **CORS_SETTINGS
    )
    
    # Add rate limiting middleware
    if not settings.debug:
        app.middleware("http")(rate_limiter)

    # Add security headers middleware
    if not settings.debug:
        @app.middleware("http")
        async def add_security_headers(request, call_next):
            response = await call_next(request)
            for header, value in SECURITY_HEADERS.items():
                response.headers[header] = value
            return response
    
    # Mount static files for uploads
    app.mount("/uploads", StaticFiles(directory=settings.upload_directory), name="uploads")
    
    # Include API router
    app.include_router(api_router, prefix="/api")
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "AlertAI API is running",
            "version": "1.0.0",
            "docs_url": "/docs" if settings.debug else None
        }
    
    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
