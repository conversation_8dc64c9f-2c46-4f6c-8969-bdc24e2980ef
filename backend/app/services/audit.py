"""
Audit service for logging security and system events
"""

import json
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.database.user import DBAuditLog
from app.core.exceptions import DatabaseError


class AuditService:
    """Service for audit logging and security event tracking"""
    
    @staticmethod
    def log_auth_event(
        db: Session,
        action: str,
        user_id: Optional[int] = None,
        email: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> DBAuditLog:
        """
        Log authentication-related events
        
        Args:
            db: Database session
            action: Action performed (login_success, login_failed, logout, etc.)
            user_id: User ID (if available)
            email: User email
            ip_address: Client IP address
            user_agent: Client user agent
            success: Whether the action was successful
            error_message: Error message if action failed
            additional_data: Additional event data
            
        Returns:
            DBAuditLog: Created audit log entry
        """
        try:
            # Prepare event data
            event_data = {
                "email": email,
                "timestamp": datetime.utcnow().isoformat(),
                "action_type": "authentication"
            }
            
            if additional_data:
                event_data.update(additional_data)
            
            # Create audit log entry
            audit_log = DBAuditLog(
                user_id=user_id,
                action=action,
                resource_type="auth",
                resource_id=email,
                new_values=json.dumps(event_data),
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message
            )
            
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            
            return audit_log
            
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error logging auth event: {str(e)}", "log_auth_event", "audit_logs")
    
    @staticmethod
    def log_resource_event(
        db: Session,
        action: str,
        resource_type: str,
        resource_id: str,
        user_id: Optional[int] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> DBAuditLog:
        """
        Log resource-related events (CRUD operations)
        
        Args:
            db: Database session
            action: Action performed (create, read, update, delete)
            resource_type: Type of resource (alert, comment, file, etc.)
            resource_id: Resource identifier
            user_id: User ID performing the action
            old_values: Previous values (for updates)
            new_values: New values
            ip_address: Client IP address
            user_agent: Client user agent
            success: Whether the action was successful
            error_message: Error message if action failed
            
        Returns:
            DBAuditLog: Created audit log entry
        """
        try:
            # Create audit log entry
            audit_log = DBAuditLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=str(resource_id),
                old_values=json.dumps(old_values) if old_values else None,
                new_values=json.dumps(new_values) if new_values else None,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message
            )
            
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            
            return audit_log
            
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error logging resource event: {str(e)}", "log_resource_event", "audit_logs")
    
    @staticmethod
    def log_system_event(
        db: Session,
        action: str,
        user_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> DBAuditLog:
        """
        Log system-level events
        
        Args:
            db: Database session
            action: Action performed (system_start, config_change, etc.)
            user_id: User ID (if applicable)
            details: Event details
            ip_address: Client IP address
            user_agent: Client user agent
            success: Whether the action was successful
            error_message: Error message if action failed
            
        Returns:
            DBAuditLog: Created audit log entry
        """
        try:
            # Create audit log entry
            audit_log = DBAuditLog(
                user_id=user_id,
                action=action,
                resource_type="system",
                resource_id="system",
                new_values=json.dumps(details) if details else None,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message
            )
            
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            
            return audit_log
            
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error logging system event: {str(e)}", "log_system_event", "audit_logs")
    
    @staticmethod
    def get_audit_logs(
        db: Session,
        user_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        action: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        success_only: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> list[DBAuditLog]:
        """
        Get audit logs with filtering
        
        Args:
            db: Database session
            user_id: Filter by user ID
            resource_type: Filter by resource type
            action: Filter by action
            start_date: Filter by start date
            end_date: Filter by end date
            success_only: Filter by success status
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            list[DBAuditLog]: List of audit log entries
        """
        try:
            query = db.query(DBAuditLog)
            
            if user_id:
                query = query.filter(DBAuditLog.user_id == user_id)
            
            if resource_type:
                query = query.filter(DBAuditLog.resource_type == resource_type)
            
            if action:
                query = query.filter(DBAuditLog.action == action)
            
            if start_date:
                query = query.filter(DBAuditLog.created_at >= start_date)
            
            if end_date:
                query = query.filter(DBAuditLog.created_at <= end_date)
            
            if success_only is not None:
                query = query.filter(DBAuditLog.success == success_only)
            
            return query.order_by(DBAuditLog.created_at.desc()).offset(offset).limit(limit).all()
            
        except Exception as e:
            raise DatabaseError(f"Error fetching audit logs: {str(e)}", "get_audit_logs", "audit_logs")
    
    @staticmethod
    def get_audit_logs_count(
        db: Session,
        user_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        action: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        success_only: Optional[bool] = None
    ) -> int:
        """
        Get count of audit logs with filtering
        
        Args:
            db: Database session
            user_id: Filter by user ID
            resource_type: Filter by resource type
            action: Filter by action
            start_date: Filter by start date
            end_date: Filter by end date
            success_only: Filter by success status
            
        Returns:
            int: Count of matching audit log entries
        """
        try:
            query = db.query(DBAuditLog)
            
            if user_id:
                query = query.filter(DBAuditLog.user_id == user_id)
            
            if resource_type:
                query = query.filter(DBAuditLog.resource_type == resource_type)
            
            if action:
                query = query.filter(DBAuditLog.action == action)
            
            if start_date:
                query = query.filter(DBAuditLog.created_at >= start_date)
            
            if end_date:
                query = query.filter(DBAuditLog.created_at <= end_date)
            
            if success_only is not None:
                query = query.filter(DBAuditLog.success == success_only)
            
            return query.count()
            
        except Exception as e:
            raise DatabaseError(f"Error counting audit logs: {str(e)}", "get_audit_logs_count", "audit_logs")
    
    @staticmethod
    def create_integrity_checksum(data: Dict[str, Any]) -> str:
        """
        Create integrity checksum for audit log data
        
        Args:
            data: Data to create checksum for
            
        Returns:
            str: SHA-256 checksum
        """
        # Sort data to ensure consistent checksums
        sorted_data = json.dumps(data, sort_keys=True)
        return hashlib.sha256(sorted_data.encode()).hexdigest()
    
    @staticmethod
    def verify_integrity_checksum(data: Dict[str, Any], checksum: str) -> bool:
        """
        Verify integrity checksum for audit log data
        
        Args:
            data: Data to verify
            checksum: Expected checksum
            
        Returns:
            bool: True if checksum matches
        """
        calculated_checksum = AuditService.create_integrity_checksum(data)
        return calculated_checksum == checksum
