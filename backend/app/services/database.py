"""
Database service for AlertAI

Provides database operations and data access layer.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from app.models.database.alert import DBAlert
from app.models.schemas.alert import Alert
from app.core.exceptions import NotFoundError, DatabaseError


class DatabaseService:
    """Service layer for database operations"""

    @staticmethod
    def get_alerts(
        db: Session,
        limit: int = 100,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False
    ) -> List[DBAlert]:
        """
        Get alerts with pagination and filtering
        
        Args:
            db: Database session
            limit: Maximum number of alerts to return
            offset: Number of alerts to skip
            filters: Filter criteria
            include_deleted: Whether to include deleted alerts
            
        Returns:
            List[DBAlert]: List of alert database models
        """
        try:
            query = db.query(DBAlert)
            
            if not include_deleted:
                query = query.filter(DBAlert.is_deleted == False)
            
            # Apply filters if provided
            if filters:
                if filters.get("search"):
                    search_term = f"%{filters['search']}%"
                    query = query.filter(
                        DBAlert.title.ilike(search_term) |
                        DBAlert.description.ilike(search_term)
                    )
                
                if filters.get("severity"):
                    query = query.filter(DBAlert.severity == filters["severity"])
                
                if filters.get("status"):
                    query = query.filter(DBAlert.status == filters["status"])
                
                if filters.get("source"):
                    query = query.filter(DBAlert.source == filters["source"])
                
                if filters.get("assigned_to"):
                    query = query.filter(DBAlert.assigned_analyst_name == filters["assigned_to"])
            
            return query.order_by(DBAlert.id.desc()).limit(limit).offset(offset).all()
            
        except Exception as e:
            raise DatabaseError(f"Error fetching alerts: {str(e)}", "get_alerts", "alerts")

    @staticmethod
    def get_alerts_count(
        db: Session,
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False
    ) -> int:
        """
        Get total count of alerts
        
        Args:
            db: Database session
            filters: Filter criteria
            include_deleted: Whether to include deleted alerts
            
        Returns:
            int: Total count of alerts
        """
        try:
            query = db.query(DBAlert)
            
            if not include_deleted:
                query = query.filter(DBAlert.is_deleted == False)
            
            # Apply same filters as get_alerts
            if filters:
                if filters.get("search"):
                    search_term = f"%{filters['search']}%"
                    query = query.filter(
                        DBAlert.title.ilike(search_term) |
                        DBAlert.description.ilike(search_term)
                    )
                
                if filters.get("severity"):
                    query = query.filter(DBAlert.severity == filters["severity"])
                
                if filters.get("status"):
                    query = query.filter(DBAlert.status == filters["status"])
                
                if filters.get("source"):
                    query = query.filter(DBAlert.source == filters["source"])
                
                if filters.get("assigned_to"):
                    query = query.filter(DBAlert.assigned_analyst_name == filters["assigned_to"])
            
            return query.count()
            
        except Exception as e:
            raise DatabaseError(f"Error counting alerts: {str(e)}", "get_alerts_count", "alerts")

    @staticmethod
    def get_alert_by_id(db: Session, alert_id: int) -> Optional[DBAlert]:
        """
        Get a specific alert by ID
        
        Args:
            db: Database session
            alert_id: Alert ID
            
        Returns:
            Optional[DBAlert]: Alert database model or None
        """
        try:
            return db.query(DBAlert).filter(
                DBAlert.id == alert_id,
                DBAlert.is_deleted == False
            ).first()
            
        except Exception as e:
            raise DatabaseError(f"Error fetching alert {alert_id}: {str(e)}", "get_alert_by_id", "alerts")

    @staticmethod
    def create_alert(
        db: Session,
        title: str,
        description: str,
        severity: str,
        source: str,
        status: str = "open",
        **kwargs
    ) -> DBAlert:
        """
        Create a new alert
        
        Args:
            db: Database session
            title: Alert title
            description: Alert description
            severity: Alert severity
            source: Alert source
            status: Alert status
            **kwargs: Additional alert fields
            
        Returns:
            DBAlert: Created alert database model
        """
        try:
            db_alert = DBAlert(
                title=title,
                description=description,
                severity=severity,
                source=source,
                status=status,
                **kwargs
            )
            
            db.add(db_alert)
            db.commit()
            db.refresh(db_alert)
            
            return db_alert
            
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error creating alert: {str(e)}", "create_alert", "alerts")

    @staticmethod
    def update_alert(
        db: Session,
        alert_id: int,
        update_data: Dict[str, Any],
        **kwargs
    ) -> DBAlert:
        """
        Update an existing alert
        
        Args:
            db: Database session
            alert_id: Alert ID
            update_data: Fields to update
            **kwargs: Additional metadata
            
        Returns:
            DBAlert: Updated alert database model
        """
        try:
            db_alert = DatabaseService.get_alert_by_id(db, alert_id)
            if not db_alert:
                raise NotFoundError("Alert", alert_id)
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(db_alert, field):
                    setattr(db_alert, field, value)
            
            db_alert.updated_at = datetime.utcnow()
            
            db.commit()
            db.refresh(db_alert)
            
            return db_alert
            
        except NotFoundError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error updating alert {alert_id}: {str(e)}", "update_alert", "alerts")

    @staticmethod
    def delete_alert(
        db: Session,
        alert_id: int,
        permanent: bool = False,
        **kwargs
    ) -> bool:
        """
        Delete an alert (soft delete by default)
        
        Args:
            db: Database session
            alert_id: Alert ID
            permanent: Whether to permanently delete
            **kwargs: Additional metadata
            
        Returns:
            bool: True if successful
        """
        try:
            db_alert = DatabaseService.get_alert_by_id(db, alert_id)
            if not db_alert:
                raise NotFoundError("Alert", alert_id)
            
            if permanent:
                db.delete(db_alert)
            else:
                db_alert.is_deleted = True
                db_alert.updated_at = datetime.utcnow()
            
            db.commit()
            return True
            
        except NotFoundError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error deleting alert {alert_id}: {str(e)}", "delete_alert", "alerts")

    @staticmethod
    def db_alert_to_pydantic(db_alert: DBAlert) -> Alert:
        """
        Convert database alert model to Pydantic schema
        
        Args:
            db_alert: Database alert model
            
        Returns:
            Alert: Pydantic alert schema
        """
        return Alert(
            id=db_alert.id,
            title=db_alert.title,
            description=db_alert.description,
            severity=db_alert.severity,
            status=db_alert.status,
            source=db_alert.source,
            created_at=db_alert.created_at,
            updated_at=db_alert.updated_at,
            assigned_analyst_id=db_alert.assigned_analyst_id,
            assigned_analyst_name=db_alert.assigned_analyst_name,
            tags=db_alert.tags or [],
            due_date=db_alert.due_date,
            priority=db_alert.priority,
            investigation_notes=db_alert.investigation_notes,
            mitre_techniques=db_alert.mitre_techniques or [],
            escalation_level=db_alert.escalation_level,
            sla_deadline=db_alert.sla_deadline,
            is_deleted=db_alert.is_deleted
        )
