"""
User service for managing users, roles, and permissions
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.models.database.user import DBUser, DBRole, DBPermission, DBUserSession, DBAuditLog, DBAPIKey
from app.core.auth import AuthService
from app.core.exceptions import NotFoundError, ValidationError, DatabaseError


class UserService:
    """Service for user management operations"""
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[DBUser]:
        """Get user by ID"""
        try:
            return db.query(DBUser).filter(DBUser.id == user_id, DBUser.is_active == True).first()
        except Exception as e:
            raise DatabaseError(f"Error fetching user {user_id}: {str(e)}", "get_user_by_id", "users")
    
    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[DBUser]:
        """Get user by email"""
        try:
            return db.query(DBUser).filter(DBUser.email == email, DBUser.is_active == True).first()
        except Exception as e:
            raise DatabaseError(f"Error fetching user by email: {str(e)}", "get_user_by_email", "users")
    
    @staticmethod
    def get_users(db: Session, skip: int = 0, limit: int = 100, include_inactive: bool = False) -> List[DBUser]:
        """Get list of users"""
        try:
            query = db.query(DBUser)
            if not include_inactive:
                query = query.filter(DBUser.is_active == True)
            return query.offset(skip).limit(limit).all()
        except Exception as e:
            raise DatabaseError(f"Error fetching users: {str(e)}", "get_users", "users")
    
    @staticmethod
    def create_user(
        db: Session,
        email: str,
        password: str,
        full_name: str,
        is_superuser: bool = False,
        is_active: bool = True,
        **kwargs
    ) -> DBUser:
        """Create a new user"""
        try:
            # Check if user already exists
            existing_user = UserService.get_user_by_email(db, email)
            if existing_user:
                raise ValidationError("User with this email already exists", "email", email)
            
            # Hash password
            password_hash = AuthService.get_password_hash(password)
            
            # Create user
            db_user = DBUser(
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                is_superuser=is_superuser,
                is_active=is_active,
                **kwargs
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
            
            return db_user
            
        except ValidationError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error creating user: {str(e)}", "create_user", "users")
    
    @staticmethod
    def update_user(db: Session, user_id: int, update_data: dict) -> DBUser:
        """Update user information"""
        try:
            user = UserService.get_user_by_id(db, user_id)
            if not user:
                raise NotFoundError("User", user_id)
            
            # Handle password update separately
            if "password" in update_data:
                update_data["password_hash"] = AuthService.get_password_hash(update_data.pop("password"))
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(user, field):
                    setattr(user, field, value)
            
            user.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(user)
            
            return user
            
        except NotFoundError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error updating user {user_id}: {str(e)}", "update_user", "users")
    
    @staticmethod
    def deactivate_user(db: Session, user_id: int) -> bool:
        """Deactivate a user (soft delete)"""
        try:
            user = UserService.get_user_by_id(db, user_id)
            if not user:
                raise NotFoundError("User", user_id)
            
            user.is_active = False
            user.updated_at = datetime.utcnow()
            db.commit()
            
            return True
            
        except NotFoundError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error deactivating user {user_id}: {str(e)}", "deactivate_user", "users")
    
    # Role management
    @staticmethod
    def get_role_by_name(db: Session, name: str) -> Optional[DBRole]:
        """Get role by name"""
        try:
            return db.query(DBRole).filter(DBRole.name == name, DBRole.is_active == True).first()
        except Exception as e:
            raise DatabaseError(f"Error fetching role by name: {str(e)}", "get_role_by_name", "roles")
    
    @staticmethod
    def create_role(db: Session, name: str, description: str = None, is_system_role: bool = False) -> DBRole:
        """Create a new role"""
        try:
            # Check if role already exists
            existing_role = UserService.get_role_by_name(db, name)
            if existing_role:
                raise ValidationError("Role with this name already exists", "name", name)
            
            db_role = DBRole(
                name=name,
                description=description,
                is_system_role=is_system_role
            )
            
            db.add(db_role)
            db.commit()
            db.refresh(db_role)
            
            return db_role
            
        except ValidationError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error creating role: {str(e)}", "create_role", "roles")
    
    @staticmethod
    def assign_role_to_user(db: Session, user_id: int, role_name: str) -> bool:
        """Assign a role to a user"""
        try:
            user = UserService.get_user_by_id(db, user_id)
            if not user:
                raise NotFoundError("User", user_id)
            
            role = UserService.get_role_by_name(db, role_name)
            if not role:
                raise NotFoundError("Role", role_name)
            
            if role not in user.roles:
                user.roles.append(role)
                db.commit()
            
            return True
            
        except NotFoundError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error assigning role to user: {str(e)}", "assign_role_to_user", "users")
    
    # Permission management
    @staticmethod
    def get_permission_by_name(db: Session, name: str) -> Optional[DBPermission]:
        """Get permission by name"""
        try:
            return db.query(DBPermission).filter(DBPermission.name == name, DBPermission.is_active == True).first()
        except Exception as e:
            raise DatabaseError(f"Error fetching permission by name: {str(e)}", "get_permission_by_name", "permissions")
    
    @staticmethod
    def create_permission(
        db: Session,
        name: str,
        description: str = None,
        resource_type: str = None
    ) -> DBPermission:
        """Create a new permission"""
        try:
            # Check if permission already exists
            existing_permission = UserService.get_permission_by_name(db, name)
            if existing_permission:
                raise ValidationError("Permission with this name already exists", "name", name)
            
            db_permission = DBPermission(
                name=name,
                description=description,
                resource_type=resource_type
            )
            
            db.add(db_permission)
            db.commit()
            db.refresh(db_permission)
            
            return db_permission
            
        except ValidationError:
            raise
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error creating permission: {str(e)}", "create_permission", "permissions")
    
    # Session management
    @staticmethod
    def create_user_session(
        db: Session,
        user_id: int,
        session_token: str,
        ip_address: str = None,
        user_agent: str = None,
        expires_in_hours: int = 24
    ) -> DBUserSession:
        """Create a new user session"""
        try:
            expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
            
            db_session = DBUserSession(
                user_id=user_id,
                session_token=session_token,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )
            
            db.add(db_session)
            db.commit()
            db.refresh(db_session)
            
            return db_session
            
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error creating user session: {str(e)}", "create_user_session", "user_sessions")
    
    @staticmethod
    def get_active_session(db: Session, session_token: str) -> Optional[DBUserSession]:
        """Get active session by token"""
        try:
            return db.query(DBUserSession).filter(
                DBUserSession.session_token == session_token,
                DBUserSession.is_active == True,
                DBUserSession.expires_at > datetime.utcnow()
            ).first()
        except Exception as e:
            raise DatabaseError(f"Error fetching session: {str(e)}", "get_active_session", "user_sessions")
    
    @staticmethod
    def invalidate_session(db: Session, session_token: str) -> bool:
        """Invalidate a user session"""
        try:
            session = UserService.get_active_session(db, session_token)
            if session:
                session.is_active = False
                db.commit()
                return True
            return False
        except Exception as e:
            db.rollback()
            raise DatabaseError(f"Error invalidating session: {str(e)}", "invalidate_session", "user_sessions")
