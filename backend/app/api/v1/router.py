"""
Main API router for version 1

Combines all endpoint routers into a single API router.
"""

from fastapi import APIRouter

from .endpoints import (
    alerts,
    comments,
    chat,
    files,
    sources,
    logs,
    health,
    auth,
    audit
)

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    audit.router,
    prefix="/audit",
    tags=["audit"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

api_router.include_router(
    alerts.router,
    prefix="/alerts",
    tags=["alerts"]
)

api_router.include_router(
    comments.router,
    prefix="/alerts",
    tags=["comments"]
)

api_router.include_router(
    files.router,
    prefix="/alerts",
    tags=["files"]
)

api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["chat"]
)

api_router.include_router(
    sources.router,
    prefix="/sources",
    tags=["sources"]
)

api_router.include_router(
    logs.router,
    prefix="/logs",
    tags=["logs"]
)
