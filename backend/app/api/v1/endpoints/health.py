"""
Health check endpoints for AlertAI API
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from datetime import datetime

from app.api.deps import get_db
from app.core.config import settings

router = APIRouter()


@router.get("/")
async def health_check():
    """
    Basic health check endpoint
    
    Returns:
        dict: Health status information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "service": "AlertAI API"
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """
    Detailed health check including database connectivity
    
    Args:
        db: Database session
        
    Returns:
        dict: Detailed health status information
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "service": "AlertAI API",
        "components": {}
    }
    
    # Check database connectivity
    try:
        # Simple query to test database connection
        db.execute("SELECT 1")
        health_status["components"]["database"] = {
            "status": "healthy",
            "url": settings.database_url.split("://")[0] + "://***"  # Hide credentials
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Check AI service (placeholder)
    try:
        # TODO: Add actual AI service health check
        health_status["components"]["ai_service"] = {
            "status": "healthy",
            "url": settings.ollama_base_url
        }
    except Exception as e:
        health_status["components"]["ai_service"] = {
            "status": "unhealthy", 
            "error": str(e)
        }
    
    return health_status


@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)):
    """
    Readiness check for Kubernetes/container orchestration
    
    Args:
        db: Database session
        
    Returns:
        dict: Readiness status
    """
    try:
        # Test database connection
        db.execute("SELECT 1")
        return {"status": "ready"}
    except Exception:
        return {"status": "not ready"}, 503


@router.get("/live")
async def liveness_check():
    """
    Liveness check for Kubernetes/container orchestration
    
    Returns:
        dict: Liveness status
    """
    return {"status": "alive"}
