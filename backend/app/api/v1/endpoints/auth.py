"""
Authentication endpoints for AlertAI API

Handles user login, logout, registration, and token management.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_request_info
from app.core.auth import AuthService, RoleManager
from app.core.config import settings
from app.core.exceptions import AuthenticationError, ValidationError
from app.core.security import sanitize_input
from app.core.logging import security_logger, app_logger
from app.models.schemas.auth import (
    Token,
    TokenData,
    UserCreate,
    UserResponse,
    UserLogin,
    PasswordChange,
    PasswordReset
)
from app.services.user import UserService
from app.services.audit import AuditService

router = APIRouter()

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/token")


@router.post("/token", response_model=Token)
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests
    
    Args:
        request: FastAPI request object
        form_data: OAuth2 form data with username and password
        db: Database session
        
    Returns:
        Token: Access token and token type
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Sanitize input
        email = sanitize_input(form_data.username, 255)
        password = form_data.password
        
        # Authenticate user
        user = AuthService.authenticate_user(db, email, password)
        if not user:
            # Log failed login attempt
            security_logger.log_auth_event(
                event_type="login_failed",
                email=email,
                ip_address=request_info["ip_address"],
                user_agent=request_info["user_agent"],
                success=False,
                error_message="Invalid credentials"
            )
            AuditService.log_auth_event(
                db=db,
                action="login_failed",
                email=email,
                ip_address=request_info["ip_address"],
                user_agent=request_info["user_agent"],
                success=False,
                error_message="Invalid credentials"
            )
            app_logger.warning(f"Failed login attempt for {email} from {request_info['ip_address']}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = AuthService.create_access_token(
            data={"sub": user.id, "email": user.email},
            expires_delta=access_token_expires
        )
        
        # Update last login
        user.last_login = datetime.now(timezone.utc)
        user.failed_login_attempts = 0
        db.commit()
        
        # Create user session
        UserService.create_user_session(
            db=db,
            user_id=user.id,
            session_token=access_token,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"]
        )
        
        # Log successful login
        security_logger.log_auth_event(
            event_type="login_success",
            user_id=user.id,
            email=user.email,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            success=True
        )
        AuditService.log_auth_event(
            db=db,
            action="login_success",
            user_id=user.id,
            email=user.email,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            success=True
        )
        app_logger.info(f"Successful login for {user.email} from {request_info['ip_address']}")
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during authentication: {str(e)}"
        )


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Register a new user
    
    Args:
        user_data: User registration data
        request: FastAPI request object
        db: Database session
        
    Returns:
        UserResponse: Created user information
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Sanitize input
        email = sanitize_input(user_data.email, 255)
        full_name = sanitize_input(user_data.full_name, 255)
        
        # Validate email format
        if not email or "@" not in email:
            raise ValidationError("Invalid email format", "email", email)
        
        # Create user
        user = UserService.create_user(
            db=db,
            email=email,
            password=user_data.password,
            full_name=full_name,
            phone=user_data.phone,
            department=user_data.department,
            title=user_data.title
        )
        
        # Assign default role
        UserService.assign_role_to_user(db, user.id, "analyst")
        
        # Log user creation
        AuditService.log_auth_event(
            db=db,
            action="user_registered",
            user_id=user.id,
            email=user.email,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            success=True
        )
        
        return UserResponse(
            id=user.id,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            roles=AuthService.get_user_roles(user),
            permissions=AuthService.get_user_permissions(user),
            created_at=user.created_at
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=e.message)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during registration: {str(e)}"
        )


@router.post("/logout")
async def logout(
    request: Request,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Logout user and invalidate token
    
    Args:
        request: FastAPI request object
        token: JWT access token
        db: Database session
        
    Returns:
        dict: Logout confirmation
    """
    try:
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Get current user
        user = AuthService.get_current_user(db, token)
        
        # Invalidate session
        UserService.invalidate_session(db, token)
        
        # Log logout
        AuditService.log_auth_event(
            db=db,
            action="logout",
            user_id=user.id,
            email=user.email,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            success=True
        )
        
        return {"message": "Successfully logged out"}
        
    except AuthenticationError as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=e.message)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during logout: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get current user information
    
    Args:
        token: JWT access token
        db: Database session
        
    Returns:
        UserResponse: Current user information
    """
    try:
        user = AuthService.get_current_user(db, token)
        
        return UserResponse(
            id=user.id,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            roles=AuthService.get_user_roles(user),
            permissions=AuthService.get_user_permissions(user),
            created_at=user.created_at,
            last_login=user.last_login,
            phone=user.phone,
            department=user.department,
            title=user.title,
            timezone=user.timezone,
            theme_preference=user.theme_preference
        )
        
    except AuthenticationError as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=e.message)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching user info: {str(e)}"
        )


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    request: Request,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Change user password
    
    Args:
        password_data: Password change data
        request: FastAPI request object
        token: JWT access token
        db: Database session
        
    Returns:
        dict: Password change confirmation
    """
    try:
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Get current user
        user = AuthService.get_current_user(db, token)
        
        # Verify current password
        if not AuthService.verify_password(password_data.current_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        UserService.update_user(db, user.id, {"password": password_data.new_password})
        
        # Log password change
        AuditService.log_auth_event(
            db=db,
            action="password_changed",
            user_id=user.id,
            email=user.email,
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            success=True
        )
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error changing password: {str(e)}"
        )


@router.post("/init-system")
async def initialize_system(
    admin_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Initialize system with default roles and admin user
    This endpoint should only be available during initial setup
    
    Args:
        admin_data: Admin user creation data
        db: Database session
        
    Returns:
        dict: Initialization confirmation
    """
    try:
        # Check if system is already initialized
        existing_users = UserService.get_users(db, limit=1)
        if existing_users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="System is already initialized"
            )
        
        # Create default roles and permissions
        RoleManager.create_default_roles(db)
        
        # Create admin user
        admin_user = RoleManager.create_default_admin_user(
            db=db,
            email=admin_data.email,
            password=admin_data.password
        )
        
        return {
            "message": "System initialized successfully",
            "admin_user_id": admin_user.id,
            "admin_email": admin_user.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error initializing system: {str(e)}"
        )
