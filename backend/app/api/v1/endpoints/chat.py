"""
Chat endpoints for AlertAI API

Handles AI chat functionality and session management.
"""

from fastapi import APIRouter

router = APIRouter()

# TODO: Implement chat endpoints
# - POST / - General chat with AI
# - POST /alerts/{alert_id} - Alert-specific chat
# - GET /sessions - Get chat sessions
# - POST /sessions - Create new chat session
# - GET /sessions/{session_id} - Get chat session with messages
# - PUT /sessions/{session_id}/messages/{message_id} - Edit message
