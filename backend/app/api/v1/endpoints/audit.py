"""
Audit log endpoints for AlertAI API

Provides access to audit logs for security monitoring and compliance.
"""

from typing import Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db, require_authentication, validate_admin_access, get_pagination_params
from app.core.auth import AuthService
from app.core.exceptions import AuthorizationError
from app.models.schemas.audit import AuditLogResponse, AuditLogStats, AuditLogExport
from app.services.audit import AuditService
from app.models.database.user import DBUser

router = APIRouter()


@router.get("/", response_model=AuditLogResponse)
async def get_audit_logs(
    request: Request,
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    action: Optional[str] = Query(None, description="Filter by action"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    success_only: Optional[bool] = Query(None, description="Filter by success status"),
    pagination: dict = Depends(get_pagination_params),
    current_user: DBUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Get audit logs with filtering and pagination
    
    Args:
        request: FastAPI request object
        user_id: Filter by user ID
        resource_type: Filter by resource type (alerts, comments, etc.)
        action: Filter by action (create, update, delete, etc.)
        start_date: Filter by start date
        end_date: Filter by end date
        success_only: Filter by success status
        pagination: Pagination parameters
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        AuditLogResponse: Paginated audit logs
        
    Raises:
        AuthorizationError: If user doesn't have audit access
    """
    # Check permissions - only admins or users viewing their own logs
    if not current_user.is_superuser:
        if not AuthService.check_permission(current_user, "audit:read"):
            if user_id != current_user.id:
                raise AuthorizationError("Can only view your own audit logs")
        
        # Non-admin users can only see their own logs
        user_id = current_user.id
    
    try:
        # Get audit logs
        logs = AuditService.get_audit_logs(
            db=db,
            user_id=user_id,
            resource_type=resource_type,
            action=action,
            start_date=start_date,
            end_date=end_date,
            success_only=success_only,
            limit=pagination["limit"],
            offset=pagination["offset"]
        )
        
        # Get total count
        total_count = AuditService.get_audit_logs_count(
            db=db,
            user_id=user_id,
            resource_type=resource_type,
            action=action,
            start_date=start_date,
            end_date=end_date,
            success_only=success_only
        )
        
        # Convert to response format
        audit_logs = []
        for log in logs:
            audit_logs.append({
                "id": log.id,
                "user_id": log.user_id,
                "action": log.action,
                "resource_type": log.resource_type,
                "resource_id": log.resource_id,
                "old_values": log.old_values,
                "new_values": log.new_values,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "success": log.success,
                "error_message": log.error_message,
                "created_at": log.created_at
            })
        
        return AuditLogResponse(
            logs=audit_logs,
            total=total_count,
            limit=pagination["limit"],
            offset=pagination["offset"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching audit logs: {str(e)}")


@router.get("/stats", response_model=AuditLogStats)
async def get_audit_stats(
    days: int = Query(30, description="Number of days to analyze", ge=1, le=365),
    current_user: DBUser = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Get audit log statistics
    
    Args:
        days: Number of days to analyze
        current_user: Current authenticated admin user
        db: Database session
        
    Returns:
        AuditLogStats: Audit statistics
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get total events
        total_events = AuditService.get_audit_logs_count(
            db=db,
            start_date=start_date,
            end_date=end_date
        )
        
        # Get successful events
        successful_events = AuditService.get_audit_logs_count(
            db=db,
            start_date=start_date,
            end_date=end_date,
            success_only=True
        )
        
        # Get failed events
        failed_events = total_events - successful_events
        
        # Get authentication events
        auth_events = AuditService.get_audit_logs_count(
            db=db,
            resource_type="auth",
            start_date=start_date,
            end_date=end_date
        )
        
        # Get resource events by type
        resource_stats = {}
        for resource_type in ["alerts", "comments", "files", "users", "system"]:
            count = AuditService.get_audit_logs_count(
                db=db,
                resource_type=resource_type,
                start_date=start_date,
                end_date=end_date
            )
            if count > 0:
                resource_stats[resource_type] = count
        
        # Calculate success rate
        success_rate = (successful_events / total_events * 100) if total_events > 0 else 0
        
        return AuditLogStats(
            total_events=total_events,
            successful_events=successful_events,
            failed_events=failed_events,
            success_rate=round(success_rate, 2),
            auth_events=auth_events,
            resource_stats=resource_stats,
            period_days=days,
            start_date=start_date,
            end_date=end_date
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating audit stats: {str(e)}")


@router.get("/actions")
async def get_available_actions(
    current_user: DBUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Get list of available audit actions for filtering
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        dict: Available actions and resource types
    """
    try:
        # Get distinct actions and resource types from audit logs
        from sqlalchemy import distinct
        from app.models.database.user import DBAuditLog
        
        actions = db.query(distinct(DBAuditLog.action)).filter(
            DBAuditLog.action.isnot(None)
        ).all()
        
        resource_types = db.query(distinct(DBAuditLog.resource_type)).filter(
            DBAuditLog.resource_type.isnot(None)
        ).all()
        
        return {
            "actions": [action[0] for action in actions],
            "resource_types": [rt[0] for rt in resource_types]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching audit actions: {str(e)}")


@router.post("/export", response_model=AuditLogExport)
async def export_audit_logs(
    export_request: dict,
    current_user: DBUser = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Export audit logs to various formats
    
    Args:
        export_request: Export configuration
        current_user: Current authenticated admin user
        db: Database session
        
    Returns:
        AuditLogExport: Export information
    """
    try:
        # Extract export parameters
        format_type = export_request.get("format", "json")  # json, csv, xlsx
        start_date = export_request.get("start_date")
        end_date = export_request.get("end_date")
        filters = export_request.get("filters", {})
        
        if start_date:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Get audit logs for export (no pagination limit)
        logs = AuditService.get_audit_logs(
            db=db,
            user_id=filters.get("user_id"),
            resource_type=filters.get("resource_type"),
            action=filters.get("action"),
            start_date=start_date,
            end_date=end_date,
            success_only=filters.get("success_only"),
            limit=10000,  # Large limit for export
            offset=0
        )
        
        # Generate export file
        import json
        import tempfile
        import os
        
        if format_type == "json":
            export_data = []
            for log in logs:
                export_data.append({
                    "id": log.id,
                    "user_id": log.user_id,
                    "action": log.action,
                    "resource_type": log.resource_type,
                    "resource_id": log.resource_id,
                    "old_values": log.old_values,
                    "new_values": log.new_values,
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                    "success": log.success,
                    "error_message": log.error_message,
                    "created_at": log.created_at.isoformat()
                })
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(export_data, f, indent=2)
                temp_path = f.name
            
            file_size = os.path.getsize(temp_path)
            
            # Log export action
            AuditService.log_system_event(
                db=db,
                action="audit_export",
                user_id=current_user.id,
                details={
                    "format": format_type,
                    "record_count": len(logs),
                    "file_size": file_size,
                    "filters": filters
                },
                success=True
            )
            
            return AuditLogExport(
                file_path=temp_path,
                format=format_type,
                record_count=len(logs),
                file_size=file_size,
                created_at=datetime.utcnow()
            )
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported export format: {format_type}")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting audit logs: {str(e)}")


@router.delete("/cleanup")
async def cleanup_old_logs(
    days: int = Query(90, description="Delete logs older than this many days", ge=30),
    current_user: DBUser = Depends(validate_admin_access),
    db: Session = Depends(get_db)
):
    """
    Clean up old audit logs
    
    Args:
        days: Delete logs older than this many days
        current_user: Current authenticated admin user
        db: Database session
        
    Returns:
        dict: Cleanup results
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Count logs to be deleted
        from app.models.database.user import DBAuditLog
        logs_to_delete = db.query(DBAuditLog).filter(
            DBAuditLog.created_at < cutoff_date
        ).count()
        
        # Delete old logs
        deleted_count = db.query(DBAuditLog).filter(
            DBAuditLog.created_at < cutoff_date
        ).delete()
        
        db.commit()
        
        # Log cleanup action
        AuditService.log_system_event(
            db=db,
            action="audit_cleanup",
            user_id=current_user.id,
            details={
                "cutoff_date": cutoff_date.isoformat(),
                "deleted_count": deleted_count,
                "retention_days": days
            },
            success=True
        )
        
        return {
            "message": f"Successfully deleted {deleted_count} old audit logs",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error cleaning up audit logs: {str(e)}")
