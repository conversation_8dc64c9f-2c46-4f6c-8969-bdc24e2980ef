"""
Alert endpoints for AlertAI API

Handles CRUD operations for alerts with proper validation and error handling.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_pagination_params, get_filter_params, get_request_info
from app.core.exceptions import NotFoundError, ValidationError
from app.core.security import sanitize_input
from app.models.schemas.alert import (
    Alert,
    AlertResponse,
    AlertCreateRequest,
    AlertUpdateRequest,
    AlertDeleteResponse
)
from app.services.database import DatabaseService

router = APIRouter()


@router.get("/", response_model=AlertResponse)
async def get_alerts(
    request: Request,
    pagination: dict = Depends(get_pagination_params),
    filters: dict = Depends(get_filter_params),
    db: Session = Depends(get_db)
):
    """
    Get alerts with pagination and filtering
    
    Args:
        request: FastAPI request object
        pagination: Pagination parameters (limit, offset)
        filters: Filter parameters (search, severity, status, etc.)
        db: Database session
        
    Returns:
        AlertResponse: List of alerts with total count
        
    Raises:
        HTTPException: If database error occurs
    """
    try:
        # Get alerts with pagination and filters
        db_alerts = DatabaseService.get_alerts(
            db=db,
            limit=pagination["limit"],
            offset=pagination["offset"],
            filters=filters
        )
        
        # Convert to API schema
        alerts = [DatabaseService.db_alert_to_pydantic(db_alert) for db_alert in db_alerts]
        
        # Get total count for pagination
        total_count = DatabaseService.get_alerts_count(db=db, filters=filters)
        
        return AlertResponse(alerts=alerts, total=total_count)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching alerts: {str(e)}")


@router.get("/{alert_id}", response_model=Alert)
async def get_alert(
    alert_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific alert by ID
    
    Args:
        alert_id: Alert ID
        db: Database session
        
    Returns:
        Alert: Alert details
        
    Raises:
        NotFoundError: If alert doesn't exist
    """
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise NotFoundError("Alert", alert_id)
    
    return DatabaseService.db_alert_to_pydantic(db_alert)


@router.post("/", response_model=Alert)
async def create_alert(
    alert_data: AlertCreateRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Create a new alert
    
    Args:
        alert_data: Alert creation data
        request: FastAPI request object
        db: Database session
        
    Returns:
        Alert: Created alert
        
    Raises:
        ValidationError: If input validation fails
        HTTPException: If creation fails
    """
    try:
        # Validate and sanitize input
        title = sanitize_input(alert_data.title, 255)
        description = sanitize_input(alert_data.description, 10000)
        source = sanitize_input(alert_data.source, 100)
        
        if not title.strip():
            raise ValidationError("Title is required", "title")
        
        if not description.strip():
            raise ValidationError("Description is required", "description")
        
        if not source.strip():
            raise ValidationError("Source is required", "source")
        
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Create alert
        db_alert = DatabaseService.create_alert(
            db=db,
            title=title,
            description=description,
            severity=alert_data.severity,
            source=source,
            status=alert_data.status,
            assigned_analyst_id=alert_data.assigned_analyst_id,
            assigned_analyst_name=alert_data.assigned_analyst_name,
            tags=alert_data.tags,
            due_date=alert_data.due_date,
            priority=alert_data.priority,
            investigation_notes=alert_data.investigation_notes,
            mitre_techniques=alert_data.mitre_techniques,
            escalation_level=alert_data.escalation_level,
            sla_deadline=alert_data.sla_deadline,
            changed_by="current_user",  # TODO: Get from auth
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"]
        )
        
        return DatabaseService.db_alert_to_pydantic(db_alert)
        
    except ValidationError:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating alert: {str(e)}")


@router.put("/{alert_id}", response_model=Alert)
async def update_alert(
    alert_id: int,
    alert_data: AlertUpdateRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Update an existing alert
    
    Args:
        alert_id: Alert ID
        alert_data: Alert update data
        request: FastAPI request object
        db: Database session
        
    Returns:
        Alert: Updated alert
        
    Raises:
        NotFoundError: If alert doesn't exist
        ValidationError: If input validation fails
        HTTPException: If update fails
    """
    try:
        # Check if alert exists
        db_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not db_alert:
            raise NotFoundError("Alert", alert_id)
        
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Update alert
        updated_alert = DatabaseService.update_alert(
            db=db,
            alert_id=alert_id,
            update_data=alert_data.dict(exclude_unset=True),
            changed_by="current_user",  # TODO: Get from auth
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"]
        )
        
        return DatabaseService.db_alert_to_pydantic(updated_alert)
        
    except (NotFoundError, ValidationError):
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating alert: {str(e)}")


@router.delete("/{alert_id}", response_model=AlertDeleteResponse)
async def delete_alert(
    alert_id: int,
    request: Request,
    permanent: bool = False,
    db: Session = Depends(get_db)
):
    """
    Delete an alert (soft delete by default)
    
    Args:
        alert_id: Alert ID
        request: FastAPI request object
        permanent: Whether to permanently delete (default: False)
        db: Database session
        
    Returns:
        AlertDeleteResponse: Deletion confirmation
        
    Raises:
        NotFoundError: If alert doesn't exist
        HTTPException: If deletion fails
    """
    try:
        # Check if alert exists
        db_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not db_alert:
            raise NotFoundError("Alert", alert_id)
        
        # Get request info for logging
        request_info = get_request_info(request)
        
        # Delete alert
        success = DatabaseService.delete_alert(
            db=db,
            alert_id=alert_id,
            permanent=permanent,
            changed_by="current_user",  # TODO: Get from auth
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"]
        )
        
        return AlertDeleteResponse(
            success=success,
            message=f"Alert {alert_id} {'permanently deleted' if permanent else 'deleted'}"
        )
        
    except NotFoundError:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting alert: {str(e)}")
