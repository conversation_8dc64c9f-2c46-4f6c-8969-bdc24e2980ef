"""
API dependencies for AlertAI backend

Contains dependency injection functions for database connections,
authentication, and other shared resources.
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.db.connection import SessionLocal
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.core.security import get_client_ip
from app.core.auth import AuthService
from app.models.database.user import DBUser


# Security schemes for authentication
security = HTTPBearer(auto_error=False)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/token", auto_error=False)


def get_db() -> Generator[Session, None, None]:
    """
    Database dependency that provides a SQLAlchemy session.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_user(
    token: Optional[str] = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> Optional[DBUser]:
    """
    Get current authenticated user from JWT token.

    Args:
        token: JWT access token
        db: Database session

    Returns:
        Optional[DBUser]: User object if authenticated, None otherwise
    """
    if not token:
        return None

    try:
        user = AuthService.get_current_user(db, token)
        return user
    except AuthenticationError:
        return None


def require_authentication(
    current_user: Optional[DBUser] = Depends(get_current_user)
) -> DBUser:
    """
    Require authentication for protected endpoints.

    Args:
        current_user: Current user from get_current_user dependency

    Returns:
        DBUser: Authenticated user object

    Raises:
        AuthenticationError: If user is not authenticated
    """
    if not current_user:
        raise AuthenticationError("Authentication required")

    return current_user


def get_request_info(request: Request) -> dict:
    """
    Extract request information for logging and security.
    
    Args:
        request: FastAPI request object
        
    Returns:
        dict: Request information including IP, user agent, etc.
    """
    return {
        "ip_address": get_client_ip(request),
        "user_agent": request.headers.get("User-Agent", "Unknown"),
        "method": request.method,
        "url": str(request.url),
        "headers": dict(request.headers),
    }


def validate_admin_access(
    current_user: DBUser = Depends(require_authentication)
) -> DBUser:
    """
    Validate admin access for administrative endpoints.

    Args:
        current_user: Current authenticated user

    Returns:
        DBUser: User object if admin access is granted

    Raises:
        AuthorizationError: If user doesn't have admin access
    """
    if not current_user.is_superuser and not AuthService.check_permission(current_user, "system:admin"):
        raise AuthorizationError("Admin access required")

    return current_user


def get_pagination_params(
    limit: int = 100,
    offset: int = 0
) -> dict:
    """
    Get and validate pagination parameters.
    
    Args:
        limit: Maximum number of items to return
        offset: Number of items to skip
        
    Returns:
        dict: Validated pagination parameters
    """
    from app.core.security import validate_pagination
    
    validated_limit, validated_offset = validate_pagination(limit, offset)
    
    return {
        "limit": validated_limit,
        "offset": validated_offset
    }


def get_filter_params(
    search: Optional[str] = None,
    severity: Optional[str] = None,
    status: Optional[str] = None,
    source: Optional[str] = None,
    assigned_to: Optional[str] = None,
    created_after: Optional[str] = None,
    created_before: Optional[str] = None,
) -> dict:
    """
    Get and validate filter parameters for alerts.
    
    Args:
        search: Search term for title/description
        severity: Alert severity filter
        status: Alert status filter
        source: Alert source filter
        assigned_to: Assigned analyst filter
        created_after: Created after date filter
        created_before: Created before date filter
        
    Returns:
        dict: Validated filter parameters
    """
    from app.core.security import sanitize_input
    
    filters = {}
    
    if search:
        filters["search"] = sanitize_input(search, 100)
    
    if severity:
        filters["severity"] = sanitize_input(severity, 20)
    
    if status:
        filters["status"] = sanitize_input(status, 20)
    
    if source:
        filters["source"] = sanitize_input(source, 100)
    
    if assigned_to:
        filters["assigned_to"] = sanitize_input(assigned_to, 100)
    
    if created_after:
        filters["created_after"] = created_after
    
    if created_before:
        filters["created_before"] = created_before
    
    return filters


class CommonQueryParams:
    """Common query parameters for API endpoints"""
    
    def __init__(
        self,
        limit: int = 100,
        offset: int = 0,
        search: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "desc"
    ):
        from app.core.security import validate_pagination, sanitize_input
        
        self.limit, self.offset = validate_pagination(limit, offset)
        self.search = sanitize_input(search, 100) if search else None
        self.sort_by = sanitize_input(sort_by, 50) if sort_by else None
        self.sort_order = sort_order if sort_order in ["asc", "desc"] else "desc"


def get_common_params(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = "desc"
) -> CommonQueryParams:
    """
    Dependency for common query parameters.
    
    Returns:
        CommonQueryParams: Validated common query parameters
    """
    return CommonQueryParams(limit, offset, search, sort_by, sort_order)
