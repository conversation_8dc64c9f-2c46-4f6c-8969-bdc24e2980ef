"""
Chat database models
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, <PERSON>, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBChatSession(BaseModel):
    """Database model for chat sessions"""
    
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), nullable=False)  # User identifier
    user_db_id = Column(Integer, ForeignKey('users.id'), nullable=True)  # Link to user table
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=True)
    title = Column(String(255), nullable=False)
    is_incognito = Column(Boolean, default=False)
    ended_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    message_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship("DBUser", back_populates="chat_sessions")
    alert = relationship("DBAlert", back_populates="chat_sessions")
    messages = relationship("DBChatMessage", back_populates="session", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DBChatSession(id={self.id}, user_id='{self.user_id}', title='{self.title}')>"


class DBChatMessage(BaseModel):
    """Database model for chat messages"""
    
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey('chat_sessions.id'), nullable=False)
    user_message = Column(Text, nullable=False)
    ai_response = Column(Text, nullable=False)
    message_order = Column(Integer, nullable=False)
    response_time_ms = Column(Integer, nullable=True)
    context_data = Column(JSON, nullable=True)  # Additional context used for the response
    model_used = Column(String(100), nullable=True)
    
    # Relationships
    session = relationship("DBChatSession", back_populates="messages")

    def __repr__(self):
        return f"<DBChatMessage(id={self.id}, session_id={self.session_id}, message_order={self.message_order})>"
