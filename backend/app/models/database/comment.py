"""
Comment database models
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBComment(BaseModel):
    """Database model for comments"""
    
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=False)
    author = Column(String(100), nullable=False)
    author_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    content = Column(Text, nullable=False)
    is_deleted = Column(Boolean, default=False)
    
    # Relationships
    alert = relationship("DBAlert", back_populates="comments")
    author_user = relationship("DBUser", back_populates="comments")
    mentions = relationship("DBCommentMention", back_populates="comment", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DBComment(id={self.id}, alert_id={self.alert_id}, author='{self.author}')>"


class DBCommentMention(BaseModel):
    """Database model for comment mentions"""
    
    __tablename__ = "comment_mentions"

    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    mentioned_user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    mentioned_username = Column(String(100), nullable=False)
    
    # Relationships
    comment = relationship("DBComment", back_populates="mentions")
    mentioned_user = relationship("DBUser")

    def __repr__(self):
        return f"<DBCommentMention(id={self.id}, comment_id={self.comment_id}, mentioned_username='{self.mentioned_username}')>"
