"""
Webhook database models
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBWebhookSource(BaseModel):
    """Database model for webhook sources"""
    
    __tablename__ = "webhook_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    webhook_url = Column(String(500), nullable=False, unique=True)
    secret_key = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    source_type = Column(String(100), nullable=False)  # siem, ids, firewall, etc.
    config = Column(JSON, nullable=True)  # Source-specific configuration
    last_received = Column(DateTime(timezone=True), nullable=True)
    total_alerts_received = Column(Integer, default=0)
    
    # Relationships
    webhook_alerts = relationship("DBWebhookAlert", back_populates="source", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DBWebhookSource(id={self.id}, name='{self.name}', source_type='{self.source_type}')>"


class DBWebhookAlert(BaseModel):
    """Database model for webhook alerts"""
    
    __tablename__ = "webhook_alerts"

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey('webhook_sources.id'), nullable=False)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=True)  # Link to created alert
    raw_payload = Column(JSON, nullable=False)  # Original webhook payload
    processed_data = Column(JSON, nullable=True)  # Processed/normalized data
    status = Column(String(50), default='received')  # received, processed, failed
    error_message = Column(Text, nullable=True)
    processing_time_ms = Column(Integer, nullable=True)
    
    # Relationships
    source = relationship("DBWebhookSource", back_populates="webhook_alerts")
    alert = relationship("DBAlert")

    def __repr__(self):
        return f"<DBWebhookAlert(id={self.id}, source_id={self.source_id}, status='{self.status}')>"
