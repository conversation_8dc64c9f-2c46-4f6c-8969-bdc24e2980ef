"""
File resource database models
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBFileResource(BaseModel):
    """Database model for file resources"""
    
    __tablename__ = "file_resources"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(100), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=True)
    is_image = Column(Boolean, default=False)
    uploaded_by = Column(String(100), nullable=False)
    uploaded_by_user_id = Column(Integer, <PERSON><PERSON><PERSON>('users.id'), nullable=True)
    description = Column(Text, nullable=True)
    is_deleted = Column(Boolean, default=False)
    
    # Relationships
    alert = relationship("DBAlert", back_populates="file_resources")
    uploaded_by_user = relationship("DBUser")

    def __repr__(self):
        return f"<DBFileResource(id={self.id}, alert_id={self.alert_id}, filename='{self.filename}')>"
