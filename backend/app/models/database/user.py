"""
User, Role, and Permission database models
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, Table, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import BaseModel


# Association tables for many-to-many relationships
user_roles = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

role_permissions = Table(
    'role_permissions',
    BaseModel.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)


class DBUser(BaseModel):
    """Database model for users"""
    
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    
    # Profile information
    phone = Column(String(50), nullable=True)
    department = Column(String(100), nullable=True)
    title = Column(String(100), nullable=True)
    timezone = Column(String(50), default='UTC', nullable=False)
    
    # Preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    theme_preference = Column(String(20), default='light', nullable=False)
    
    # Relationships
    roles = relationship("DBRole", secondary=user_roles, back_populates="users")
    created_alerts = relationship("DBAlert", foreign_keys="DBAlert.created_by_id", back_populates="created_by")
    assigned_alerts = relationship("DBAlert", foreign_keys="DBAlert.assigned_analyst_id", back_populates="assigned_analyst")
    comments = relationship("DBComment", back_populates="author_user")
    chat_sessions = relationship("DBChatSession", back_populates="user")
    audit_logs = relationship("DBAuditLog", back_populates="user")

    def __repr__(self):
        return f"<DBUser(id={self.id}, email='{self.email}', full_name='{self.full_name}')>"


class DBRole(BaseModel):
    """Database model for roles"""
    
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_system_role = Column(Boolean, default=False, nullable=False)  # Cannot be deleted
    
    # Relationships
    users = relationship("DBUser", secondary=user_roles, back_populates="roles")
    permissions = relationship("DBPermission", secondary=role_permissions, back_populates="roles")

    def __repr__(self):
        return f"<DBRole(id={self.id}, name='{self.name}')>"


class DBPermission(BaseModel):
    """Database model for permissions"""
    
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    resource_type = Column(String(50), nullable=True)  # alerts, comments, files, etc.
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    roles = relationship("DBRole", secondary=role_permissions, back_populates="permissions")

    def __repr__(self):
        return f"<DBPermission(id={self.id}, name='{self.name}')>"


class DBUserSession(BaseModel):
    """Database model for user sessions"""
    
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_token = Column(String(255), unique=True, index=True, nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("DBUser")

    def __repr__(self):
        return f"<DBUserSession(id={self.id}, user_id={self.user_id}, expires_at='{self.expires_at}')>"


class DBAuditLog(BaseModel):
    """Database model for audit logs"""
    
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    action = Column(String(100), nullable=False, index=True)
    resource_type = Column(String(50), nullable=True, index=True)
    resource_id = Column(String(100), nullable=True, index=True)
    old_values = Column(Text, nullable=True)  # JSON string
    new_values = Column(Text, nullable=True)  # JSON string
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    success = Column(Boolean, nullable=False, index=True)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("DBUser", back_populates="audit_logs")

    def __repr__(self):
        return f"<DBAuditLog(id={self.id}, action='{self.action}', resource_type='{self.resource_type}')>"


class DBAPIKey(BaseModel):
    """Database model for API keys"""
    
    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    name = Column(String(255), nullable=False)
    key_hash = Column(String(255), unique=True, index=True, nullable=False)
    key_prefix = Column(String(10), nullable=False)  # First few chars for identification
    permissions = Column(Text, nullable=True)  # JSON array of permissions
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    last_used = Column(DateTime(timezone=True), nullable=True)
    usage_count = Column(Integer, default=0, nullable=False)
    
    # Relationships
    user = relationship("DBUser")

    def __repr__(self):
        return f"<DBAPIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"


# Legacy alias for backward compatibility
DBAnalyst = DBUser
