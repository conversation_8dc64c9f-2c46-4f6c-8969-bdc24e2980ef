"""
Alert database model
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBAlert(BaseModel):
    """Database model for alerts"""
    
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=False)
    severity = Column(String(20), nullable=False, index=True)  # low, medium, high, critical
    status = Column(String(20), nullable=False, index=True)    # open, acknowledged, resolved
    source = Column(String(100), nullable=False, index=True)
    assigned_analyst_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    assigned_analyst_name = Column(String(100), nullable=True)
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    tags = Column(JSON, nullable=True)  # JSON array of strings
    due_date = Column(DateTime(timezone=True), nullable=True)
    priority = Column(Integer, nullable=True)  # 1-5 scale
    investigation_notes = Column(Text, nullable=True)
    mitre_techniques = Column(JSON, nullable=True)  # JSON array of MITRE ATT&CK technique IDs
    escalation_level = Column(Integer, default=0)  # 0-3 scale
    sla_deadline = Column(DateTime(timezone=True), nullable=True)
    is_deleted = Column(Boolean, default=False, index=True)

    # Relationships
    assigned_analyst = relationship("DBUser", foreign_keys=[assigned_analyst_id], back_populates="assigned_alerts")
    created_by = relationship("DBUser", foreign_keys=[created_by_id], back_populates="created_alerts")
    comments = relationship("DBComment", back_populates="alert", cascade="all, delete-orphan")
    ai_findings = relationship("DBAIFinding", back_populates="alert", cascade="all, delete-orphan")
    file_resources = relationship("DBFileResource", back_populates="alert", cascade="all, delete-orphan")
    change_logs = relationship("DBAlertChangeLog", back_populates="alert", cascade="all, delete-orphan")
    chat_sessions = relationship("DBChatSession", back_populates="alert")

    def __repr__(self):
        return f"<DBAlert(id={self.id}, title='{self.title}', severity='{self.severity}', status='{self.status}')>"
