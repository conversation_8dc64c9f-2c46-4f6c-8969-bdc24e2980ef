"""
AI Finding database models
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBAIFinding(BaseModel):
    """Database model for AI findings"""
    
    __tablename__ = "ai_findings"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=False)
    finding_type = Column(String(50), nullable=False)  # analysis, correlation, threat_intel, etc.
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    confidence_score = Column(Float, nullable=True)  # 0.0 to 1.0
    severity = Column(String(20), nullable=True)
    mitre_techniques = Column(JSON, nullable=True)  # JSON array of MITRE ATT&CK technique IDs
    iocs = Column(JSON, nullable=True)  # JSON array of indicators of compromise
    recommendations = Column(Text, nullable=True)
    raw_data = Column(JSON, nullable=True)  # Raw AI response data
    model_used = Column(String(100), nullable=True)
    processing_time_ms = Column(Integer, nullable=True)
    is_verified = Column(Boolean, default=False)
    
    # Relationships
    alert = relationship("DBAlert", back_populates="ai_findings")

    def __repr__(self):
        return f"<DBAIFinding(id={self.id}, alert_id={self.alert_id}, finding_type='{self.finding_type}')>"
