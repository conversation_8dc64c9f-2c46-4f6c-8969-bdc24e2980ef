"""
System database models
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.db.base import BaseModel


class DBAlertChangeLog(BaseModel):
    """Database model for alert change logs"""
    
    __tablename__ = "alert_change_logs"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey('alerts.id'), nullable=False)
    changed_by = Column(String(100), nullable=False)
    changed_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    change_type = Column(String(50), nullable=False)  # created, updated, status_changed, etc.
    field_name = Column(String(100), nullable=True)
    old_value = Column(Text, nullable=True)
    new_value = Column(Text, nullable=True)
    change_reason = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Relationships
    alert = relationship("DBAlert", back_populates="change_logs")
    changed_by_user = relationship("DBUser")

    def __repr__(self):
        return f"<DBAlertChangeLog(id={self.id}, alert_id={self.alert_id}, change_type='{self.change_type}')>"


class DBSystemLog(BaseModel):
    """Database model for system logs"""
    
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    event_type = Column(String(100), nullable=False, index=True)
    user_id = Column(String(100), nullable=True)
    user_db_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    username = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True)
    resource_type = Column(String(100), nullable=True)
    resource_id = Column(String(100), nullable=True)
    action = Column(String(100), nullable=False)
    details = Column(JSON, nullable=True)
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(Text, nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    request_size_bytes = Column(Integer, nullable=True)
    response_size_bytes = Column(Integer, nullable=True)
    log_level = Column(String(20), nullable=False, default='INFO')
    checksum = Column(String(64), nullable=True)  # For integrity verification
    
    # Relationships
    user = relationship("DBUser")

    def __repr__(self):
        return f"<DBSystemLog(id={self.id}, event_type='{self.event_type}', action='{self.action}')>"
