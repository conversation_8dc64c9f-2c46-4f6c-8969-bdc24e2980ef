"""
System API schemas for request/response validation
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel


class SystemLog(BaseModel):
    """Schema for system log responses"""
    
    id: int
    timestamp: datetime
    event_type: str
    user_id: Optional[str] = None
    username: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: str
    details: Optional[Dict[str, Any]] = None
    success: bool
    error_message: Optional[str] = None
    response_time_ms: Optional[int] = None
    request_size_bytes: Optional[int] = None
    response_size_bytes: Optional[int] = None
    log_level: str
    checksum: Optional[str] = None

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SystemLogResponse(BaseModel):
    """Schema for paginated system log responses"""
    
    logs: List[SystemLog]
    total: int
