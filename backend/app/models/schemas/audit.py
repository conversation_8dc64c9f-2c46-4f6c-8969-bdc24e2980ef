"""
Audit log API schemas for request/response validation
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel


class AuditLogEntry(BaseModel):
    """Schema for individual audit log entry"""
    
    id: int
    user_id: Optional[int] = None
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    old_values: Optional[str] = None  # JSON string
    new_values: Optional[str] = None  # JSON string
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    success: bool
    error_message: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AuditLogResponse(BaseModel):
    """Schema for paginated audit log response"""
    
    logs: List[AuditLogEntry]
    total: int
    limit: int
    offset: int


class AuditLogStats(BaseModel):
    """Schema for audit log statistics"""
    
    total_events: int
    successful_events: int
    failed_events: int
    success_rate: float  # Percentage
    auth_events: int
    resource_stats: Dict[str, int]  # Resource type -> count
    period_days: int
    start_date: datetime
    end_date: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AuditLogExport(BaseModel):
    """Schema for audit log export response"""
    
    file_path: str
    format: str
    record_count: int
    file_size: int
    created_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AuditLogFilter(BaseModel):
    """Schema for audit log filtering"""
    
    user_id: Optional[int] = None
    resource_type: Optional[str] = None
    action: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    success_only: Optional[bool] = None


class AuditLogExportRequest(BaseModel):
    """Schema for audit log export request"""
    
    format: str = "json"  # json, csv, xlsx
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    filters: Optional[AuditLogFilter] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SecurityEvent(BaseModel):
    """Schema for security event logging"""
    
    event_type: str
    severity: str  # low, medium, high, critical
    description: str
    user_id: Optional[int] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class ComplianceReport(BaseModel):
    """Schema for compliance reporting"""
    
    report_type: str
    period_start: datetime
    period_end: datetime
    total_events: int
    security_events: int
    failed_logins: int
    privilege_escalations: int
    data_access_events: int
    generated_at: datetime
    generated_by: int

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
