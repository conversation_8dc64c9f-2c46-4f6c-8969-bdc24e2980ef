"""
File API schemas for request/response validation
"""

from datetime import datetime
from pydantic import BaseModel


class UploadedFile(BaseModel):
    """Schema for uploaded file responses"""
    
    id: int
    alert_id: int
    filename: str
    file_path: str
    file_type: str
    file_size: int
    uploaded_by: str
    uploaded_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class FileResource(BaseModel):
    """Schema for file resource responses"""
    
    id: int
    alert_id: int
    filename: str
    file_path: str
    file_type: str
    file_size: int
    is_image: bool
    uploaded_by: str
    uploaded_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
