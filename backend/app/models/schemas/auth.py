"""
Authentication API schemas for request/response validation
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, field_validator, model_validator


class Token(BaseModel):
    """Schema for JWT token response"""
    
    access_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """Schema for JWT token data"""
    
    email: Optional[str] = None
    user_id: Optional[int] = None
    permissions: Optional[List[str]] = []


class UserLogin(BaseModel):
    """Schema for user login request"""
    
    email: EmailStr
    password: str


class UserCreate(BaseModel):
    """Schema for user creation request"""
    
    email: EmailStr
    password: str
    full_name: str
    phone: Optional[str] = None
    department: Optional[str] = None
    title: Optional[str] = None

    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Full name is required')
        if len(v.strip()) < 2:
            raise ValueError('Full name must be at least 2 characters long')
        return v.strip()


class UserUpdate(BaseModel):
    """Schema for user update request"""
    
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    title: Optional[str] = None
    timezone: Optional[str] = None
    email_notifications: Optional[bool] = None
    theme_preference: Optional[str] = None

    @field_validator('theme_preference')
    @classmethod
    def validate_theme(cls, v):
        if v and v not in ['light', 'dark', 'auto']:
            raise ValueError('Theme must be light, dark, or auto')
        return v


class UserResponse(BaseModel):
    """Schema for user response"""
    
    id: int
    email: str
    full_name: str
    is_active: bool
    is_superuser: Optional[bool] = False
    roles: List[str] = []
    permissions: List[str] = []
    created_at: datetime
    last_login: Optional[datetime] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    title: Optional[str] = None
    timezone: Optional[str] = None
    email_notifications: Optional[bool] = None
    theme_preference: Optional[str] = None

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PasswordChange(BaseModel):
    """Schema for password change request"""
    
    current_password: str
    new_password: str
    confirm_password: str

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

    @model_validator(mode='after')
    def passwords_match(self):
        if self.new_password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self


class PasswordReset(BaseModel):
    """Schema for password reset request"""
    
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation"""
    
    token: str
    new_password: str
    confirm_password: str

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

    @model_validator(mode='after')
    def passwords_match(self):
        if self.new_password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self


class RoleResponse(BaseModel):
    """Schema for role response"""
    
    id: int
    name: str
    description: Optional[str] = None
    is_active: bool
    is_system_role: bool
    permissions: List[str] = []
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RoleCreate(BaseModel):
    """Schema for role creation request"""
    
    name: str
    description: Optional[str] = None
    permissions: List[str] = []

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Role name is required')
        if len(v.strip()) < 2:
            raise ValueError('Role name must be at least 2 characters long')
        return v.strip().lower()


class PermissionResponse(BaseModel):
    """Schema for permission response"""
    
    id: int
    name: str
    description: Optional[str] = None
    resource_type: Optional[str] = None
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserRoleAssignment(BaseModel):
    """Schema for user role assignment"""
    
    user_id: int
    role_names: List[str]


class APIKeyCreate(BaseModel):
    """Schema for API key creation request"""
    
    name: str
    permissions: Optional[List[str]] = []
    expires_in_days: Optional[int] = None

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('API key name is required')
        return v.strip()

    @field_validator('expires_in_days')
    @classmethod
    def validate_expiry(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Expiry days must be positive')
        if v is not None and v > 365:
            raise ValueError('API key cannot expire more than 365 days from now')
        return v


class APIKeyResponse(BaseModel):
    """Schema for API key response"""
    
    id: int
    name: str
    key_prefix: str
    permissions: List[str] = []
    expires_at: Optional[datetime] = None
    is_active: bool
    last_used: Optional[datetime] = None
    usage_count: int
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class APIKeyCreateResponse(BaseModel):
    """Schema for API key creation response (includes the actual key)"""
    
    id: int
    name: str
    api_key: str  # Only returned once during creation
    key_prefix: str
    permissions: List[str] = []
    expires_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
