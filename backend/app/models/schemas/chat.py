"""
Chat API schemas for request/response validation
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel


class ChatRequest(BaseModel):
    """Schema for chat requests"""
    
    message: str
    user_id: Optional[str] = "current_user"
    session_id: Optional[int] = None
    page_context: Optional[Dict[str, Any]] = None
    is_incognito: Optional[bool] = False


class ChatResponse(BaseModel):
    """Schema for chat responses"""
    
    response: str
    session_id: int
    message_id: int
    is_new_session: bool = False


class ChatMessage(BaseModel):
    """Schema for chat messages"""
    
    id: int
    session_id: int
    user_message: str
    ai_response: str
    message_order: int
    created_at: datetime
    response_time_ms: Optional[int] = None
    context_data: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ChatSession(BaseModel):
    """Schema for chat sessions"""
    
    id: int
    user_id: str
    alert_id: Optional[int] = None
    title: str
    is_incognito: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    is_active: bool = True
    message_count: Optional[int] = 0

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
