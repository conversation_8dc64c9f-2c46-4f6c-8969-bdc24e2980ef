"""
User API schemas for request/response validation
"""

from datetime import datetime
from pydantic import BaseModel


class Analyst(BaseModel):
    """Schema for analyst responses"""
    
    id: int
    name: str
    email: str
    role: str
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
