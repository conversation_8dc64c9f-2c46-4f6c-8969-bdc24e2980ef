"""
Alert API schemas for request/response validation
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, field_validator

from app.core.constants import AlertSeverity, AlertStatus


class AlertBase(BaseModel):
    """Base alert schema with common fields"""
    
    title: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    source: str
    assigned_analyst_id: Optional[int] = None
    assigned_analyst_name: Optional[str] = None
    tags: Optional[List[str]] = []
    due_date: Optional[datetime] = None
    priority: Optional[int] = None  # 1-5 scale
    investigation_notes: Optional[str] = None
    mitre_techniques: Optional[List[str]] = []
    escalation_level: Optional[int] = 0  # 0-3 scale
    sla_deadline: Optional[datetime] = None

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError('Title cannot be empty')
        if len(v.strip()) > 255:
            raise ValueError('Title cannot exceed 255 characters')
        return v.strip()

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if not v or not v.strip():
            raise ValueError('Description cannot be empty')
        if len(v.strip()) > 10000:
            raise ValueError('Description cannot exceed 10000 characters')
        return v.strip()

    @field_validator('source')
    @classmethod
    def validate_source(cls, v):
        if not v or not v.strip():
            raise ValueError('Source cannot be empty')
        if len(v.strip()) > 100:
            raise ValueError('Source cannot exceed 100 characters')
        return v.strip()

    @field_validator('priority')
    @classmethod
    def validate_priority(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Priority must be between 1 and 5')
        return v

    @field_validator('escalation_level')
    @classmethod
    def validate_escalation_level(cls, v):
        if v is not None and (v < 0 or v > 3):
            raise ValueError('Escalation level must be between 0 and 3')
        return v


class AlertCreateRequest(AlertBase):
    """Schema for creating a new alert"""
    pass


class AlertUpdateRequest(BaseModel):
    """Schema for updating an existing alert"""
    
    title: Optional[str] = None
    description: Optional[str] = None
    severity: Optional[AlertSeverity] = None
    status: Optional[AlertStatus] = None
    source: Optional[str] = None
    assigned_analyst_id: Optional[int] = None
    assigned_analyst_name: Optional[str] = None
    tags: Optional[List[str]] = None
    due_date: Optional[datetime] = None
    priority: Optional[int] = None
    investigation_notes: Optional[str] = None
    mitre_techniques: Optional[List[str]] = None
    escalation_level: Optional[int] = None
    sla_deadline: Optional[datetime] = None

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('Title cannot be empty')
            if len(v.strip()) > 255:
                raise ValueError('Title cannot exceed 255 characters')
        return v

    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('Description cannot be empty')
            if len(v.strip()) > 10000:
                raise ValueError('Description cannot exceed 10000 characters')
        return v

    @field_validator('priority')
    @classmethod
    def validate_priority(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Priority must be between 1 and 5')
        return v

    @field_validator('escalation_level')
    @classmethod
    def validate_escalation_level(cls, v):
        if v is not None and (v < 0 or v > 3):
            raise ValueError('Escalation level must be between 0 and 3')
        return v


class Alert(AlertBase):
    """Schema for alert responses"""
    
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: Optional[bool] = False

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AlertResponse(BaseModel):
    """Schema for paginated alert responses"""
    
    alerts: List[Alert]
    total: int


class AlertDeleteResponse(BaseModel):
    """Schema for alert deletion responses"""
    
    success: bool
    message: str
