"""
Comment API schemas for request/response validation
"""

from datetime import datetime
from pydantic import BaseModel, field_validator


class CommentBase(BaseModel):
    """Base comment schema"""
    
    author: str
    content: str

    @field_validator('author')
    @classmethod
    def validate_author(cls, v):
        if not v or not v.strip():
            raise ValueError('Author cannot be empty')
        if len(v.strip()) > 100:
            raise ValueError('Author cannot exceed 100 characters')
        return v.strip()

    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('Content cannot be empty')
        if len(v.strip()) < 3:
            raise ValueError('Content must be at least 3 characters')
        if len(v.strip()) > 10000:
            raise ValueError('Content cannot exceed 10000 characters')
        return v.strip()


class CommentCreate(CommentBase):
    """Schema for creating a new comment"""
    pass


class Comment(CommentBase):
    """Schema for comment responses"""
    
    id: int
    alert_id: int
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
