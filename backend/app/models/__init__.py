"""
Models package for AlertAI

Contains both database models (SQLAlchemy) and API schemas (Pydantic).
"""

# Import database models
from .database import *

# Import API schemas  
from .schemas import *

__all__ = [
    # Database models
    "DBAlert",
    "DBComment", 
    "DBAIFinding",
    "DBFileResource",
    "DBAnalyst",
    "DBCommentMention",
    "DBChatSession",
    "DBChatMessage",
    "DBWebhookSource",
    "DBWebhookAlert",
    "DBAlertChangeLog",
    "DBSystemLog",
    
    # API schemas
    "Alert",
    "AlertResponse",
    "AlertCreateRequest",
    "AlertUpdateRequest",
    "AlertDeleteResponse",
    "Comment",
    "CommentCreate",
    "AIFinding",
    "UploadedFile",
    "FileResource",
    "ChatSession",
    "ChatMessage",
    "ChatRequest",
    "ChatResponse",
    "Analyst",
    "SystemLog",
    "SystemLogResponse",
    "WebhookSource",
    "WebhookSourceCreate",
    "WebhookSourceUpdate",
    "WebhookSourceResponse",
]
