from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from datetime import datetime
import os

# Database configuration
DATABASE_URL = "sqlite:///./alertai.db"

# Create engine
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class
Base = declarative_base()


class DBAlert(Base):
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    severity = Column(String(20), nullable=False)  # low, medium, high, critical
    status = Column(String(20), nullable=False)    # open, acknowledged, resolved
    source = Column(String(100), nullable=False)
    assigned_analyst_id = Column(Integer, nullable=True)
    assigned_analyst_name = Column(String(100), nullable=True)
    tags = Column(JSON, nullable=True)  # JSON array of strings
    due_date = Column(DateTime(timezone=True), nullable=True)
    priority = Column(Integer, nullable=True)  # 1-5 scale
    investigation_notes = Column(Text, nullable=True)
    mitre_techniques = Column(JSON, nullable=True)  # JSON array of MITRE ATT&CK technique IDs
    escalation_level = Column(Integer, default=0)  # 0-3 scale
    sla_deadline = Column(DateTime(timezone=True), nullable=True)
    is_deleted = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    comments = relationship("DBComment", back_populates="alert", cascade="all, delete-orphan")
    ai_findings = relationship("DBAIFinding", back_populates="alert", cascade="all, delete-orphan")
    file_resources = relationship("DBFileResource", back_populates="alert", cascade="all, delete-orphan")
    chat_sessions = relationship("DBChatSession", back_populates="alert")
    change_logs = relationship("DBAlertChangeLog", back_populates="alert", cascade="all, delete-orphan")


class DBComment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    author = Column(String(100), nullable=False)
    author_id = Column(Integer, ForeignKey("analysts.id"), nullable=True)
    content = Column(Text, nullable=False)
    original_content = Column(Text, nullable=True)  # For AI beautified comments
    is_beautified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    alert = relationship("DBAlert", back_populates="comments")
    author_analyst = relationship("DBAnalyst", back_populates="comments")
    mentions = relationship("DBCommentMention", back_populates="comment", cascade="all, delete-orphan")


class DBAIFinding(Base):
    __tablename__ = "ai_findings"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    type = Column(String(50), nullable=False)  # root_cause, recommendation, similar_incidents
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    confidence = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    alert = relationship("DBAlert", back_populates="ai_findings")


class DBAlertChangeLog(Base):
    __tablename__ = "alert_change_logs"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    changed_by = Column(String(100), nullable=False)  # Username or analyst name
    changed_at = Column(DateTime(timezone=True), server_default=func.now())
    change_type = Column(String(50), nullable=False)  # created, updated, deleted, status_changed, etc.
    field_name = Column(String(100), nullable=True)  # Which field was changed
    old_value = Column(Text, nullable=True)  # Previous value
    new_value = Column(Text, nullable=True)  # New value
    description = Column(Text, nullable=True)  # Human-readable description
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 address
    user_agent = Column(Text, nullable=True)  # Browser/client info

    # Relationships
    alert = relationship("DBAlert", back_populates="change_logs")


class DBFileResource(Base):
    __tablename__ = "file_resources"

    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(100), nullable=False)
    file_size = Column(Integer, nullable=False)
    is_image = Column(Boolean, default=False)
    thumbnail_path = Column(String(500), nullable=True)
    uploaded_by = Column(String(100), nullable=False)
    uploaded_by_id = Column(Integer, ForeignKey("analysts.id"), nullable=True)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    file_metadata = Column(JSON, nullable=True)

    # Relationships
    alert = relationship("DBAlert", back_populates="file_resources")
    uploader = relationship("DBAnalyst", back_populates="uploaded_files")


class DBAnalyst(Base):
    __tablename__ = "analysts"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    avatar_url = Column(String(500), nullable=True)
    role = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    comments = relationship("DBComment", back_populates="author_analyst")
    uploaded_files = relationship("DBFileResource", back_populates="uploader")
    mentions = relationship("DBCommentMention", back_populates="analyst")


class DBCommentMention(Base):
    __tablename__ = "comment_mentions"

    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(Integer, ForeignKey("comments.id"), nullable=False)
    analyst_id = Column(Integer, ForeignKey("analysts.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    comment = relationship("DBComment", back_populates="mentions")
    analyst = relationship("DBAnalyst", back_populates="mentions")


class DBChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), nullable=False)  # User identifier
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=True)  # NULL for general chat
    title = Column(String(255), nullable=False)  # Auto-generated or user-defined title
    is_incognito = Column(Boolean, default=False)  # Incognito sessions won't be saved permanently
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    ended_at = Column(DateTime(timezone=True), nullable=True)  # When session was ended
    is_active = Column(Boolean, default=True)  # Whether session is still active

    # Relationships
    alert = relationship("DBAlert", back_populates="chat_sessions")
    messages = relationship("DBChatMessage", back_populates="session", cascade="all, delete-orphan")


class DBChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    user_message = Column(Text, nullable=False)  # User's message
    ai_response = Column(Text, nullable=False)   # AI's response
    message_order = Column(Integer, nullable=False)  # Order within the session
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    response_time_ms = Column(Integer, nullable=True)  # How long AI took to respond
    context_data = Column(JSON, nullable=True)  # Additional context used for this message

    # Relationships
    session = relationship("DBChatSession", back_populates="messages")


class DBWebhookSource(Base):
    __tablename__ = "webhook_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    webhook_secret = Column(String(64), unique=True, nullable=False)  # Unique secret for URL generation
    auth_type = Column(String(20), nullable=False)  # custom_header, bearer_token
    auth_header_name = Column(String(100), nullable=True)
    auth_header_value = Column(Text, nullable=True)  # Encrypted in production
    auth_bearer_token = Column(Text, nullable=True)  # Encrypted in production
    json_mapping = Column(JSON, nullable=False)  # JSON schema mapping configuration
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    alert_count = Column(Integer, default=0)  # Number of alerts created through this integration

    # Relationships
    webhook_alerts = relationship("DBWebhookAlert", back_populates="source")


class DBWebhookAlert(Base):
    __tablename__ = "webhook_alerts"

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("webhook_sources.id"), nullable=False)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    raw_payload = Column(JSON, nullable=False)  # Original webhook payload
    processed_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    source = relationship("DBWebhookSource", back_populates="webhook_alerts")
    alert = relationship("DBAlert")


class DBSystemLog(Base):
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    event_type = Column(String(100), nullable=False, index=True)  # alert_created, bitzy_interaction, etc.
    user_id = Column(String(100), nullable=True, index=True)  # User identifier
    username = Column(String(100), nullable=True, index=True)  # Display name
    ip_address = Column(String(45), nullable=True, index=True)  # IPv4/IPv6 address
    user_agent = Column(Text, nullable=True)  # Browser/client info
    session_id = Column(String(100), nullable=True, index=True)  # User session identifier
    resource_type = Column(String(50), nullable=True, index=True)  # alert, source, comment, etc.
    resource_id = Column(String(100), nullable=True, index=True)  # ID of affected resource
    action = Column(String(100), nullable=False, index=True)  # create, update, delete, view, etc.
    details = Column(JSON, nullable=True)  # Additional context as JSON object
    success = Column(Boolean, nullable=False, default=True, index=True)  # Whether action succeeded
    error_message = Column(Text, nullable=True)  # Error details if action failed
    response_time_ms = Column(Integer, nullable=True)  # Performance metric
    request_size_bytes = Column(Integer, nullable=True)  # Request payload size
    response_size_bytes = Column(Integer, nullable=True)  # Response payload size
    log_level = Column(String(10), nullable=False, default='INFO', index=True)  # INFO, WARN, ERROR, DEBUG
    checksum = Column(String(64), nullable=True)  # SHA-256 hash for tamper detection


# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Create all tables
def create_tables():
    Base.metadata.create_all(bind=engine)


# Initialize database
def init_db():
    create_tables()
