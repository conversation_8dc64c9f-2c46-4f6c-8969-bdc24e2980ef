"""
Panther Labs Real Alert Seed Data
=================================

This module creates realistic seed data based on real security detection rules
from Panther Labs' open-source repository. Each alert represents a real security
scenario that could be detected in production environments.
"""

from datetime import datetime, timedelta
import random
from sqlalchemy.orm import Session
from database import get_db, init_db
from db_service import DatabaseService
from panther_real_alerts import PANTHER_REAL_ALERTS
from models import AlertSeverity, AlertStatus, AIFindingType


def create_panther_seed_data(db: Session):
    """Create seed data using real Panther Labs alert cases"""
    
    print("🌱 Starting Panther Labs real alert seeding...")
    
    # Create analysts first
    analysts_data = [
        {
            "username": "sarah.chen",
            "full_name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "Senior Security Analyst",
            "avatar_url": "/avatars/sarah.jpg"
        },
        {
            "username": "mike.rodrigue<PERSON>",
            "full_name": "<PERSON>", 
            "email": "<EMAIL>",
            "role": "SOC Lead",
            "avatar_url": "/avatars/mike.jpg"
        },
        {
            "username": "alex.kim",
            "full_name": "Alex Kim",
            "email": "<EMAIL>", 
            "role": "Incident Response Specialist",
            "avatar_url": "/avatars/alex.jpg"
        },
        {
            "username": "jordan.taylor",
            "full_name": "Jordan Taylor",
            "email": "<EMAIL>",
            "role": "Threat Hunter",
            "avatar_url": "/avatars/jordan.jpg"
        },
        {
            "username": "casey.morgan",
            "full_name": "Casey Morgan",
            "email": "<EMAIL>",
            "role": "Security Engineer",
            "avatar_url": "/avatars/casey.jpg"
        }
    ]
    
    # Create analysts
    analysts = []
    for analyst_data in analysts_data:
        analyst = DatabaseService.create_analyst(
            db=db,
            username=analyst_data["username"],
            full_name=analyst_data["full_name"],
            email=analyst_data["email"],
            role=analyst_data["role"],
            avatar_url=analyst_data["avatar_url"]
        )
        analysts.append(analyst)
    
    print(f"✅ Seeded {len(analysts)} analysts")
    
    # Create alerts from Panther Labs real cases
    alerts = []
    for i, alert_data in enumerate(PANTHER_REAL_ALERTS):
        # Create realistic timestamps
        created_time = datetime.now() - timedelta(
            hours=random.randint(1, 168),  # 1 hour to 1 week ago
            minutes=random.randint(0, 59)
        )
        
        # Map severity strings to enum values
        severity_map = {
            "low": AlertSeverity.LOW,
            "medium": AlertSeverity.MEDIUM, 
            "high": AlertSeverity.HIGH,
            "critical": AlertSeverity.CRITICAL
        }
        
        # Map status strings to enum values
        status_map = {
            "open": AlertStatus.OPEN,
            "acknowledged": AlertStatus.ACKNOWLEDGED,
            "resolved": AlertStatus.RESOLVED
        }
        
        # Enhance description with technical details for threat intel analysis
        enhanced_description = alert_data["description"]
        if "details" in alert_data:
            details = alert_data["details"]
            # Add source IP if present
            if "source_ip" in details:
                enhanced_description += f" Source IP: {details['source_ip']}."
            if "source_ips" in details:
                enhanced_description += f" Source IPs: {', '.join(details['source_ips'])}."
            if "destination_ip" in details:
                enhanced_description += f" Destination IP: {details['destination_ip']}."
            # Add user information
            if "user" in details:
                enhanced_description += f" User: {details['user']}."
            if "user_identity" in details:
                enhanced_description += f" User: {details['user_identity']}."
            # Add file hashes if present
            if "file_hash" in details:
                enhanced_description += f" File hash: {details['file_hash']}."
            if "hash" in details:
                enhanced_description += f" Hash: {details['hash']}."
            # Add malicious domains
            if "malicious_domain" in details:
                enhanced_description += f" Domain: {details['malicious_domain']}."
            if "query_domain" in details:
                enhanced_description += f" Domain: {details['query_domain']}."

        alert = DatabaseService.create_alert(
            db=db,
            title=alert_data["title"],
            description=enhanced_description,
            severity=severity_map[alert_data["severity"]].value,
            status=status_map[alert_data["status"]].value,
            source=alert_data["source"],
            created_at=created_time,
            updated_at=created_time + timedelta(minutes=random.randint(5, 120)) if random.choice([True, False]) else None
        )
        alerts.append(alert)
    
    print(f"✅ Seeded {len(alerts)} real Panther Labs alerts")
    
    # Create realistic comments for alerts
    comment_templates = [
        "Initial triage completed. Escalating to **Tier 2** for further investigation.",
        "✅ **Confirmed as true positive**. Implementing containment measures:\n\n1. Isolate affected systems\n2. Preserve evidence\n3. Notify stakeholders",
        "False positive - legitimate administrative activity during maintenance window. ✅",
        "Correlating with threat intelligence feeds for additional context:\n\n- Checking IOC databases\n- Cross-referencing with recent campaigns\n- Analyzing TTPs",
        "User contacted and confirmed authorized activity. Closing as `benign`. 📞",
        "🚨 **Suspicious activity confirmed**. Initiating incident response procedures.",
        "Monitoring for additional indicators of compromise:\n\n```\n- Process execution patterns\n- Network communications\n- File system changes\n```",
        "Remediation actions completed. Continuing to monitor for **24 hours**. ⏰",
        "Adding to watchlist for future correlation and pattern analysis. 📊",
        "Documented in knowledge base for future reference. 📚\n\n> See KB article #SEC-2024-001 for similar cases",
        "⚠️ **ESCALATION REQUIRED**: Pattern matches known APT activity.",
        "Investigation shows connection to recent phishing campaign:\n\n- Same C2 infrastructure\n- Similar payload delivery\n- Targeting same user groups",
        "🔍 **Deep dive analysis**:\n\n| Metric | Value |\n|--------|-------|\n| Risk Score | High |\n| Confidence | 85% |\n| IOCs Found | 3 |",
        "Endpoint isolated and forensic imaging in progress. Current status: `QUARANTINED` 🔒",
        "**Timeline reconstruction**:\n\n- `14:30` - Initial compromise\n- `14:45` - Lateral movement detected\n- `15:00` - Data access attempt\n- `15:15` - Alert triggered"
    ]
    
    comments_created = 0
    for alert in alerts:
        # Create 1-4 comments per alert
        num_comments = random.randint(1, 4)
        for j in range(num_comments):
            analyst = random.choice(analysts)
            comment_time = alert.created_at + timedelta(minutes=random.randint(10, 300))
            
            DatabaseService.create_comment(
                db=db,
                alert_id=alert.id,
                author=analyst.full_name,
                content=random.choice(comment_templates),
                created_at=comment_time
            )
            comments_created += 1
    
    print(f"✅ Seeded {comments_created} realistic comments")
    
    # Create AI findings for high/critical alerts
    ai_findings_created = 0
    for alert in alerts:
        if alert.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]:
            # Create 2-4 AI findings per high/critical alert
            num_findings = random.randint(2, 4)
            
            finding_templates = {
                AIFindingType.ROOT_CAUSE: [
                    f"Analysis indicates this alert was triggered by {alert.source} detecting suspicious activity patterns consistent with the MITRE ATT&CK framework.",
                    f"Root cause analysis suggests this incident originated from compromised credentials or privilege escalation attempts.",
                    f"Investigation reveals this alert correlates with known threat actor TTPs documented in recent threat intelligence reports."
                ],
                AIFindingType.SIMILAR_INCIDENTS: [
                    f"Found 3 similar incidents in the past 30 days with comparable attack patterns and indicators.",
                    f"Historical analysis shows 2 related alerts from the same source IP range in the last 14 days.",
                    f"Pattern matching identified 4 similar events targeting the same asset class in recent weeks."
                ],
                AIFindingType.RECOMMENDED_ACTIONS: [
                    "1. Isolate affected systems\n2. Reset compromised credentials\n3. Review access logs\n4. Update security controls",
                    "1. Block malicious IP addresses\n2. Update detection rules\n3. Notify affected users\n4. Implement additional monitoring",
                    "1. Contain the threat\n2. Preserve evidence\n3. Analyze malware samples\n4. Update threat intelligence"
                ],
                AIFindingType.IMPACT_ANALYSIS: [
                    f"Potential impact: {alert.severity.value.upper()}. Estimated affected systems: {random.randint(1, 10)}. Data at risk: Confidential business information.",
                    f"Impact assessment: {alert.severity.value.upper()} severity incident affecting critical infrastructure components.",
                    f"Business impact: {alert.severity.value.upper()} - potential for data exfiltration or service disruption if not contained."
                ]
            }
            
            finding_types = list(AIFindingType)
            selected_types = random.sample(finding_types, min(num_findings, len(finding_types)))
            
            for finding_type in selected_types:
                finding_time = alert.created_at + timedelta(minutes=random.randint(30, 180))
                content = random.choice(finding_templates[finding_type])
                
                DatabaseService.create_ai_finding(
                    db=db,
                    alert_id=alert.id,
                    finding_type=finding_type.value,
                    title=f"{finding_type.value.replace('_', ' ').title()}",
                    content=content,
                    confidence=random.uniform(0.75, 0.95),
                    created_at=finding_time
                )
                ai_findings_created += 1
    
    print(f"✅ Seeded {ai_findings_created} AI findings")
    
    # Create some file resources for alerts
    file_resources_created = 0
    file_types = [
        ("network_capture.pcap", "application/vnd.tcpdump.pcap", False, 2048000, {"packets": 15420}),
        ("malware_sample.exe", "application/x-msdownload", False, 512000, {"hash": "a1b2c3d4e5f6"}),
        ("screenshot_evidence.png", "image/png", True, 245760, {"width": 1920, "height": 1080}),
        ("incident_timeline.pdf", "application/pdf", False, 892340, {"pages": 8}),
        ("log_analysis.txt", "text/plain", False, 156780, {"lines": 2341})
    ]
    
    for alert in alerts[:10]:  # Add files to first 10 alerts
        num_files = random.randint(1, 3)
        for i in range(num_files):
            filename, file_type, is_image, size, metadata = random.choice(file_types)
            analyst = random.choice(analysts)
            
            # Use placeholder images for demo - using more reliable sources
            if is_image:
                # Use different image sources for variety and reliability
                image_sources = [
                    f"https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Security+Log+{alert.id}-{i}",
                    f"https://via.placeholder.com/400x300/DC2626/FFFFFF?text=Alert+Evidence+{alert.id}-{i}",
                    f"https://via.placeholder.com/400x300/059669/FFFFFF?text=Network+Trace+{alert.id}-{i}",
                    f"https://via.placeholder.com/400x300/7C2D12/FFFFFF?text=System+Screenshot+{alert.id}-{i}",
                ]
                file_path = image_sources[(alert.id + i) % len(image_sources)]
                thumb_path = file_path.replace("400x300", "100x100")
            else:
                file_path = f"/uploads/{alert.id}/{filename}"
                thumb_path = None
            
            DatabaseService.create_file_resource(
                db=db,
                alert_id=alert.id,
                filename=filename,
                file_path=file_path,
                file_type=file_type,
                file_size=size,
                is_image=is_image,
                uploaded_by=analyst.full_name,
                uploaded_by_id=analyst.id,
                thumbnail_path=thumb_path,
                metadata=metadata
            )
            file_resources_created += 1
    
    print(f"✅ Seeded {file_resources_created} file resources")
    print("🎉 Panther Labs real alert seeding completed successfully!")


if __name__ == "__main__":
    # Initialize database and create seed data
    init_db()
    db = next(get_db())
    try:
        create_panther_seed_data(db)
    finally:
        db.close()
