#!/usr/bin/env python3
"""
Test script for the authentication system
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_authentication_system():
    """Test the authentication system components"""
    print("🔐 Testing AlertAI Authentication System")
    print("=" * 50)
    
    try:
        # Test 1: Configuration
        print("1. Testing configuration...")
        from app.core.config import settings
        print(f"   ✅ Config loaded successfully")
        print(f"   - Secret key length: {len(settings.secret_key)}")
        print(f"   - Token expire minutes: {settings.access_token_expire_minutes}")
        
        # Test 2: Authentication Service
        print("\n2. Testing authentication service...")
        from app.core.auth import AuthService
        print("   ✅ Auth service imported")
        
        # Test password hashing
        test_password = "TestPassword123"
        hashed_password = AuthService.get_password_hash(test_password)
        password_verified = AuthService.verify_password(test_password, hashed_password)
        print(f"   ✅ Password hashing: {password_verified}")
        
        # Test JWT tokens
        test_data = {"sub": 123, "email": "<EMAIL>"}
        token = AuthService.create_access_token(test_data)
        decoded_payload = AuthService.verify_token(token)
        token_valid = decoded_payload["sub"] == 123 and decoded_payload["email"] == "<EMAIL>"
        print(f"   ✅ JWT tokens: {token_valid}")
        
        # Test 3: Database Models
        print("\n3. Testing database models...")
        from app.models.database.user import DBUser, DBRole, DBPermission
        print("   ✅ User models imported")
        
        from app.models.database.alert import DBAlert
        print("   ✅ Alert models imported")
        
        # Test 4: API Schemas
        print("\n4. Testing API schemas...")
        from app.models.schemas.auth import Token, UserCreate, UserResponse
        print("   ✅ Auth schemas imported")
        
        # Test 5: Services
        print("\n5. Testing services...")
        from app.services.user import UserService
        from app.services.audit import AuditService
        print("   ✅ User and audit services imported")
        
        # Test 6: Rate Limiting
        print("\n6. Testing rate limiting...")
        from app.core.rate_limiting import RateLimitMiddleware, RateLimitConfig
        rate_limiter = RateLimitMiddleware()
        print("   ✅ Rate limiting system ready")
        
        # Test 7: API Endpoints
        print("\n7. Testing API endpoints...")
        from app.api.v1.endpoints.auth import router as auth_router
        from app.api.v1.endpoints.audit import router as audit_router
        print("   ✅ Auth and audit endpoints imported")
        
        # Test 8: Main Application
        print("\n8. Testing main application...")
        from app.main import create_application
        app = create_application()
        print("   ✅ Application created successfully")
        
        # Count routes
        route_count = len([route for route in app.routes if hasattr(route, 'path')])
        print(f"   - Total routes: {route_count}")
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Authentication system is ready for use!")
        print("\nNext steps:")
        print("1. Start the server: uvicorn app.main:app --reload")
        print("2. Initialize system: POST /api/auth/init-system")
        print("3. Register users: POST /api/auth/register")
        print("4. Login: POST /api/auth/token")
        print("5. Access protected endpoints with Bearer token")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_authentication_system()
    sys.exit(0 if success else 1)
