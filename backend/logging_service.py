"""
Centralized Logging Service for AlertAI
Captures all user interactions and system events in a standardized format
"""

import hashlib
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from database import DBSystemLog
from models import SystemLogCreate


class LoggingService:
    """Centralized service for system-wide logging"""
    
    @staticmethod
    def _generate_checksum(log_data: dict) -> str:
        """Generate SHA-256 checksum for tamper detection"""
        # Create a deterministic string from log data
        checksum_data = {
            'timestamp': log_data.get('timestamp'),
            'event_type': log_data.get('event_type'),
            'user_id': log_data.get('user_id'),
            'action': log_data.get('action'),
            'resource_type': log_data.get('resource_type'),
            'resource_id': log_data.get('resource_id'),
            'success': log_data.get('success')
        }
        
        # Convert to JSON string and hash
        json_str = json.dumps(checksum_data, sort_keys=True, default=str)
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    @staticmethod
    def log_event(
        db: Session,
        event_type: str,
        action: str,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        request_size_bytes: Optional[int] = None,
        response_size_bytes: Optional[int] = None,
        log_level: str = 'INFO'
    ) -> DBSystemLog:
        """Log a system event with all relevant context"""
        
        # Prepare log data
        log_data = {
            'timestamp': datetime.now(),
            'event_type': event_type,
            'user_id': user_id,
            'username': username,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'session_id': session_id,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            'details': details or {},
            'success': success,
            'error_message': error_message,
            'response_time_ms': response_time_ms,
            'request_size_bytes': request_size_bytes,
            'response_size_bytes': response_size_bytes,
            'log_level': log_level.upper()
        }
        
        # Generate checksum for tamper detection
        checksum = LoggingService._generate_checksum(log_data)
        
        # Create database record
        db_log = DBSystemLog(
            timestamp=log_data['timestamp'],
            event_type=event_type,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            details=details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms,
            request_size_bytes=request_size_bytes,
            response_size_bytes=response_size_bytes,
            log_level=log_level.upper(),
            checksum=checksum
        )
        
        db.add(db_log)
        db.commit()
        db.refresh(db_log)
        
        return db_log
    
    @staticmethod
    def log_alert_event(
        db: Session,
        action: str,
        alert_id: int,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log alert-related events"""
        return LoggingService.log_event(
            db=db,
            event_type="alert_management",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="alert",
            resource_id=str(alert_id),
            details=details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )
    
    @staticmethod
    def log_bitzy_interaction(
        db: Session,
        action: str,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        alert_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log Bitzy AI interactions"""
        return LoggingService.log_event(
            db=db,
            event_type="bitzy_interaction",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="chat" if alert_id is None else "alert_chat",
            resource_id=str(alert_id) if alert_id else None,
            details=details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )
    
    @staticmethod
    def log_source_event(
        db: Session,
        action: str,
        source_id: int,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log webhook source events"""
        return LoggingService.log_event(
            db=db,
            event_type="source_management",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="webhook_source",
            resource_id=str(source_id),
            details=details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )
    
    @staticmethod
    def log_user_action(
        db: Session,
        action: str,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log general user actions"""
        return LoggingService.log_event(
            db=db,
            event_type="user_action",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="user",
            resource_id=user_id,
            details=details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )
    
    @staticmethod
    def log_file_operation(
        db: Session,
        action: str,
        file_id: int,
        alert_id: Optional[int] = None,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log file operations"""
        return LoggingService.log_event(
            db=db,
            event_type="file_operation",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="file",
            resource_id=str(file_id),
            details={**(details or {}), "alert_id": alert_id} if alert_id else details,
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )
    
    @staticmethod
    def log_comment_event(
        db: Session,
        action: str,
        comment_id: int,
        alert_id: int,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> DBSystemLog:
        """Log comment-related events"""
        return LoggingService.log_event(
            db=db,
            event_type="comment_management",
            action=action,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            resource_type="comment",
            resource_id=str(comment_id),
            details={**(details or {}), "alert_id": alert_id},
            success=success,
            error_message=error_message,
            response_time_ms=response_time_ms
        )


# Global logging service instance
logging_service = LoggingService()
