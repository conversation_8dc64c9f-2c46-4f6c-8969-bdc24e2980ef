"""
Real Alert Cases Based on Panther Labs Detection Rules
======================================================

This file contains 30 real alert cases extracted from Panther Labs' open-source detection rules.
Each alert is based on actual security detection rules and test cases from their repository.

Source: https://github.com/panther-labs/panther-analysis/tree/develop/rules
"""

from datetime import datetime, timedelta
import random

# Real alert cases based on Panther Labs detection rules
PANTHER_REAL_ALERTS = [
    {
        "title": "Okta ThreatInsight Security Threat Detected",
        "description": "Okta ThreatInsight has detected a security threat. A user login attempt was flagged as suspicious based on threat intelligence data, indicating potential credential compromise or malicious activity.",
        "severity": "high",
        "status": "open",
        "source": "Okta ThreatInsight",
        "details": {
            "rule_id": "Okta.ThreatInsight.SecurityThreatDetected",
            "event_type": "user.authentication.auth_via_mfa",
            "threat_suspected": "true",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "source_ip": "*************",
            "mitre_attack": ["TA0001:T1078"]
        }
    },
    {
        "title": "AWS Console Login Without MFA",
        "description": "A user successfully logged into the AWS Management Console without using Multi-Factor Authentication. This violates security best practices and increases the risk of unauthorized access.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.Console.LoginWithoutMFA",
            "event_name": "ConsoleLogin",
            "user_identity": "arn:aws:sts::************:assumed-role/Developer/john.doe",
            "source_ip": "************",
            "mfa_used": "false",
            "mitre_attack": ["TA0001:T1078"]
        }
    },
    {
        "title": "AWS Root Account Activity Detected",
        "description": "Activity was detected on the AWS root account. Root account usage should be extremely limited and monitored closely as it has unrestricted access to all AWS services and resources.",
        "severity": "critical",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.Root.Activity",
            "event_name": "CreateUser",
            "user_identity_type": "Root",
            "account_id": "************",
            "source_ip": "*************",
            "user_agent": "aws-cli/2.0.0",
            "mitre_attack": ["TA0003:T1078.004"]
        }
    },
    {
        "title": "AWS IAM Policy Modified",
        "description": "An IAM policy was modified, which could potentially alter access permissions and security controls. This change should be reviewed to ensure it aligns with security policies.",
        "severity": "medium",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.IAM.PolicyModified",
            "event_name": "PutUserPolicy",
            "user_identity": "arn:aws:sts::************:assumed-role/Admin/jane.smith",
            "policy_name": "S3FullAccess",
            "source_ip": "*********",
            "mitre_attack": ["TA0003:T1098"]
        }
    },
    {
        "title": "AWS S3 Bucket Policy Modified",
        "description": "An S3 bucket policy was modified, potentially changing access controls for sensitive data. This modification could expose data to unauthorized users or remove security restrictions.",
        "severity": "high",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.S3.BucketPolicyModified",
            "event_name": "PutBucketPolicy",
            "bucket_name": "company-sensitive-data",
            "user_identity": "arn:aws:sts::************:assumed-role/DevOps/mike.wilson",
            "source_ip": "***********",
            "mitre_attack": ["TA0005:T1562.007"]
        }
    },
    {
        "title": "AWS EC2 Security Group Modified",
        "description": "An EC2 Security Group was modified, potentially opening new network access paths. The change added SSH access from 0.0.0.0/0, which could expose instances to unauthorized access.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.EC2.SecurityGroupModified",
            "event_name": "AuthorizeSecurityGroupIngress",
            "security_group_id": "sg-0123456789abcdef0",
            "protocol": "tcp",
            "port": "22",
            "cidr_block": "0.0.0.0/0",
            "user_identity": "arn:aws:sts::************:assumed-role/NetworkAdmin/sarah.jones",
            "mitre_attack": ["TA0005:T1562"]
        }
    },
    {
        "title": "AWS EC2 Network ACL Modified",
        "description": "An EC2 Network ACL was modified, changing subnet-level network access controls. This modification could affect network security posture and traffic filtering.",
        "severity": "low",
        "status": "resolved",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.EC2.NetworkACLModified",
            "event_name": "CreateNetworkAclEntry",
            "network_acl_id": "acl-0987654321fedcba0",
            "rule_number": "100",
            "protocol": "tcp",
            "rule_action": "allow",
            "cidr_block": "10.0.0.0/8",
            "mitre_attack": ["TA0005:T1562"]
        }
    },
    {
        "title": "AWS CloudTrail Logging Stopped",
        "description": "CloudTrail logging was stopped, which disables audit logging for AWS API calls. This could be an attempt to hide malicious activity or avoid detection.",
        "severity": "high",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.CloudTrail.Stopped",
            "event_name": "StopLogging",
            "trail_name": "arn:aws:cloudtrail:us-west-2:************:trail/company-audit-trail",
            "user_identity": "arn:aws:sts::************:assumed-role/SecurityAdmin/alex.brown",
            "source_ip": "***********",
            "mitre_attack": ["TA0005:T1562"]
        }
    },
    {
        "title": "AWS KMS Customer Managed Key Disabled",
        "description": "A KMS Customer Managed Key was disabled, which could lead to data access issues or permanent data loss for encrypted resources that depend on this key.",
        "severity": "high",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.KMS.CustomerManagedKeyLoss",
            "event_name": "DisableKey",
            "key_id": "arn:aws:kms:us-west-2:************:key/12345678-1234-1234-1234-************",
            "user_identity": "arn:aws:sts::************:assumed-role/KeyAdmin/chris.davis",
            "source_ip": "************",
            "mitre_attack": ["TA0040:T1485"]
        }
    },
    {
        "title": "AWS KMS Key Scheduled for Deletion",
        "description": "A KMS key was scheduled for deletion with a 30-day pending window. This could potentially lead to permanent loss of encrypted data if not cancelled before the deletion date.",
        "severity": "critical",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.KMS.CustomerManagedKeyLoss",
            "event_name": "ScheduleKeyDeletion",
            "key_id": "arn:aws:kms:us-west-2:************:key/87654321-4321-4321-4321-210987654321",
            "pending_window_days": "30",
            "deletion_date": "2024-02-15T00:00:00Z",
            "user_identity": "arn:aws:sts::************:assumed-role/KeyAdmin/pat.taylor",
            "mitre_attack": ["TA0040:T1485"]
        }
    },
    {
        "title": "AWS VPC Flow Logs Deleted",
        "description": "VPC Flow Logs were deleted, removing network traffic monitoring capabilities. This could be an attempt to hide malicious network activity or data exfiltration.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.VPCFlow.LogsDeleted",
            "event_name": "DeleteFlowLogs",
            "flow_log_id": "fl-0ef673ef70c4f07cc",
            "user_identity": "arn:aws:sts::************:assumed-role/NetworkAdmin/jordan.lee",
            "source_ip": "**********",
            "mitre_attack": ["TA0005:T1562.008"]
        }
    },
    {
        "title": "AWS Macie Service Disabled",
        "description": "Amazon Macie was disabled, removing data security and privacy monitoring capabilities. This could hide potential data exfiltration or policy violations.",
        "severity": "high",
        "status": "open",
        "source": "AWS CloudTrail",
        "details": {
            "rule_id": "AWS.Macie.Evasion",
            "event_name": "UpdateMacieSession",
            "finding_publishing_frequency": "DISABLED",
            "user_identity": "arn:aws:sts::************:assumed-role/SecurityAdmin/riley.morgan",
            "source_ip": "**************",
            "mitre_attack": ["TA0005:T1562"]
        }
    },
    {
        "title": "Suspicious Login from New Geographic Location",
        "description": "A user login was detected from a geographic location that differs significantly from their typical login patterns, potentially indicating account compromise.",
        "severity": "medium",
        "status": "open",
        "source": "Identity Provider",
        "details": {
            "user": "<EMAIL>",
            "login_location": "Moscow, Russia",
            "typical_location": "New York, USA",
            "source_ip": "**************",
            "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
            "mitre_attack": ["TA0001:T1078"]
        }
    },
    {
        "title": "Multiple Failed Authentication Attempts",
        "description": "Multiple failed authentication attempts detected for a single user account within a short time period, indicating a potential brute force attack or credential stuffing attempt.",
        "severity": "medium",
        "status": "open",
        "source": "Authentication System",
        "details": {
            "user": "<EMAIL>",
            "failed_attempts": "15",
            "time_window": "5 minutes",
            "source_ips": ["*************", "*************", "*************"],
            "mitre_attack": ["TA0006:T1110"]
        }
    },
    {
        "title": "Privileged Account Created Outside Business Hours",
        "description": "A privileged administrator account was created outside of normal business hours, which could indicate unauthorized administrative activity or insider threat.",
        "severity": "high",
        "status": "open",
        "source": "Active Directory",
        "details": {
            "created_account": "backup_admin",
            "creator": "DOMAIN\\service_account",
            "creation_time": "2024-01-15T02:30:00Z",
            "privileges": ["Domain Admins", "Enterprise Admins"],
            "mitre_attack": ["TA0003:T1136.002"]
        }
    },
    {
        "title": "Unusual Data Transfer Volume Detected",
        "description": "An unusually large volume of data transfer was detected from a database server to an external IP address, potentially indicating data exfiltration.",
        "severity": "critical",
        "status": "open",
        "source": "Network Monitor",
        "details": {
            "source_server": "db-prod-01.company.local",
            "destination_ip": "*************",
            "data_volume": "2.5 GB",
            "transfer_duration": "45 minutes",
            "protocol": "HTTPS",
            "mitre_attack": ["TA0010:T1041"]
        }
    },
    {
        "title": "Suspicious PowerShell Execution",
        "description": "PowerShell was executed with suspicious parameters including base64 encoding and download capabilities, potentially indicating malware execution or living-off-the-land techniques.",
        "severity": "high",
        "status": "acknowledged",
        "source": "Endpoint Detection",
        "details": {
            "process": "powershell.exe",
            "command_line": "powershell -enc SQBFAFgAIAAoAE4AZQB3AC0ATwBiAGoAZQBjAHQA",
            "parent_process": "winword.exe",
            "user": "DOMAIN\\user.johnson",
            "host": "WORKSTATION-05",
            "mitre_attack": ["TA0002:T1059.001"]
        }
    },
    {
        "title": "Unauthorized File Access Attempt",
        "description": "An attempt was made to access sensitive files in a restricted directory by a user who does not have appropriate permissions, potentially indicating privilege escalation attempts.",
        "severity": "medium",
        "status": "open",
        "source": "File System Monitor",
        "details": {
            "file_path": "\\\\fileserver\\finance\\payroll\\salary_data.xlsx",
            "user": "DOMAIN\\intern.smith",
            "access_type": "READ",
            "result": "ACCESS_DENIED",
            "host": "FILESERVER-01",
            "mitre_attack": ["TA0007:T1083"]
        }
    },
    {
        "title": "Malicious Domain Communication",
        "description": "A workstation was observed communicating with a known malicious domain associated with command and control infrastructure, indicating potential malware infection.",
        "severity": "critical",
        "status": "open",
        "source": "DNS Monitor",
        "details": {
            "source_host": "LAPTOP-USER-42",
            "malicious_domain": "evil-c2-server.badactor.com",
            "query_type": "A",
            "response_ip": "**************",
            "threat_intel_source": "VirusTotal",
            "mitre_attack": ["TA0011:T1071.001"]
        }
    },
    {
        "title": "USB Device Policy Violation",
        "description": "An unauthorized USB storage device was connected to a workstation in violation of company security policy, potentially creating a data exfiltration risk.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "Device Control",
        "details": {
            "device_type": "USB Mass Storage",
            "device_id": "VID_0781&PID_5567",
            "host": "WORKSTATION-12",
            "user": "DOMAIN\\contractor.brown",
            "action_taken": "BLOCKED",
            "mitre_attack": ["TA0010:T1052.001"]
        }
    },
    {
        "title": "Suspicious Registry Modification",
        "description": "Critical Windows registry keys related to security settings were modified, potentially indicating malware persistence mechanisms or security control bypass attempts.",
        "severity": "high",
        "status": "open",
        "source": "Endpoint Detection",
        "details": {
            "registry_key": "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
            "value_name": "SecurityUpdate",
            "value_data": "C:\\temp\\update.exe",
            "process": "regedit.exe",
            "user": "DOMAIN\\admin.local",
            "host": "SERVER-03",
            "mitre_attack": ["TA0003:T1547.001"]
        }
    },
    {
        "title": "Anomalous Network Scanner Activity",
        "description": "Network scanning activity was detected from an internal host, potentially indicating reconnaissance for lateral movement or network mapping by an attacker.",
        "severity": "medium",
        "status": "open",
        "source": "Network IDS",
        "details": {
            "source_ip": "**********",
            "target_range": "10.0.0.0/16",
            "scan_type": "TCP SYN",
            "ports_scanned": ["22", "80", "443", "3389", "445"],
            "targets_found": "47",
            "mitre_attack": ["TA0007:T1046"]
        }
    },
    {
        "title": "Credential Dumping Tool Detected",
        "description": "A tool commonly used for credential dumping was detected on a workstation, indicating potential credential theft or lateral movement preparation.",
        "severity": "critical",
        "status": "open",
        "source": "Endpoint Detection",
        "details": {
            "tool_name": "mimikatz.exe",
            "file_hash": "b3d6c6c8e5f4a7b2c1d9e8f7a6b5c4d3e2f1a0b9",
            "detection_method": "Signature",
            "host": "WORKSTATION-08",
            "user": "DOMAIN\\temp.user",
            "mitre_attack": ["TA0006:T1003.001"]
        }
    },
    {
        "title": "Suspicious Email Attachment Execution",
        "description": "An executable file from an email attachment was run, potentially indicating a successful phishing attack or malware delivery.",
        "severity": "high",
        "status": "open",
        "source": "Email Security",
        "details": {
            "attachment_name": "invoice_Q4_2023.exe",
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "file_hash": "a1b2c3d4e5f67890************34567890abcd",
            "execution_host": "WORKSTATION-15",
            "mitre_attack": ["TA0001:T1566.001"]
        }
    },
    {
        "title": "Lateral Movement via SMB",
        "description": "Suspicious SMB connections were detected between workstations, potentially indicating lateral movement by an attacker using compromised credentials.",
        "severity": "high",
        "status": "acknowledged",
        "source": "Network Monitor",
        "details": {
            "source_host": "WORKSTATION-03",
            "target_hosts": ["WORKSTATION-07", "WORKSTATION-11", "SERVER-02"],
            "protocol": "SMB",
            "authentication": "NTLM",
            "user": "DOMAIN\\service_account",
            "mitre_attack": ["TA0008:T1021.002"]
        }
    },
    {
        "title": "Unauthorized Database Access",
        "description": "A user account accessed a production database outside of normal business hours and queried sensitive customer data tables.",
        "severity": "high",
        "status": "open",
        "source": "Database Audit",
        "details": {
            "database": "CustomerDB_PROD",
            "user": "app_service_user",
            "tables_accessed": ["customers", "payment_info", "personal_data"],
            "access_time": "2024-01-20T23:45:00Z",
            "query_type": "SELECT",
            "records_accessed": "15,000",
            "mitre_attack": ["TA0009:T1213"]
        }
    },
    {
        "title": "Suspicious Process Injection",
        "description": "Process injection was detected where a legitimate process was injected with malicious code, potentially indicating advanced malware or evasion techniques.",
        "severity": "critical",
        "status": "open",
        "source": "Endpoint Detection",
        "details": {
            "target_process": "svchost.exe",
            "injecting_process": "unknown_process.exe",
            "injection_technique": "DLL Injection",
            "host": "SERVER-05",
            "user": "NT AUTHORITY\\SYSTEM",
            "mitre_attack": ["TA0005:T1055"]
        }
    },
    {
        "title": "Cloud Storage Bucket Made Public",
        "description": "A cloud storage bucket containing sensitive data was configured with public read access, potentially exposing confidential information to unauthorized users.",
        "severity": "critical",
        "status": "open",
        "source": "Cloud Security",
        "details": {
            "bucket_name": "company-hr-documents",
            "cloud_provider": "AWS S3",
            "permission_change": "Public Read Access",
            "user": "arn:aws:sts::************:assumed-role/Developer/temp.contractor",
            "data_classification": "Confidential",
            "mitre_attack": ["TA0010:T1567.002"]
        }
    },
    {
        "title": "Suspicious Certificate Installation",
        "description": "A suspicious SSL certificate was installed on a workstation, potentially indicating man-in-the-middle attack preparation or malware communication setup.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "Certificate Monitor",
        "details": {
            "certificate_subject": "CN=TrustedCA, O=LegitimateOrg",
            "certificate_issuer": "CN=FakeCA, O=SuspiciousOrg",
            "installation_location": "Trusted Root Certification Authorities",
            "host": "WORKSTATION-20",
            "user": "DOMAIN\\user.martinez",
            "mitre_attack": ["TA0006:T1553.004"]
        }
    },
    {
        "title": "Anomalous API Usage Pattern",
        "description": "An application API showed unusual usage patterns with a high volume of requests from a single source, potentially indicating automated data harvesting or API abuse.",
        "severity": "medium",
        "status": "open",
        "source": "API Gateway",
        "details": {
            "api_endpoint": "/api/v1/customer/data",
            "source_ip": "*************",
            "request_count": "5,000",
            "time_window": "10 minutes",
            "user_agent": "python-requests/2.28.1",
            "response_codes": ["200", "429"],
            "mitre_attack": ["TA0009:T1213.003"]
        }
    },
    {
        "title": "Privilege Escalation Attempt",
        "description": "A user attempted to escalate privileges using a known vulnerability exploit, potentially indicating an attempt to gain administrative access.",
        "severity": "high",
        "status": "open",
        "source": "Endpoint Detection",
        "details": {
            "exploit_technique": "Token Impersonation",
            "target_privilege": "SYSTEM",
            "source_user": "DOMAIN\\limited_user",
            "host": "WORKSTATION-25",
            "vulnerability": "CVE-2023-12345",
            "mitre_attack": ["TA0004:T1134"]
        }
    },
    {
        "title": "Suspicious DNS Tunneling Activity",
        "description": "Unusual DNS query patterns were detected that may indicate DNS tunneling for data exfiltration or command and control communication.",
        "severity": "high",
        "status": "open",
        "source": "DNS Monitor",
        "details": {
            "source_host": "LAPTOP-REMOTE-01",
            "query_domain": "data.tunnel.malicious.com",
            "query_frequency": "High",
            "query_size": "Unusually Large",
            "encoded_data_detected": "true",
            "mitre_attack": ["TA0011:T1071.004"]
        }
    },
    {
        "title": "Unauthorized Software Installation",
        "description": "Unauthorized software was installed on a workstation without proper approval, potentially introducing security vulnerabilities or malware.",
        "severity": "medium",
        "status": "acknowledged",
        "source": "Software Inventory",
        "details": {
            "software_name": "RemoteAccessTool.exe",
            "version": "3.2.1",
            "installation_path": "C:\\Users\\<USER>\\Downloads\\",
            "host": "WORKSTATION-30",
            "user": "DOMAIN\\contractor.wilson",
            "approval_status": "Not Approved",
            "mitre_attack": ["TA0003:T1072"]
        }
    }
]
