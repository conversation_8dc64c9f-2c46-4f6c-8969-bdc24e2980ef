# AlertAI Backend Configuration
# Copy this file to .env and adjust values as needed

# API Configuration
API_HOST=0.0.0.0
API_PORT=8001
API_BASE_URL=http://localhost:8001

# Frontend URLs for CORS (comma-separated)
FRONTEND_URLS=http://localhost:3000,http://localhost:3001

# Database Configuration
DATABASE_URL=sqlite:///./alertai.db
DATABASE_ECHO=false

# Security Configuration
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,text/plain,application/pdf,application/json
UPLOAD_DIRECTORY=uploads

# AI Service Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:8b
AI_REQUEST_TIMEOUT=120

# Threat Intelligence Tools Configuration
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
VIRUSTOTAL_RATE_LIMIT_DELAY=15.0

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Pagination Defaults
DEFAULT_PAGE_SIZE=100
MAX_PAGE_SIZE=1000

# Cache Configuration
CACHE_TTL=300

# Development/Production flags
DEBUG=false
TESTING=false
