from pydantic import BaseModel
from datetime import datetime
from enum import Enum
from typing import Optional, List


class AlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    OPEN = "open"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"


class Alert(BaseModel):
    id: int
    title: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    source: str
    assigned_analyst_id: Optional[int] = None
    assigned_analyst_name: Optional[str] = None
    tags: Optional[List[str]] = []
    due_date: Optional[datetime] = None
    priority: Optional[int] = None  # 1-5 scale
    investigation_notes: Optional[str] = None
    mitre_techniques: Optional[List[str]] = []
    escalation_level: Optional[int] = 0  # 0-3 scale
    sla_deadline: Optional[datetime] = None
    is_deleted: Optional[bool] = False

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class AlertResponse(BaseModel):
    alerts: list[Alert]
    total: int


class Comment(BaseModel):
    id: int
    alert_id: int
    author: str
    content: str
    created_at: datetime

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class CommentCreate(BaseModel):
    content: str
    author: str


class AIFindingType(str, Enum):
    ROOT_CAUSE = "root_cause"
    SIMILAR_INCIDENTS = "similar_incidents"
    RECOMMENDED_ACTIONS = "recommended_actions"
    IMPACT_ANALYSIS = "impact_analysis"


class AIFinding(BaseModel):
    id: int
    alert_id: int
    type: AIFindingType
    title: str
    content: str
    confidence: float  # 0.0 to 1.0
    created_at: datetime

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class UploadedFile(BaseModel):
    id: int
    alert_id: int
    filename: str
    file_path: str
    file_type: str
    file_size: int
    uploaded_by: str
    uploaded_at: datetime

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ChatSession(BaseModel):
    id: int
    user_id: str
    alert_id: Optional[int] = None
    title: str
    is_incognito: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    is_active: bool = True
    message_count: Optional[int] = 0  # Number of messages in session

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ChatMessage(BaseModel):
    id: int
    session_id: int
    user_message: str
    ai_response: str
    message_order: int
    created_at: datetime
    response_time_ms: Optional[int] = None
    context_data: Optional[dict] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ChatSessionWithMessages(BaseModel):
    session: ChatSession
    messages: List[ChatMessage]

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ChatRequest(BaseModel):
    message: str
    user_id: Optional[str] = "current_user"
    session_id: Optional[int] = None  # If None, creates new session
    page_context: Optional[dict] = None
    is_incognito: Optional[bool] = False


class EditMessageRequest(BaseModel):
    new_message: str
    user_id: Optional[str] = "current_user"
    page_context: Optional[dict] = None


class ChatResponse(BaseModel):
    message: str
    response: str
    session_id: int
    message_id: int
    created_at: datetime
    is_new_session: bool = False

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ChatSessionCreateRequest(BaseModel):
    user_id: Optional[str] = "current_user"
    alert_id: Optional[int] = None
    title: Optional[str] = None
    is_incognito: Optional[bool] = False


class ChatSessionListResponse(BaseModel):
    sessions: List[ChatSession]
    total_count: int

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class Analyst(BaseModel):
    id: int
    username: str
    full_name: str
    email: str
    avatar_url: Optional[str] = None
    role: str
    is_active: bool = True

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class CommentMention(BaseModel):
    id: int
    comment_id: int
    analyst_id: int
    analyst_username: str
    created_at: datetime

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class EnhancedComment(BaseModel):
    id: int
    alert_id: int
    author: str
    author_id: int
    content: str
    original_content: Optional[str] = None  # For AI beautified comments
    is_beautified: bool = False
    mentions: List[CommentMention] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class CommentBeautifyRequest(BaseModel):
    content: str


class CommentBeautifyResponse(BaseModel):
    original_content: str
    beautified_content: str
    created_at: datetime

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class AlertCreateRequest(BaseModel):
    title: str
    description: str
    severity: AlertSeverity
    source: str
    status: AlertStatus = AlertStatus.OPEN
    assigned_analyst_id: Optional[int] = None
    tags: Optional[List[str]] = []
    due_date: Optional[datetime] = None


class AlertCreateResponse(BaseModel):
    alert: Alert
    message: str

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class AlertUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    severity: Optional[AlertSeverity] = None
    status: Optional[AlertStatus] = None
    source: Optional[str] = None
    assigned_analyst_id: Optional[int] = None
    tags: Optional[List[str]] = None
    due_date: Optional[datetime] = None


class AlertUpdateResponse(BaseModel):
    alert: Alert
    message: str

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class AlertDeleteResponse(BaseModel):
    message: str
    deleted_alert_id: int


class AlertChangeLog(BaseModel):
    id: int
    alert_id: int
    changed_by: str
    changed_at: datetime
    change_type: str
    field_name: Optional[str] = None
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    description: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class AlertChangeLogResponse(BaseModel):
    change_logs: List[AlertChangeLog]
    total_count: int

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class SystemLog(BaseModel):
    id: int
    timestamp: datetime
    event_type: str
    user_id: Optional[str] = None
    username: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: str
    details: Optional[dict] = None
    success: bool = True
    error_message: Optional[str] = None
    response_time_ms: Optional[int] = None
    request_size_bytes: Optional[int] = None
    response_size_bytes: Optional[int] = None
    log_level: str = 'INFO'
    checksum: Optional[str] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class SystemLogCreate(BaseModel):
    event_type: str
    user_id: Optional[str] = None
    username: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: str
    details: Optional[dict] = None
    success: bool = True
    error_message: Optional[str] = None
    response_time_ms: Optional[int] = None
    request_size_bytes: Optional[int] = None
    response_size_bytes: Optional[int] = None
    log_level: str = 'INFO'


class SystemLogFilter(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    event_types: Optional[List[str]] = None
    user_ids: Optional[List[str]] = None
    usernames: Optional[List[str]] = None
    ip_addresses: Optional[List[str]] = None
    resource_types: Optional[List[str]] = None
    resource_ids: Optional[List[str]] = None
    actions: Optional[List[str]] = None
    success: Optional[bool] = None
    log_levels: Optional[List[str]] = None
    search_query: Optional[str] = None  # Search across all text fields
    limit: int = 100
    offset: int = 0

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class SystemLogResponse(BaseModel):
    logs: List[SystemLog]
    total_count: int
    filtered_count: int
    has_more: bool

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class SystemLogStats(BaseModel):
    total_logs: int
    logs_today: int
    error_rate_24h: float
    top_event_types: List[dict]
    top_users: List[dict]
    top_ip_addresses: List[dict]
    avg_response_time_ms: Optional[float] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileResource(BaseModel):
    id: int
    alert_id: int
    filename: str
    file_path: str
    file_type: str
    file_size: int
    is_image: bool
    thumbnail_path: Optional[str] = None
    uploaded_by: str
    uploaded_by_id: int
    uploaded_at: datetime
    metadata: Optional[dict] = {}

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


# Webhook Integration Models
class AuthenticationType(str, Enum):
    CUSTOM_HEADER = "custom_header"
    BEARER_TOKEN = "bearer_token"


class WebhookSource(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    webhook_url: str
    webhook_secret: str  # Unique secret for URL generation
    auth_type: AuthenticationType
    auth_header_name: Optional[str] = None
    auth_header_value: Optional[str] = None
    auth_bearer_token: Optional[str] = None
    json_mapping: dict  # JSON schema mapping configuration
    is_active: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    alert_count: int = 0  # Number of alerts created through this source

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class WebhookSourceCreate(BaseModel):
    name: str
    description: Optional[str] = None
    auth_type: AuthenticationType
    auth_header_name: Optional[str] = None
    auth_header_value: Optional[str] = None
    auth_bearer_token: Optional[str] = None
    json_mapping: dict


class WebhookSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    auth_type: Optional[AuthenticationType] = None
    auth_header_name: Optional[str] = None
    auth_header_value: Optional[str] = None
    auth_bearer_token: Optional[str] = None
    json_mapping: Optional[dict] = None
    is_active: Optional[bool] = None


class WebhookSourceResponse(BaseModel):
    source: WebhookSource
    webhook_url: str  # Full webhook URL for external systems

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class WebhookSourceListResponse(BaseModel):
    sources: List[WebhookSource]
    total_count: int

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class WebhookPayload(BaseModel):
    """Model for incoming webhook payloads"""
    data: dict  # Raw JSON payload from external system


class WebhookTestRequest(BaseModel):
    source_id: int
    test_payload: dict


class WebhookTestResponse(BaseModel):
    success: bool
    mapped_alert: Optional[dict] = None
    errors: List[str] = []
    warnings: List[str] = []

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
