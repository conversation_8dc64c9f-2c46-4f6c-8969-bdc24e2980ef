"""
Test suite for AlertAI backend improvements
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os
from unittest.mock import patch, MagicMock

from main import app, get_db, validate_file_upload, sanitize_input, validate_pagination
from database import Base
from config import settings
from models import AlertCreateRequest, AlertSeverity, AlertStatus, CommentCreate


# Test database setup
@pytest.fixture
def test_db():
    """Create a test database"""
    engine = create_engine("sqlite:///./test.db", connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    Base.metadata.create_all(bind=engine)
    
    def override_get_db():
        try:
            db = TestingSessionLocal()
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    
    yield TestingSessionLocal()
    
    # Cleanup
    os.remove("./test.db")
    app.dependency_overrides.clear()


@pytest.fixture
def client(test_db):
    """Create a test client"""
    return TestClient(app)


class TestSecurityValidation:
    """Test security validation functions"""
    
    def test_sanitize_input_removes_control_characters(self):
        """Test that control characters are removed"""
        malicious_input = "Hello\x00\x08\x0B\x0C\x0E\x1F\x7FWorld"
        result = sanitize_input(malicious_input)
        assert result == "HelloWorld"
    
    def test_sanitize_input_limits_length(self):
        """Test that input is limited to max length"""
        long_input = "A" * 2000
        result = sanitize_input(long_input, max_length=100)
        assert len(result) == 100
    
    def test_sanitize_input_strips_whitespace(self):
        """Test that whitespace is stripped"""
        input_with_whitespace = "  Hello World  "
        result = sanitize_input(input_with_whitespace)
        assert result == "Hello World"
    
    def test_validate_pagination_enforces_limits(self):
        """Test pagination validation"""
        # Test minimum limit
        limit, offset = validate_pagination(-5, -10)
        assert limit == 1
        assert offset == 0
        
        # Test maximum limit
        limit, offset = validate_pagination(2000, 100)
        assert limit == 1000
        assert offset == 100
    
    def test_validate_file_upload_checks_size(self):
        """Test file upload validation"""
        from fastapi import UploadFile
        from io import BytesIO
        
        # Mock file that's too large
        large_file = MagicMock(spec=UploadFile)
        large_file.filename = "test.txt"
        large_file.size = settings.max_file_size + 1
        large_file.content_type = "text/plain"
        
        with pytest.raises(Exception) as exc_info:
            validate_file_upload(large_file)
        assert "exceeds maximum allowed size" in str(exc_info.value)
    
    def test_validate_file_upload_checks_type(self):
        """Test file type validation"""
        from fastapi import UploadFile
        
        # Mock file with invalid type
        invalid_file = MagicMock(spec=UploadFile)
        invalid_file.filename = "test.exe"
        invalid_file.size = 1000
        invalid_file.content_type = "application/x-executable"
        
        with pytest.raises(Exception) as exc_info:
            validate_file_upload(invalid_file)
        assert "not allowed" in str(exc_info.value)
    
    def test_validate_file_upload_checks_filename(self):
        """Test filename validation for path traversal"""
        from fastapi import UploadFile
        
        # Mock file with malicious filename
        malicious_file = MagicMock(spec=UploadFile)
        malicious_file.filename = "../../../etc/passwd"
        malicious_file.size = 1000
        malicious_file.content_type = "text/plain"
        
        with pytest.raises(Exception) as exc_info:
            validate_file_upload(malicious_file)
        assert "Invalid filename" in str(exc_info.value)


class TestAPIEndpoints:
    """Test API endpoints with improved error handling"""
    
    def test_get_alerts_with_pagination(self, client):
        """Test alerts endpoint with pagination"""
        response = client.get("/api/alerts?limit=10&offset=0")
        assert response.status_code == 200
        data = response.json()
        assert "alerts" in data
        assert "total" in data
    
    def test_get_alerts_validates_pagination(self, client):
        """Test that pagination parameters are validated"""
        # Test with invalid parameters
        response = client.get("/api/alerts?limit=-1&offset=-5")
        assert response.status_code == 200  # Should still work with corrected values
    
    def test_create_comment_validates_input(self, client):
        """Test comment creation with input validation"""
        # First create an alert
        alert_data = {
            "title": "Test Alert",
            "description": "Test Description",
            "severity": "medium",
            "source": "test",
            "status": "open"
        }
        alert_response = client.post("/api/alerts", json=alert_data)
        assert alert_response.status_code == 200
        alert_id = alert_response.json()["alert"]["id"]
        
        # Test with empty content
        comment_data = {
            "author": "Test User",
            "content": ""
        }
        response = client.post(f"/api/alerts/{alert_id}/comments", json=comment_data)
        assert response.status_code == 400
        assert "required" in response.json()["detail"]
        
        # Test with too short content
        comment_data["content"] = "Hi"
        response = client.post(f"/api/alerts/{alert_id}/comments", json=comment_data)
        assert response.status_code == 400
        assert "at least 3 characters" in response.json()["detail"]
    
    def test_file_upload_security(self, client):
        """Test file upload security measures"""
        # First create an alert
        alert_data = {
            "title": "Test Alert",
            "description": "Test Description", 
            "severity": "medium",
            "source": "test",
            "status": "open"
        }
        alert_response = client.post("/api/alerts", json=alert_data)
        alert_id = alert_response.json()["alert"]["id"]
        
        # Test with malicious filename
        with tempfile.NamedTemporaryFile(suffix=".txt") as tmp_file:
            tmp_file.write(b"test content")
            tmp_file.seek(0)
            
            files = {"file": ("../../../etc/passwd", tmp_file, "text/plain")}
            response = client.post(f"/api/alerts/{alert_id}/upload", files=files)
            assert response.status_code == 400
            assert "Invalid filename" in response.json()["detail"]
    
    def test_error_responses_are_consistent(self, client):
        """Test that error responses follow consistent format"""
        # Test 404 error
        response = client.get("/api/alerts/99999")
        assert response.status_code == 404
        assert "detail" in response.json()
        
        # Test validation error
        invalid_alert_data = {
            "title": "",  # Empty title should cause validation error
            "description": "Test",
            "severity": "invalid_severity",
            "source": "test"
        }
        response = client.post("/api/alerts", json=invalid_alert_data)
        assert response.status_code in [400, 422]
        assert "detail" in response.json()


class TestPerformanceImprovements:
    """Test performance improvements"""
    
    def test_pagination_reduces_response_size(self, client):
        """Test that pagination actually limits response size"""
        # Get all alerts
        response_all = client.get("/api/alerts")
        all_alerts = response_all.json()["alerts"]
        
        # Get limited alerts
        response_limited = client.get("/api/alerts?limit=5")
        limited_alerts = response_limited.json()["alerts"]
        
        # If there are more than 5 alerts, limited should be smaller
        if len(all_alerts) > 5:
            assert len(limited_alerts) <= 5
    
    @patch('main.DatabaseService.get_alerts')
    def test_database_queries_use_pagination(self, mock_get_alerts, client):
        """Test that database queries use pagination parameters"""
        mock_get_alerts.return_value = []
        
        client.get("/api/alerts?limit=10&offset=20")
        
        # Verify that the database service was called with pagination
        mock_get_alerts.assert_called_once()
        args, kwargs = mock_get_alerts.call_args
        assert 'limit' in kwargs or len(args) >= 3
        assert 'offset' in kwargs or len(args) >= 4


class TestErrorHandling:
    """Test comprehensive error handling"""
    
    def test_handles_database_errors_gracefully(self, client):
        """Test that database errors are handled gracefully"""
        with patch('main.DatabaseService.get_alerts') as mock_get_alerts:
            mock_get_alerts.side_effect = Exception("Database connection failed")
            
            response = client.get("/api/alerts")
            assert response.status_code == 500
            assert "error" in response.json()["detail"].lower()
    
    def test_handles_ai_service_unavailable(self, client):
        """Test handling when AI service is unavailable"""
        with patch('main.ollama_service.is_service_available') as mock_available:
            mock_available.return_value = False
            
            chat_data = {
                "message": "Test message",
                "user_id": "test_user"
            }
            response = client.post("/api/chat", json=chat_data)
            assert response.status_code == 503
            assert "unavailable" in response.json()["detail"].lower()
    
    def test_validates_alert_existence(self, client):
        """Test that endpoints validate alert existence"""
        # Test with non-existent alert
        response = client.get("/api/alerts/99999/comments")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
