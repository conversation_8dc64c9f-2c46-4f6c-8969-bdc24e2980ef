"""
Alert Correlation Service
========================

This service provides intelligent alert correlation capabilities including:
- Pattern detection across multiple alerts
- Timeline analysis
- Asset-based correlation
- Attack chain reconstruction
- Campaign identification
"""

import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from database import DBAlert
from models import Alert, AlertSeverity


class CorrelationType(Enum):
    SAME_SOURCE_IP = "same_source_ip"
    SAME_USER = "same_user"
    SAME_ASSET = "same_asset"
    TEMPORAL_PROXIMITY = "temporal_proximity"
    ATTACK_CHAIN = "attack_chain"
    CAMPAIGN = "campaign"
    SIMILAR_INDICATORS = "similar_indicators"


@dataclass
class CorrelationResult:
    correlation_type: CorrelationType
    confidence: float
    related_alerts: List[int]
    description: str
    timeline: List[Dict[str, Any]]
    indicators: List[str]
    risk_score: float


@dataclass
class AttackChain:
    chain_id: str
    alerts: List[int]
    start_time: datetime
    end_time: datetime
    attack_stages: List[str]
    confidence: float
    description: str


class AlertCorrelationService:
    """Service for correlating alerts and identifying attack patterns"""
    
    def __init__(self):
        self.mitre_attack_chains = {
            "credential_access_chain": [
                "T1078",      # Valid Accounts
                "T1110",      # Brute Force
                "T1003.001",  # LSASS Memory
                "T1134"       # Access Token Manipulation
            ],
            "lateral_movement_chain": [
                "T1078",      # Valid Accounts
                "T1021.002",  # SMB/Windows Admin Shares
                "T1055",      # Process Injection
                "T1547.001"   # Registry Run Keys
            ],
            "data_exfiltration_chain": [
                "T1083",      # File and Directory Discovery
                "T1213",      # Data from Information Repositories
                "T1041",      # Exfiltration Over C2 Channel
                "T1567.002"   # Exfiltration to Cloud Storage
            ]
        }

    def correlate_alerts(self, db: Session, alert_id: int, time_window_hours: int = 24) -> List[CorrelationResult]:
        """Find correlated alerts for a given alert"""
        correlations = []
        
        # Get the target alert
        target_alert = db.query(DBAlert).filter(DBAlert.id == alert_id).first()
        if not target_alert:
            return correlations
        
        # Define time window
        start_time = target_alert.created_at - timedelta(hours=time_window_hours)
        end_time = target_alert.created_at + timedelta(hours=time_window_hours)
        
        # Get alerts in time window
        related_alerts = db.query(DBAlert).filter(
            DBAlert.id != alert_id,
            DBAlert.created_at >= start_time,
            DBAlert.created_at <= end_time
        ).all()
        
        # Perform different types of correlation
        correlations.extend(self._correlate_by_source_ip(target_alert, related_alerts))
        correlations.extend(self._correlate_by_user(target_alert, related_alerts))
        correlations.extend(self._correlate_by_asset(target_alert, related_alerts))
        correlations.extend(self._correlate_by_temporal_proximity(target_alert, related_alerts))
        correlations.extend(self._correlate_by_attack_chain(target_alert, related_alerts))
        correlations.extend(self._correlate_by_campaign(target_alert, related_alerts))
        
        # Sort by confidence score
        correlations.sort(key=lambda x: x.confidence, reverse=True)
        
        return correlations[:10]  # Return top 10 correlations

    def detect_attack_chains(self, db: Session, time_window_hours: int = 72) -> List[AttackChain]:
        """Detect potential attack chains across alerts"""
        chains = []
        
        # Get recent alerts
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        alerts = db.query(DBAlert).filter(DBAlert.created_at >= cutoff_time).all()
        
        # Group alerts by potential attack vectors
        ip_groups = self._group_alerts_by_ip(alerts)
        user_groups = self._group_alerts_by_user(alerts)
        asset_groups = self._group_alerts_by_asset(alerts)
        
        # Analyze each group for attack chain patterns
        for group_type, groups in [("ip", ip_groups), ("user", user_groups), ("asset", asset_groups)]:
            for group_key, group_alerts in groups.items():
                if len(group_alerts) >= 2:  # Need at least 2 alerts for a chain
                    chain = self._analyze_attack_chain(group_alerts, group_type, group_key)
                    if chain:
                        chains.append(chain)
        
        return sorted(chains, key=lambda x: x.confidence, reverse=True)

    def calculate_campaign_score(self, alerts: List[DBAlert]) -> float:
        """Calculate likelihood that alerts are part of coordinated campaign"""
        if len(alerts) < 2:
            return 0.0
        
        score = 0.0
        factors = []
        
        # Time clustering
        time_span = max(a.created_at for a in alerts) - min(a.created_at for a in alerts)
        if time_span <= timedelta(hours=24):
            score += 0.3
            factors.append("Alerts clustered within 24 hours")
        elif time_span <= timedelta(days=7):
            score += 0.2
            factors.append("Alerts clustered within 1 week")
        
        # Source diversity
        sources = set(a.source for a in alerts)
        if len(sources) > 1:
            score += 0.2 * min(len(sources) / 5, 1.0)
            factors.append(f"Multiple data sources involved ({len(sources)})")
        
        # Severity escalation
        severities = [a.severity for a in alerts]
        if self._has_severity_escalation(severities):
            score += 0.2
            factors.append("Severity escalation pattern detected")
        
        # Geographic/IP correlation
        ips = self._extract_ips_from_alerts(alerts)
        if len(ips) > 1 and self._are_ips_related(ips):
            score += 0.3
            factors.append("Related IP addresses detected")
        
        return min(score, 1.0)

    def _correlate_by_source_ip(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by source IP address"""
        correlations = []
        target_ips = self._extract_ips_from_alert(target_alert)
        
        for ip in target_ips:
            matching_alerts = []
            for alert in related_alerts:
                if ip in self._extract_ips_from_alert(alert):
                    matching_alerts.append(alert.id)
            
            if matching_alerts:
                correlations.append(CorrelationResult(
                    correlation_type=CorrelationType.SAME_SOURCE_IP,
                    confidence=0.8,
                    related_alerts=matching_alerts,
                    description=f"Alerts share source IP address: {ip}",
                    timeline=self._build_timeline([target_alert] + [a for a in related_alerts if a.id in matching_alerts]),
                    indicators=[ip],
                    risk_score=self._calculate_ip_risk_score(ip, len(matching_alerts))
                ))
        
        return correlations

    def _correlate_by_user(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by user account"""
        correlations = []
        target_users = self._extract_users_from_alert(target_alert)
        
        for user in target_users:
            matching_alerts = []
            for alert in related_alerts:
                if user in self._extract_users_from_alert(alert):
                    matching_alerts.append(alert.id)
            
            if matching_alerts:
                correlations.append(CorrelationResult(
                    correlation_type=CorrelationType.SAME_USER,
                    confidence=0.9,
                    related_alerts=matching_alerts,
                    description=f"Alerts involve same user account: {user}",
                    timeline=self._build_timeline([target_alert] + [a for a in related_alerts if a.id in matching_alerts]),
                    indicators=[user],
                    risk_score=self._calculate_user_risk_score(user, len(matching_alerts))
                ))
        
        return correlations

    def _correlate_by_asset(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by affected asset/host"""
        correlations = []
        target_assets = self._extract_assets_from_alert(target_alert)
        
        for asset in target_assets:
            matching_alerts = []
            for alert in related_alerts:
                if asset in self._extract_assets_from_alert(alert):
                    matching_alerts.append(alert.id)
            
            if matching_alerts:
                correlations.append(CorrelationResult(
                    correlation_type=CorrelationType.SAME_ASSET,
                    confidence=0.85,
                    related_alerts=matching_alerts,
                    description=f"Alerts affect same asset: {asset}",
                    timeline=self._build_timeline([target_alert] + [a for a in related_alerts if a.id in matching_alerts]),
                    indicators=[asset],
                    risk_score=self._calculate_asset_risk_score(asset, len(matching_alerts))
                ))
        
        return correlations

    def _correlate_by_temporal_proximity(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by temporal proximity"""
        correlations = []
        
        # Group alerts by time proximity (within 1 hour)
        close_alerts = []
        for alert in related_alerts:
            time_diff = abs((alert.created_at - target_alert.created_at).total_seconds())
            if time_diff <= 3600:  # 1 hour
                close_alerts.append(alert.id)
        
        if close_alerts:
            correlations.append(CorrelationResult(
                correlation_type=CorrelationType.TEMPORAL_PROXIMITY,
                confidence=0.6,
                related_alerts=close_alerts,
                description=f"Alerts occurred within 1 hour of each other",
                timeline=self._build_timeline([target_alert] + [a for a in related_alerts if a.id in close_alerts]),
                indicators=[],
                risk_score=min(len(close_alerts) * 0.1, 1.0)
            ))
        
        return correlations

    def _correlate_by_attack_chain(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by MITRE ATT&CK chain patterns"""
        correlations = []
        
        # Extract MITRE techniques from alerts
        target_techniques = self._extract_mitre_techniques(target_alert)
        
        for chain_name, chain_techniques in self.mitre_attack_chains.items():
            matching_alerts = []
            chain_progress = []
            
            for alert in related_alerts:
                alert_techniques = self._extract_mitre_techniques(alert)
                for technique in alert_techniques:
                    if technique in chain_techniques:
                        matching_alerts.append(alert.id)
                        chain_progress.append(technique)
            
            if len(set(chain_progress)) >= 2:  # At least 2 different techniques in chain
                correlations.append(CorrelationResult(
                    correlation_type=CorrelationType.ATTACK_CHAIN,
                    confidence=0.75,
                    related_alerts=matching_alerts,
                    description=f"Alerts match {chain_name.replace('_', ' ')} attack pattern",
                    timeline=self._build_timeline([target_alert] + [a for a in related_alerts if a.id in matching_alerts]),
                    indicators=list(set(chain_progress)),
                    risk_score=len(set(chain_progress)) * 0.25
                ))
        
        return correlations

    def _correlate_by_campaign(self, target_alert: DBAlert, related_alerts: List[DBAlert]) -> List[CorrelationResult]:
        """Correlate alerts by campaign indicators"""
        correlations = []
        
        # Calculate campaign score for all alerts together
        all_alerts = [target_alert] + related_alerts
        campaign_score = self.calculate_campaign_score(all_alerts)
        
        if campaign_score > 0.5:
            correlations.append(CorrelationResult(
                correlation_type=CorrelationType.CAMPAIGN,
                confidence=campaign_score,
                related_alerts=[a.id for a in related_alerts],
                description="Alerts may be part of coordinated campaign",
                timeline=self._build_timeline(all_alerts),
                indicators=[],
                risk_score=campaign_score
            ))
        
        return correlations

    def _extract_ips_from_alert(self, alert: DBAlert) -> List[str]:
        """Extract IP addresses from alert description"""
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        text = f"{alert.title} {alert.description}"
        return re.findall(ip_pattern, text)

    def _extract_users_from_alert(self, alert: DBAlert) -> List[str]:
        """Extract user accounts from alert description"""
        user_patterns = [
            r'user[:\s]+([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+)',  # email
            r'user[:\s]+([a-zA-Z0-9._-]+)',  # username
            r'DOMAIN\\\\([a-zA-Z0-9._-]+)',  # domain\user
        ]
        text = f"{alert.title} {alert.description}"
        users = []
        for pattern in user_patterns:
            users.extend(re.findall(pattern, text, re.IGNORECASE))
        return list(set(users))

    def _extract_assets_from_alert(self, alert: DBAlert) -> List[str]:
        """Extract asset/host names from alert description"""
        asset_patterns = [
            r'host[:\s]+([a-zA-Z0-9.-]+)',
            r'server[:\s]+([a-zA-Z0-9.-]+)',
            r'workstation[:\s]+([a-zA-Z0-9.-]+)',
            r'([A-Z]+-[A-Z0-9-]+)',  # WORKSTATION-01, SERVER-05
        ]
        text = f"{alert.title} {alert.description}"
        assets = []
        for pattern in asset_patterns:
            assets.extend(re.findall(pattern, text, re.IGNORECASE))
        return list(set(assets))

    def _extract_mitre_techniques(self, alert: DBAlert) -> List[str]:
        """Extract MITRE ATT&CK techniques from alert"""
        technique_pattern = r'T\d{4}(?:\.\d{3})?'
        text = f"{alert.title} {alert.description}"
        return re.findall(technique_pattern, text)

    def _build_timeline(self, alerts: List[DBAlert]) -> List[Dict[str, Any]]:
        """Build timeline from alerts"""
        timeline = []
        for alert in sorted(alerts, key=lambda x: x.created_at):
            timeline.append({
                "timestamp": alert.created_at.isoformat(),
                "alert_id": alert.id,
                "title": alert.title,
                "severity": alert.severity
            })
        return timeline

    def _calculate_ip_risk_score(self, ip: str, alert_count: int) -> float:
        """Calculate risk score for IP address"""
        base_score = min(alert_count * 0.2, 1.0)
        # Add bonus for suspicious IP ranges
        if any(ip.startswith(prefix) for prefix in ["185.220.", "203.0.113."]):
            base_score += 0.3
        return min(base_score, 1.0)

    def _calculate_user_risk_score(self, user: str, alert_count: int) -> float:
        """Calculate risk score for user account"""
        base_score = min(alert_count * 0.25, 1.0)
        # Add bonus for privileged accounts
        if any(keyword in user.lower() for keyword in ["admin", "root", "service"]):
            base_score += 0.2
        return min(base_score, 1.0)

    def _calculate_asset_risk_score(self, asset: str, alert_count: int) -> float:
        """Calculate risk score for asset"""
        base_score = min(alert_count * 0.2, 1.0)
        # Add bonus for critical assets
        if any(keyword in asset.lower() for keyword in ["server", "db", "prod"]):
            base_score += 0.3
        return min(base_score, 1.0)

    def _group_alerts_by_ip(self, alerts: List[DBAlert]) -> Dict[str, List[DBAlert]]:
        """Group alerts by IP address"""
        groups = {}
        for alert in alerts:
            ips = self._extract_ips_from_alert(alert)
            for ip in ips:
                if ip not in groups:
                    groups[ip] = []
                groups[ip].append(alert)
        return groups

    def _group_alerts_by_user(self, alerts: List[DBAlert]) -> Dict[str, List[DBAlert]]:
        """Group alerts by user"""
        groups = {}
        for alert in alerts:
            users = self._extract_users_from_alert(alert)
            for user in users:
                if user not in groups:
                    groups[user] = []
                groups[user].append(alert)
        return groups

    def _group_alerts_by_asset(self, alerts: List[DBAlert]) -> Dict[str, List[DBAlert]]:
        """Group alerts by asset"""
        groups = {}
        for alert in alerts:
            assets = self._extract_assets_from_alert(alert)
            for asset in assets:
                if asset not in groups:
                    groups[asset] = []
                groups[asset].append(alert)
        return groups

    def _analyze_attack_chain(self, alerts: List[DBAlert], group_type: str, group_key: str) -> Optional[AttackChain]:
        """Analyze alerts for attack chain patterns"""
        if len(alerts) < 2:
            return None
        
        # Sort alerts by time
        sorted_alerts = sorted(alerts, key=lambda x: x.created_at)
        
        # Extract MITRE techniques
        techniques = []
        for alert in sorted_alerts:
            techniques.extend(self._extract_mitre_techniques(alert))
        
        # Check if techniques match known attack chains
        for chain_name, chain_techniques in self.mitre_attack_chains.items():
            matching_techniques = [t for t in techniques if t in chain_techniques]
            if len(set(matching_techniques)) >= 2:
                return AttackChain(
                    chain_id=f"{chain_name}_{group_key}_{int(datetime.now().timestamp())}",
                    alerts=[a.id for a in sorted_alerts],
                    start_time=sorted_alerts[0].created_at,
                    end_time=sorted_alerts[-1].created_at,
                    attack_stages=list(set(matching_techniques)),
                    confidence=len(set(matching_techniques)) / len(chain_techniques),
                    description=f"{chain_name.replace('_', ' ').title()} detected across {group_type}: {group_key}"
                )
        
        return None

    def _has_severity_escalation(self, severities: List[str]) -> bool:
        """Check if there's a severity escalation pattern"""
        severity_order = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        severity_values = [severity_order.get(s, 0) for s in severities]
        
        # Check for increasing trend
        for i in range(1, len(severity_values)):
            if severity_values[i] > severity_values[i-1]:
                return True
        return False

    def _extract_ips_from_alerts(self, alerts: List[DBAlert]) -> List[str]:
        """Extract all IPs from multiple alerts"""
        all_ips = []
        for alert in alerts:
            all_ips.extend(self._extract_ips_from_alert(alert))
        return list(set(all_ips))

    def _are_ips_related(self, ips: List[str]) -> bool:
        """Check if IPs are related (same subnet, etc.)"""
        # Simple check for same /24 subnet
        subnets = set()
        for ip in ips:
            parts = ip.split('.')
            if len(parts) == 4:
                subnet = '.'.join(parts[:3])
                subnets.add(subnet)
        
        return len(subnets) < len(ips)  # Some IPs share subnets


# Global correlation service instance
correlation_service = AlertCorrelationService()
