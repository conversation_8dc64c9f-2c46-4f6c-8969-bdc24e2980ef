from fastapi import FastAPI, HTTPException, UploadFile, File, Depends, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.security import HTT<PERSON><PERSON>earer
from contextlib import asynccontextmanager
from datetime import datetime
from dotenv import load_dotenv
import os
import uuid
import time
import re
from pathlib import Path
from typing import List, Optional

# Load environment variables from .env file
load_dotenv()

from models import (
    Alert,
    AlertResponse,
    Comment,
    CommentCreate,
    AIFinding,
    UploadedFile,
    ChatRequest,
    ChatResponse,
    ChatSession,
    ChatSessionWithMessages,
    ChatSessionCreateRequest,
    ChatSessionListResponse,
    EditMessageRequest,
    Analyst,
    CommentBeautifyRequest,
    CommentBeautifyResponse,
    AlertCreateRequest,
    AlertCreateResponse,
    AlertUpdateRequest,
    AlertUpdateResponse,
    AlertDeleteResponse,
    AlertChangeLogResponse,
    FileResource,
    SystemLog,
    SystemLogFilter,
    SystemLogResponse,
    SystemLogStats,
    WebhookSource,
    WebhookSourceCreate,
    WebhookSourceUpdate,
    WebhookSourceResponse,
    WebhookSourceListResponse,
    WebhookPayload,
    WebhookTestRequest,
    WebhookTestResponse,
)
from ollama_service import ollama_service
from app.tools.manager import get_tool_manager
from database import get_db, init_db
from db_service import DatabaseService
from sqlalchemy.orm import Session
from panther_seed_data import create_panther_seed_data
from threat_intel_service import threat_intel_service, ThreatIntelligence
from correlation_service import correlation_service, CorrelationResult, AttackChain
from logging_service import logging_service

# Security configuration
security = HTTPBearer(auto_error=False)

# Configuration constants
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8001")
FRONTEND_URLS = os.getenv("FRONTEND_URLS", "http://localhost:3000,http://localhost:3001").split(",")
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB default
ALLOWED_FILE_TYPES = os.getenv("ALLOWED_FILE_TYPES", "image/jpeg,image/png,image/gif,text/plain,application/pdf").split(",")

# Validation patterns
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
IP_PATTERN = re.compile(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$')
DOMAIN_PATTERN = re.compile(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
HASH_PATTERN = re.compile(r'^[a-fA-F0-9]{32,64}$')

# Security and validation functions
def validate_file_upload(file: UploadFile) -> None:
    """Validate uploaded file for security"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="Filename is required")

    # Check file size
    if file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413,
            detail=f"File size exceeds maximum allowed size of {MAX_FILE_SIZE} bytes"
        )

    # Check file type
    if file.content_type and file.content_type not in ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=415,
            detail=f"File type {file.content_type} not allowed"
        )

    # Check filename for path traversal
    if ".." in file.filename or "/" in file.filename or "\\" in file.filename:
        raise HTTPException(status_code=400, detail="Invalid filename")

def sanitize_input(text: str, max_length: int = 1000) -> str:
    """Sanitize user input"""
    if not text:
        return ""

    # Remove null bytes and control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    return sanitized.strip()

def validate_pagination(limit: int = 100, offset: int = 0) -> tuple[int, int]:
    """Validate and sanitize pagination parameters"""
    limit = max(1, min(limit, 1000))  # Between 1 and 1000
    offset = max(0, offset)  # Non-negative
    return limit, offset

def get_client_ip(request: Request) -> str:
    """Extract client IP address safely"""
    # Check for forwarded headers (be careful with these in production)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()

    # Fallback to client host
    return getattr(request.client, "host", "unknown") if request.client else "unknown"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize database and seed data on startup"""
    print("🚀 Starting AlertAI API...")
    print("📊 Initializing database...")

    # Initialize database tables
    init_db()

    # Seed database with Panther Labs real alert data
    try:
        db = next(get_db())
        try:
            create_panther_seed_data(db)
            print("✅ Database initialization completed")
        finally:
            db.close()
    except Exception as e:
        print(f"⚠️  Database seeding info: {e}")
        # Don't fail startup if seeding fails (data might already exist)

    yield

    # Cleanup (if needed)
    print("🛑 Shutting down AlertAI API...")


app = FastAPI(title="AlertAI API", version="1.0.0", lifespan=lifespan)

# Configure CORS with environment-based origins
allowed_origins = FRONTEND_URLS + [
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
    ],
    expose_headers=["X-Total-Count"],
)

# Mount static files for uploads
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Database-backed API endpoints


@app.get("/")
async def root():
    return {"message": "AlertAI API is running"}


@app.get("/api/alerts", response_model=AlertResponse)
async def get_alerts(
    request: Request,
    limit: int = 100,
    offset: int = 0,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """Get alerts with pagination and filtering"""
    try:
        # Validate pagination parameters
        limit, offset = validate_pagination(limit, offset)

        # Get alerts with pagination
        db_alerts = DatabaseService.get_alerts(db, include_deleted=include_deleted, limit=limit, offset=offset)
        alerts = [DatabaseService.db_alert_to_pydantic(db_alert) for db_alert in db_alerts]

        # Get total count for pagination
        total_count = DatabaseService.get_alerts_count(db, include_deleted=include_deleted)

        # Add pagination headers
        response = AlertResponse(alerts=alerts, total=total_count)

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching alerts: {str(e)}")


@app.get("/api/alerts/{alert_id}", response_model=Alert)
async def get_alert(alert_id: int, db: Session = Depends(get_db)):
    """Get a specific alert by ID"""
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")
    return DatabaseService.db_alert_to_pydantic(db_alert)





@app.get("/api/analysts", response_model=List[Analyst])
async def get_analysts(db: Session = Depends(get_db)):
    """Get all available analysts for tagging"""
    db_analysts = DatabaseService.get_analysts(db)
    return [DatabaseService.db_analyst_to_pydantic(db_analyst) for db_analyst in db_analysts]


@app.get("/api/alerts/{alert_id}/comments", response_model=List[Comment])
async def get_alert_comments(alert_id: int, db: Session = Depends(get_db)):
    """Get comments for a specific alert"""
    # Verify alert exists
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    db_comments = DatabaseService.get_comments_by_alert_id(db, alert_id)
    return [DatabaseService.db_comment_to_pydantic(db_comment) for db_comment in db_comments]


@app.post("/api/alerts/{alert_id}/comments", response_model=Comment)
async def create_comment(
    alert_id: int,
    comment_data: CommentCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Create a new comment for an alert with validation"""
    start_time = time.time()

    try:
        # Verify alert exists
        db_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not db_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Validate and sanitize input
        author = sanitize_input(comment_data.author, 100)
        content = sanitize_input(comment_data.content, 10000)

        if not author.strip():
            raise HTTPException(status_code=400, detail="Author is required")

        if not content.strip():
            raise HTTPException(status_code=400, detail="Comment content is required")

        if len(content) < 3:
            raise HTTPException(status_code=400, detail="Comment must be at least 3 characters long")

        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "Unknown")

        # Create the comment in the database with change logging
        db_comment = DatabaseService.create_comment(
            db=db,
            alert_id=alert_id,
            author=author,
            content=content,
            changed_by=author,
            ip_address=client_ip,
            user_agent=user_agent
        )

        # Log the event
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_comment_event(
            db=db,
            action="create",
            comment_id=db_comment.id,
            alert_id=alert_id,
            user_id=author.lower().replace(' ', '_'),
            username=author,
            details={
                "content_length": len(content),
                "alert_title": db_alert.title
            },
            response_time_ms=response_time_ms
        )

        return DatabaseService.db_comment_to_pydantic(db_comment)

    except HTTPException:
        raise
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_comment_event(
            db=db,
            action="create",
            comment_id=0,
            alert_id=alert_id,
            user_id=getattr(comment_data, 'author', 'unknown').lower().replace(' ', '_'),
            username=getattr(comment_data, 'author', 'unknown'),
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error creating comment: {str(e)}")


@app.get("/api/alerts/{alert_id}/ai-findings", response_model=List[AIFinding])
async def get_ai_findings(alert_id: int, db: Session = Depends(get_db)):
    """Get AI findings for a specific alert"""
    # Verify alert exists
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    db_findings = DatabaseService.get_ai_findings_by_alert_id(db, alert_id)
    return [DatabaseService.db_ai_finding_to_pydantic(db_finding) for db_finding in db_findings]


@app.post("/api/alerts/{alert_id}/upload")
async def upload_file(
    alert_id: int,
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a file for an alert with security validation"""
    try:
        # Verify alert exists
        db_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not db_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Validate file upload
        validate_file_upload(file)

        # Create uploads directory if it doesn't exist
        upload_dir = Path(f"uploads/{alert_id}")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate secure filename
        original_filename = sanitize_input(file.filename or "unknown", 255)
        file_extension = Path(original_filename).suffix.lower()
        file_stem = Path(original_filename).stem
        unique_id = str(uuid.uuid4())[:8]
        unique_filename = f"{file_stem}_{unique_id}{file_extension}"

        # Read and validate file content
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File size {len(content)} exceeds maximum allowed size of {MAX_FILE_SIZE} bytes"
            )

        # Save the file securely
        file_path = upload_dir / unique_filename
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # Determine if it's an image
        is_image = file.content_type and file.content_type.startswith("image/")

        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "Unknown")

        # Create file resource in database with change logging
        db_resource = DatabaseService.create_file_resource(
            db=db,
            alert_id=alert_id,
            filename=original_filename,
            file_path=str(file_path),
            file_type=file.content_type or "unknown",
            file_size=len(content),
            is_image=is_image,
            uploaded_by="Current User",  # TODO: Replace with actual user when auth is implemented
            changed_by="Current User",
            ip_address=client_ip,
            user_agent=user_agent
        )

        # Return secure URL
        url_path = f"{API_BASE_URL}/uploads/{alert_id}/{unique_filename}"

        uploaded_file = UploadedFile(
            id=db_resource.id,
            alert_id=alert_id,
            filename=original_filename,
            file_path=url_path,
            file_type=file.content_type or "unknown",
            file_size=len(content),
            uploaded_by="Current User",
            uploaded_at=db_resource.uploaded_at,
        )

        return uploaded_file

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")


async def process_ai_chat_background(
    alert_id: int,
    chat_request: ChatRequest,
    db: Session
) -> ChatResponse:
    """Background task for processing AI chat requests with session management"""
    start_time = time.time()

    try:
        # Get alert and related data
        db_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not db_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        alert = DatabaseService.db_alert_to_pydantic(db_alert)

        # Handle session management
        session_id = chat_request.session_id
        chat_history = []

        if session_id:
            # Get existing session
            db_session = DatabaseService.get_chat_session_by_id(db, session_id)
            if not db_session:
                raise HTTPException(status_code=404, detail="Chat session not found")

            # Get recent chat history for context
            recent_messages = DatabaseService.get_recent_chat_messages(db, session_id, limit=5)
            chat_history = [(msg.user_message, msg.ai_response) for msg in reversed(recent_messages)]

        else:
            # Create new session if not incognito
            if not chat_request.is_incognito:
                db_session = DatabaseService.create_chat_session(
                    db=db,
                    user_id=chat_request.user_id or "current_user",
                    alert_id=alert_id,
                    is_incognito=False
                )
                session_id = db_session.id

        # Get additional context
        db_comments = DatabaseService.get_comments_by_alert_id(db, alert_id)
        comments = [DatabaseService.db_comment_to_pydantic(db_comment) for db_comment in db_comments]

        db_findings = DatabaseService.get_ai_findings_by_alert_id(db, alert_id)
        ai_findings = [DatabaseService.db_ai_finding_to_pydantic(db_finding) for db_finding in db_findings]

        # Get uploaded files for the alert (for tool analysis)
        db_file_resources = DatabaseService.get_file_resources_by_alert_id(db, alert_id)
        uploaded_files = [DatabaseService.db_file_resource_to_pydantic(db_file) for db_file in db_file_resources]

        # Get AI response with chat history and uploaded files
        ai_response = await ollama_service.chat_with_ai(
            user_message=chat_request.message,
            alert=alert,
            comments=comments,
            ai_findings=ai_findings,
            uploaded_files=uploaded_files,
            chat_history=chat_history
        )

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Save message to database if not incognito
        message_id = None
        if session_id and not chat_request.is_incognito:
            context_data = {
                "alert_id": alert_id,
                "page_context": chat_request.page_context or {}
            }

            db_message = DatabaseService.create_chat_message(
                db=db,
                session_id=session_id,
                user_message=chat_request.message,
                ai_response=ai_response,
                response_time_ms=response_time_ms,
                context_data=context_data
            )
            message_id = db_message.id

        return ChatResponse(
            message=chat_request.message,
            response=ai_response,
            session_id=session_id or 0,
            message_id=message_id or 0,
            created_at=datetime.now(),
            is_new_session=session_id is not None and chat_request.session_id is None
        )
    except Exception as e:
        print(f"Background AI chat error: {e}")
        raise

@app.post("/api/chat", response_model=ChatResponse)
async def general_chat_with_ai(
    chat_request: ChatRequest,
    db: Session = Depends(get_db)
):
    """General chat with Bitzy AI assistant when no specific alert is selected"""
    start_time = time.time()

    # Check if Ollama service is available
    if not ollama_service.is_service_available():
        # Log the service unavailability
        logging_service.log_bitzy_interaction(
            db=db,
            action="chat_attempt",
            user_id=chat_request.user_id,
            username=chat_request.user_id,
            session_id=chat_request.session_id,
            details={
                "error": "AI service unavailable",
                "page_context": getattr(chat_request, 'page_context', None),
                "is_incognito": getattr(chat_request, 'is_incognito', False)
            },
            success=False,
            error_message="AI service is currently unavailable"
        )
        raise HTTPException(
            status_code=503,
            detail=f"AI service is currently unavailable. Please ensure Ollama is running with the {ollama_service.model_name} model.",
        )

    try:
        # Handle session management
        session_id = chat_request.session_id
        chat_history = []

        if session_id:
            # Get existing session
            db_session = DatabaseService.get_chat_session_by_id(db, session_id)
            if not db_session:
                raise HTTPException(status_code=404, detail="Chat session not found")

            # Get recent chat history for context
            recent_messages = DatabaseService.get_recent_chat_messages(db, session_id, limit=5)
            chat_history = [(msg.user_message, msg.ai_response) for msg in reversed(recent_messages)]

        else:
            # Create new session if not incognito
            if not chat_request.is_incognito:
                db_session = DatabaseService.create_chat_session(
                    db=db,
                    user_id=chat_request.user_id or "current_user",
                    alert_id=None,  # General chat
                    is_incognito=False
                )
                session_id = db_session.id

        # Extract page context from the request if available
        page_context = getattr(chat_request, 'page_context', None)

        # Get AI response for general conversation with chat history
        ai_response = await ollama_service.general_chat(
            user_message=chat_request.message,
            page_context=page_context,
            chat_history=chat_history
        )

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Save message to database if not incognito
        message_id = None
        if session_id and not chat_request.is_incognito:
            context_data = {
                "page_context": chat_request.page_context or {}
            }

            db_message = DatabaseService.create_chat_message(
                db=db,
                session_id=session_id,
                user_message=chat_request.message,
                ai_response=ai_response,
                response_time_ms=response_time_ms,
                context_data=context_data
            )
            message_id = db_message.id

        # Log the successful Bitzy interaction
        logging_service.log_bitzy_interaction(
            db=db,
            action="general_chat",
            user_id=chat_request.user_id,
            username=chat_request.user_id,
            session_id=chat_request.session_id,
            details={
                "message_length": len(chat_request.message),
                "response_length": len(ai_response),
                "page_context": getattr(chat_request, 'page_context', None),
                "is_incognito": getattr(chat_request, 'is_incognito', False),
                "is_new_session": session_id is not None and chat_request.session_id is None,
                "session_id": session_id
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return ChatResponse(
            message=chat_request.message,
            response=ai_response,
            session_id=session_id or 0,
            message_id=message_id or 0,
            created_at=datetime.now(),
            is_new_session=session_id is not None and chat_request.session_id is None
        )
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_bitzy_interaction(
            db=db,
            action="general_chat",
            user_id=getattr(chat_request, 'user_id', 'unknown'),
            username=getattr(chat_request, 'user_id', 'unknown'),
            session_id=getattr(chat_request, 'session_id', None),
            details={
                "page_context": getattr(chat_request, 'page_context', None),
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error processing chat request: {str(e)}")


@app.post("/api/alerts/{alert_id}/chat", response_model=ChatResponse)
async def chat_with_ai(
    alert_id: int,
    chat_request: ChatRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Chat with AI about a specific alert with background processing"""
    start_time = time.time()

    # Verify alert exists
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    # Check if Ollama service is available
    if not ollama_service.is_service_available():
        # Log the service unavailability
        logging_service.log_bitzy_interaction(
            db=db,
            action="alert_chat_attempt",
            user_id=chat_request.user_id,
            username=chat_request.user_id,
            session_id=chat_request.session_id,
            details={
                "alert_id": alert_id,
                "alert_title": db_alert.title,
                "error": "AI service unavailable"
            },
            success=False,
            error_message="AI service is currently unavailable"
        )
        raise HTTPException(
            status_code=503,
            detail=f"AI service is currently unavailable. Please ensure Ollama is running with the {ollama_service.model_name} model.",
        )

    try:
        # Process AI chat in the main thread for now (can be moved to background if needed)
        response = await process_ai_chat_background(alert_id, chat_request, db)

        # Check if tools were used by looking for tool indicators in the response
        tools_used = []
        if hasattr(response, 'response') and response.response:
            if "🔧 **AI Tools Used:**" in response.response:
                tools_used.append("VirusTotal")
            if "🔍 **VirusTotal Analysis Results:**" in response.response:
                tools_used.append("VirusTotal")

        # Log tool usage to system logs if tools were used
        if tools_used:
            logging_service.log_event(
                db=db,
                event_type="tool_usage",
                action="virustotal_analysis",
                user_id=chat_request.user_id,
                username=chat_request.user_id,
                details={
                    "alert_id": alert_id,
                    "alert_title": db_alert.title,
                    "tools_used": tools_used,
                    "user_message": chat_request.message[:100] + "..." if len(chat_request.message) > 100 else chat_request.message,
                    "session_id": chat_request.session_id
                },
                success=True
            )

        # Log the successful alert chat
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_bitzy_interaction(
            db=db,
            action="alert_chat",
            user_id=chat_request.user_id,
            username=chat_request.user_id,
            session_id=chat_request.session_id,
            details={
                "alert_id": alert_id,
                "alert_title": db_alert.title,
                "message_length": len(chat_request.message),
                "response_length": len(response.response) if hasattr(response, 'response') else 0,
                "is_new_session": getattr(response, 'is_new_session', False),
                "tools_used": tools_used
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return response
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_bitzy_interaction(
            db=db,
            action="alert_chat",
            user_id=chat_request.user_id,
            username=chat_request.user_id,
            session_id=chat_request.session_id,
            details={
                "alert_id": alert_id,
                "alert_title": db_alert.title,
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error processing chat request: {str(e)}")


@app.get("/api/alerts/{alert_id}/resources", response_model=List[FileResource])
async def get_alert_resources(alert_id: int, db: Session = Depends(get_db)):
    """Get file resources for a specific alert"""
    # Verify alert exists
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    db_resources = DatabaseService.get_file_resources_by_alert_id(db, alert_id)
    return [DatabaseService.db_file_resource_to_pydantic(db_resource) for db_resource in db_resources]


@app.post("/api/comments/beautify", response_model=CommentBeautifyResponse)
async def beautify_comment(request: CommentBeautifyRequest):
    """Use AI to beautify and improve comment text"""

    # Check if Ollama service is available
    if not ollama_service.is_service_available():
        raise HTTPException(
            status_code=503,
            detail=f"AI service is currently unavailable. Please ensure Ollama is running with the {ollama_service.model_name} model.",
        )

    try:
        # Create a beautification prompt
        beautify_prompt = f"""
You are a professional communication assistant. Your task is to improve the following comment to make it more clear, professional, and well-structured while maintaining all technical accuracy and meaning.

CRITICAL: Return ONLY the improved comment text. Do not include any explanations, introductions, prefixes like "Here is an improved version:", or additional text. Just return the improved comment directly.

Original comment:
{request.content}

Guidelines:
- Use clear, professional language
- Maintain all technical details and accuracy
- Improve grammar and structure
- Make the content more readable
- Keep the same tone and intent
- Format with markdown if appropriate
- Return ONLY the improved text, nothing else"""

        # Get AI response for beautification
        response = ollama_service.client.chat(
            model=ollama_service.model_name,
            messages=[{"role": "user", "content": beautify_prompt}],
            options={
                "temperature": 0.3,  # Lower temperature for more consistent results
                "top_p": 0.9,
                "max_tokens": 500,
            },
        )

        beautified_content = response["message"]["content"].strip()

        return CommentBeautifyResponse(
            original_content=request.content, beautified_content=beautified_content, created_at=datetime.now()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error beautifying comment: {str(e)}")


@app.post("/api/alerts", response_model=AlertCreateResponse)
async def create_alert(alert_request: AlertCreateRequest, db: Session = Depends(get_db)):
    """Create a new alert"""
    start_time = time.time()
    try:
        # Create new alert in database
        db_alert = DatabaseService.create_alert(
            db=db,
            title=alert_request.title,
            description=alert_request.description,
            severity=alert_request.severity.value,
            status=alert_request.status.value,
            source=alert_request.source,
            created_by="API User",  # TODO: Get from auth
            ip_address=None,  # TODO: Extract from request
            user_agent=None   # TODO: Extract from request headers
        )

        # Convert to Pydantic model
        new_alert = DatabaseService.db_alert_to_pydantic(db_alert)

        # Log the event
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_alert_event(
            db=db,
            action="create",
            alert_id=db_alert.id,
            user_id="api_user",
            username="API User",
            details={
                "title": alert_request.title,
                "severity": alert_request.severity.value,
                "source": alert_request.source
            },
            response_time_ms=response_time_ms
        )

        return AlertCreateResponse(alert=new_alert, message=f"Alert #{db_alert.id} created successfully")

    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_alert_event(
            db=db,
            action="create",
            alert_id=0,
            user_id="api_user",
            username="API User",
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error creating alert: {str(e)}")


@app.put("/api/alerts/{alert_id}", response_model=AlertUpdateResponse)
async def update_alert(alert_id: int, alert_request: AlertUpdateRequest, db: Session = Depends(get_db)):
    """Update an existing alert"""
    try:
        # Check if alert exists
        existing_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not existing_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Prepare update data
        update_data = {}
        if alert_request.title is not None:
            update_data['title'] = alert_request.title
        if alert_request.description is not None:
            update_data['description'] = alert_request.description
        if alert_request.severity is not None:
            update_data['severity'] = alert_request.severity.value
        if alert_request.status is not None:
            update_data['status'] = alert_request.status.value
        if alert_request.source is not None:
            update_data['source'] = alert_request.source
        if alert_request.assigned_analyst_id is not None:
            update_data['assigned_analyst_id'] = alert_request.assigned_analyst_id
        if alert_request.tags is not None:
            update_data['tags'] = alert_request.tags
        if alert_request.due_date is not None:
            update_data['due_date'] = alert_request.due_date

        # Update alert with change logging
        updated_alert = DatabaseService.update_alert(
            db=db,
            alert_id=alert_id,
            changed_by="API User",  # TODO: Replace with actual user when auth is implemented
            ip_address=None,  # TODO: Extract from request
            user_agent=None,  # TODO: Extract from request headers
            **update_data
        )
        if not updated_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Convert to Pydantic model
        alert_response = DatabaseService.db_alert_to_pydantic(updated_alert)

        return AlertUpdateResponse(alert=alert_response, message=f"Alert #{alert_id} updated successfully")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating alert: {str(e)}")


@app.delete("/api/alerts/{alert_id}", response_model=AlertDeleteResponse)
async def delete_alert(alert_id: int, hard_delete: bool = False, db: Session = Depends(get_db)):
    """Delete an alert (soft delete by default)"""
    try:
        # Check if alert exists
        existing_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not existing_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Delete alert with change logging
        success = DatabaseService.delete_alert(
            db=db,
            alert_id=alert_id,
            soft_delete=not hard_delete,
            changed_by="API User",  # TODO: Replace with actual user when auth is implemented
            ip_address=None,  # TODO: Extract from request
            user_agent=None   # TODO: Extract from request headers
        )
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")

        delete_type = "permanently deleted" if hard_delete else "moved to trash"
        return AlertDeleteResponse(
            message=f"Alert #{alert_id} {delete_type} successfully",
            deleted_alert_id=alert_id
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting alert: {str(e)}")


@app.get("/api/alerts/{alert_id}/change-logs", response_model=AlertChangeLogResponse)
async def get_alert_change_logs(alert_id: int, db: Session = Depends(get_db)):
    """Get change logs for a specific alert"""
    try:
        # Check if alert exists
        existing_alert = DatabaseService.get_alert_by_id(db, alert_id)
        if not existing_alert:
            raise HTTPException(status_code=404, detail="Alert not found")

        # Get change logs
        db_change_logs = DatabaseService.get_change_logs_by_alert_id(db, alert_id)

        # Convert to Pydantic models
        change_logs = [DatabaseService.db_change_log_to_pydantic(log) for log in db_change_logs]

        return AlertChangeLogResponse(
            change_logs=change_logs,
            total_count=len(change_logs)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching change logs: {str(e)}")


@app.get("/api/alerts/{alert_id}/threat-intel")
async def get_alert_threat_intel(alert_id: int, db: Session = Depends(get_db)):
    """Get threat intelligence for an alert"""
    # Get the alert
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    # Extract indicators from alert
    alert_text = f"{db_alert.title} {db_alert.description}"

    # Analyze IPs
    import re
    ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    ips = re.findall(ip_pattern, alert_text)

    # Analyze domains
    domain_pattern = r'\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
    domains = re.findall(domain_pattern, alert_text)

    # Analyze file hashes
    hash_pattern = r'\b[a-fA-F0-9]{32,64}\b'
    hashes = re.findall(hash_pattern, alert_text)

    threat_intel = {
        "alert_id": alert_id,
        "indicators": {
            "ip_addresses": [],
            "domains": [],
            "file_hashes": []
        },
        "threat_summary": {
            "total_threats": 0,
            "high_confidence_threats": 0,
            "threat_actors": set(),
            "malware_families": set(),
            "attack_techniques": set()
        }
    }

    # Analyze each IP
    for ip in set(ips):
        intel = threat_intel_service.analyze_ip(ip)
        if intel:
            threat_intel["indicators"]["ip_addresses"].append({
                "value": intel.ioc_value,
                "threat_level": intel.threat_level.value,
                "confidence": intel.confidence,
                "description": intel.description,
                "sources": intel.sources,
                "threat_actors": intel.threat_actors,
                "malware_families": intel.malware_families,
                "tags": intel.tags
            })
            threat_intel["threat_summary"]["total_threats"] += 1
            if intel.confidence > 0.8:
                threat_intel["threat_summary"]["high_confidence_threats"] += 1
            threat_intel["threat_summary"]["threat_actors"].update(intel.threat_actors)
            threat_intel["threat_summary"]["malware_families"].update(intel.malware_families)
            threat_intel["threat_summary"]["attack_techniques"].update(intel.attack_techniques)

    # Analyze each domain
    for domain in set(domains):
        intel = threat_intel_service.analyze_domain(domain)
        if intel:
            threat_intel["indicators"]["domains"].append({
                "value": intel.ioc_value,
                "threat_level": intel.threat_level.value,
                "confidence": intel.confidence,
                "description": intel.description,
                "sources": intel.sources,
                "threat_actors": intel.threat_actors,
                "malware_families": intel.malware_families,
                "tags": intel.tags
            })
            threat_intel["threat_summary"]["total_threats"] += 1
            if intel.confidence > 0.8:
                threat_intel["threat_summary"]["high_confidence_threats"] += 1
            threat_intel["threat_summary"]["threat_actors"].update(intel.threat_actors)
            threat_intel["threat_summary"]["malware_families"].update(intel.malware_families)
            threat_intel["threat_summary"]["attack_techniques"].update(intel.attack_techniques)

    # Analyze each file hash
    for file_hash in set(hashes):
        intel = threat_intel_service.analyze_file_hash(file_hash)
        if intel:
            threat_intel["indicators"]["file_hashes"].append({
                "value": intel.ioc_value,
                "threat_level": intel.threat_level.value,
                "confidence": intel.confidence,
                "description": intel.description,
                "sources": intel.sources,
                "threat_actors": intel.threat_actors,
                "malware_families": intel.malware_families,
                "tags": intel.tags
            })
            threat_intel["threat_summary"]["total_threats"] += 1
            if intel.confidence > 0.8:
                threat_intel["threat_summary"]["high_confidence_threats"] += 1
            threat_intel["threat_summary"]["threat_actors"].update(intel.threat_actors)
            threat_intel["threat_summary"]["malware_families"].update(intel.malware_families)
            threat_intel["threat_summary"]["attack_techniques"].update(intel.attack_techniques)

    # Convert sets to lists for JSON serialization
    threat_intel["threat_summary"]["threat_actors"] = list(threat_intel["threat_summary"]["threat_actors"])
    threat_intel["threat_summary"]["malware_families"] = list(threat_intel["threat_summary"]["malware_families"])
    threat_intel["threat_summary"]["attack_techniques"] = list(threat_intel["threat_summary"]["attack_techniques"])

    return threat_intel


@app.get("/api/alerts/{alert_id}/correlations")
async def get_alert_correlations(alert_id: int, time_window_hours: int = 24, db: Session = Depends(get_db)):
    """Get correlated alerts for a specific alert"""
    # Check if alert exists
    db_alert = DatabaseService.get_alert_by_id(db, alert_id)
    if not db_alert:
        raise HTTPException(status_code=404, detail="Alert not found")

    # Get correlations
    correlations = correlation_service.correlate_alerts(db, alert_id, time_window_hours)

    # Convert to response format
    correlation_data = []
    for corr in correlations:
        correlation_data.append({
            "correlation_type": corr.correlation_type.value,
            "confidence": corr.confidence,
            "related_alert_ids": corr.related_alerts,
            "description": corr.description,
            "timeline": corr.timeline,
            "indicators": corr.indicators,
            "risk_score": corr.risk_score
        })

    return {
        "alert_id": alert_id,
        "time_window_hours": time_window_hours,
        "correlations": correlation_data,
        "total_correlations": len(correlation_data)
    }


@app.get("/api/attack-chains")
async def get_attack_chains(time_window_hours: int = 72, db: Session = Depends(get_db)):
    """Get detected attack chains"""
    chains = correlation_service.detect_attack_chains(db, time_window_hours)

    # Convert to response format
    chain_data = []
    for chain in chains:
        chain_data.append({
            "chain_id": chain.chain_id,
            "alert_ids": chain.alerts,
            "start_time": chain.start_time.isoformat(),
            "end_time": chain.end_time.isoformat(),
            "attack_stages": chain.attack_stages,
            "confidence": chain.confidence,
            "description": chain.description,
            "duration_hours": (chain.end_time - chain.start_time).total_seconds() / 3600
        })

    return {
        "time_window_hours": time_window_hours,
        "attack_chains": chain_data,
        "total_chains": len(chain_data)
    }


@app.get("/api/mitre-attack/{technique_id}")
async def get_mitre_attack_info(technique_id: str):
    """Get MITRE ATT&CK technique information"""
    technique_info = threat_intel_service.get_mitre_attack_info(technique_id)

    if not technique_info:
        raise HTTPException(status_code=404, detail=f"MITRE ATT&CK technique {technique_id} not found")

    return {
        "technique_id": technique_id,
        **technique_info
    }


# ===== CHAT SESSION MANAGEMENT ENDPOINTS =====

@app.post("/api/chat-sessions", response_model=ChatSession)
async def create_chat_session(
    request: ChatSessionCreateRequest,
    db: Session = Depends(get_db)
):
    """Create a new chat session"""
    start_time = time.time()
    try:
        # Verify alert exists if alert_id is provided
        alert_title = None
        if request.alert_id:
            db_alert = DatabaseService.get_alert_by_id(db, request.alert_id)
            if not db_alert:
                raise HTTPException(status_code=404, detail="Alert not found")
            alert_title = db_alert.title

        # Create new chat session
        db_session = DatabaseService.create_chat_session(
            db=db,
            user_id=request.user_id or "current_user",
            alert_id=request.alert_id,
            title=request.title,
            is_incognito=request.is_incognito or False
        )

        # Log the session creation
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_bitzy_interaction(
            db=db,
            action="session_create",
            user_id=request.user_id or "current_user",
            username=request.user_id or "current_user",
            session_id=db_session.id,
            details={
                "session_title": request.title,
                "alert_id": request.alert_id,
                "alert_title": alert_title,
                "is_incognito": request.is_incognito or False
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return DatabaseService.db_chat_session_to_pydantic(db_session)

    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_bitzy_interaction(
            db=db,
            action="session_create",
            user_id=getattr(request, 'user_id', 'unknown') or "unknown",
            username=getattr(request, 'user_id', 'unknown') or "unknown",
            details={
                "error": str(e),
                "alert_id": getattr(request, 'alert_id', None)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error creating chat session: {str(e)}")


@app.get("/api/chat-sessions", response_model=ChatSessionListResponse)
async def get_user_chat_sessions(
    user_id: str = "current_user",
    include_incognito: bool = False,
    alert_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get all chat sessions for a user"""
    try:
        db_sessions = DatabaseService.get_user_chat_sessions(
            db=db,
            user_id=user_id,
            include_incognito=include_incognito,
            alert_id=alert_id
        )

        sessions = [DatabaseService.db_chat_session_to_pydantic(db_session) for db_session in db_sessions]

        return ChatSessionListResponse(
            sessions=sessions,
            total_count=len(sessions)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving chat sessions: {str(e)}")


@app.get("/api/chat-sessions/{session_id}", response_model=ChatSessionWithMessages)
async def get_chat_session_with_messages(
    session_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific chat session with all its messages"""
    try:
        # Get session
        db_session = DatabaseService.get_chat_session_by_id(db, session_id)
        if not db_session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # Get messages
        db_messages = DatabaseService.get_chat_messages_by_session(db, session_id)

        session = DatabaseService.db_chat_session_to_pydantic(db_session)
        messages = [DatabaseService.db_chat_message_to_pydantic(db_message) for db_message in db_messages]

        return ChatSessionWithMessages(
            session=session,
            messages=messages
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving chat session: {str(e)}")


@app.post("/api/chat-sessions/{session_id}/end")
async def end_chat_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """End a chat session"""
    try:
        success = DatabaseService.end_chat_session(db, session_id)
        if not success:
            raise HTTPException(status_code=404, detail="Chat session not found")

        return {"message": "Chat session ended successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ending chat session: {str(e)}")


@app.delete("/api/chat-sessions/{session_id}")
async def delete_chat_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """Delete a chat session and all its messages"""
    try:
        success = DatabaseService.delete_chat_session(db, session_id)
        if not success:
            raise HTTPException(status_code=404, detail="Chat session not found")

        return {"message": "Chat session deleted successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting chat session: {str(e)}")


@app.put("/api/chat-messages/{message_id}/edit", response_model=ChatResponse)
async def edit_chat_message(
    message_id: int,
    edit_request: EditMessageRequest,
    db: Session = Depends(get_db)
):
    """Edit a chat message and regenerate the conversation from that point"""
    try:
        # Get the original message
        db_message = DatabaseService.get_chat_message_by_id(db, message_id)
        if not db_message:
            raise HTTPException(status_code=404, detail="Chat message not found")

        # Get the session
        db_session = DatabaseService.get_chat_session_by_id(db, db_message.session_id)
        if not db_session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # Update the user message
        updated_message = DatabaseService.update_chat_message(db, message_id, edit_request.new_message)
        if not updated_message:
            raise HTTPException(status_code=500, detail="Failed to update message")

        # Delete all messages after this one
        success = DatabaseService.delete_messages_after_order(db, db_session.id, updated_message.message_order)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete subsequent messages")

        # Check if Ollama service is available
        if not ollama_service.is_service_available():
            raise HTTPException(
                status_code=503,
                detail=f"AI service is currently unavailable. Please ensure Ollama is running with the {ollama_service.model_name} model.",
            )

        # Get chat history up to this point (excluding the current message)
        recent_messages = DatabaseService.get_recent_chat_messages(db, db_session.id, limit=10)
        chat_history = []
        for msg in reversed(recent_messages):
            if msg.message_order < updated_message.message_order:
                chat_history.append((msg.user_message, msg.ai_response))

        start_time = time.time()

        # Generate new AI response based on the edited message
        if db_session.alert_id:
            # Alert-specific chat
            db_alert = DatabaseService.get_alert_by_id(db, db_session.alert_id)
            if not db_alert:
                raise HTTPException(status_code=404, detail="Alert not found")

            alert = DatabaseService.db_alert_to_pydantic(db_alert)
            db_comments = DatabaseService.get_comments_by_alert_id(db, db_session.alert_id)
            comments = [DatabaseService.db_comment_to_pydantic(db_comment) for db_comment in db_comments]
            db_findings = DatabaseService.get_ai_findings_by_alert_id(db, db_session.alert_id)
            ai_findings = [DatabaseService.db_ai_finding_to_pydantic(db_finding) for db_finding in db_findings]

            ai_response = await ollama_service.chat_with_ai(
                user_message=edit_request.new_message,
                alert=alert,
                comments=comments,
                ai_findings=ai_findings,
                chat_history=chat_history
            )
        else:
            # General chat
            ai_response = await ollama_service.general_chat(
                user_message=edit_request.new_message,
                page_context=edit_request.page_context,
                chat_history=chat_history
            )

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Update the AI response in the database
        updated_message.ai_response = ai_response
        updated_message.response_time_ms = response_time_ms
        db.commit()
        db.refresh(updated_message)

        return ChatResponse(
            message=edit_request.new_message,
            response=ai_response,
            session_id=db_session.id,
            message_id=updated_message.id,
            created_at=updated_message.created_at,
            is_new_session=False
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error editing chat message: {str(e)}")


# Webhook Source Endpoints
import secrets
import hashlib
import json
from typing import Dict, Any


def generate_webhook_secret() -> str:
    """Generate a cryptographically secure webhook secret"""
    return secrets.token_urlsafe(32)


def generate_webhook_url(secret: str) -> str:
    """Generate webhook URL from secret"""
    return f"http://localhost:8001/api/webhooks/{secret}"


def extract_json_value(data: Dict[str, Any], path: str) -> Any:
    """Extract value from nested JSON using dot notation path"""
    try:
        keys = path.split('.')
        value = data
        for key in keys:
            if isinstance(value, dict):
                value = value.get(key)
            else:
                return None
        return value
    except:
        return None


@app.get("/api/sources", response_model=WebhookSourceListResponse)
async def get_sources(db: Session = Depends(get_db)):
    """Get all webhook sources"""
    start_time = time.time()
    try:
        db_sources = DatabaseService.get_webhook_sources(db)
        sources = [DatabaseService.db_webhook_source_to_pydantic(db_source) for db_source in db_sources]

        # Log the sources list access
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="list",
            source_id=0,
            user_id="api_user",
            username="API User",
            details={
                "source_count": len(sources),
                "active_sources": len([s for s in sources if s.is_active])
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return WebhookSourceListResponse(sources=sources, total_count=len(sources))
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="list",
            source_id=0,
            user_id="api_user",
            username="API User",
            details={
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error fetching sources: {str(e)}")


@app.post("/api/sources", response_model=WebhookSourceResponse)
async def create_source(source_data: WebhookSourceCreate, db: Session = Depends(get_db)):
    """Create a new webhook source"""
    start_time = time.time()
    try:
        # Generate unique webhook secret
        webhook_secret = generate_webhook_secret()

        # Create source in database
        db_source = DatabaseService.create_webhook_source(
            db=db,
            name=source_data.name,
            description=source_data.description,
            webhook_secret=webhook_secret,
            auth_type=source_data.auth_type.value,
            auth_header_name=source_data.auth_header_name,
            auth_header_value=source_data.auth_header_value,
            auth_bearer_token=source_data.auth_bearer_token,
            json_mapping=source_data.json_mapping
        )

        source = DatabaseService.db_webhook_source_to_pydantic(db_source)
        webhook_url = generate_webhook_url(webhook_secret)

        # Log the source creation
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="create",
            source_id=db_source.id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": source_data.name,
                "auth_type": source_data.auth_type.value,
                "mapping_fields": list(source_data.json_mapping.keys()) if source_data.json_mapping else []
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return WebhookSourceResponse(source=source, webhook_url=webhook_url)

    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="create",
            source_id=0,
            user_id="api_user",
            username="API User",
            details={
                "source_name": getattr(source_data, 'name', 'unknown'),
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error creating source: {str(e)}")


@app.get("/api/sources/{source_id}", response_model=WebhookSourceResponse)
async def get_source(source_id: int, db: Session = Depends(get_db)):
    """Get a specific webhook source"""
    start_time = time.time()
    try:
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if not db_source:
            raise HTTPException(status_code=404, detail="Source not found")

        source = DatabaseService.db_webhook_source_to_pydantic(db_source)
        webhook_url = generate_webhook_url(db_source.webhook_secret)

        # Log the source access
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="view",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": db_source.name,
                "is_active": db_source.is_active,
                "alert_count": db_source.alert_count
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return WebhookSourceResponse(source=source, webhook_url=webhook_url)
    except HTTPException:
        raise
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="view",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error fetching source: {str(e)}")


@app.put("/api/sources/{source_id}", response_model=WebhookSourceResponse)
async def update_source(source_id: int, source_data: WebhookSourceUpdate, db: Session = Depends(get_db)):
    """Update a webhook source"""
    start_time = time.time()
    try:
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if not db_source:
            raise HTTPException(status_code=404, detail="Source not found")

        # Store original values for logging
        original_name = db_source.name
        original_active = db_source.is_active

        # Update source
        updated_source = DatabaseService.update_webhook_source(db, source_id, source_data)
        source = DatabaseService.db_webhook_source_to_pydantic(updated_source)
        webhook_url = generate_webhook_url(updated_source.webhook_secret)

        # Determine what changed
        changes = {}
        if source_data.name and source_data.name != original_name:
            changes["name"] = {"from": original_name, "to": source_data.name}
        if source_data.is_active is not None and source_data.is_active != original_active:
            changes["is_active"] = {"from": original_active, "to": source_data.is_active}
            action = "enable" if source_data.is_active else "disable"
        else:
            action = "update"

        # Log the source update
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action=action,
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": updated_source.name,
                "changes": changes,
                "updated_fields": [k for k, v in source_data.model_dump(exclude_unset=True).items() if v is not None]
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return WebhookSourceResponse(source=source, webhook_url=webhook_url)

    except HTTPException:
        raise
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="update",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error updating source: {str(e)}")


@app.delete("/api/sources/{source_id}")
async def delete_source(source_id: int, db: Session = Depends(get_db)):
    """Delete a webhook source"""
    start_time = time.time()
    try:
        db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
        if not db_source:
            raise HTTPException(status_code=404, detail="Source not found")

        # Store source info for logging
        source_name = db_source.name
        alert_count = db_source.alert_count

        DatabaseService.delete_webhook_source(db, source_id)

        # Log the source deletion
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="delete",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": source_name,
                "alert_count": alert_count
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return {"message": "Source deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        # Log the error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="delete",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=500, detail=f"Error deleting source: {str(e)}")


@app.post("/api/sources/{source_id}/test", response_model=WebhookTestResponse)
async def test_source(source_id: int, test_request: WebhookTestRequest, db: Session = Depends(get_db)):
    """Test webhook source with sample payload"""
    start_time = time.time()

    db_source = DatabaseService.get_webhook_source_by_id(db, source_id)
    if not db_source:
        raise HTTPException(status_code=404, detail="Source not found")

    try:
        # Test mapping with provided payload
        mapped_alert = map_webhook_payload_to_alert(test_request.test_payload, db_source.json_mapping)

        # Log successful test
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="test",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": db_source.name,
                "test_payload_size": len(str(test_request.test_payload)),
                "mapped_title": mapped_alert.get("title", ""),
                "mapped_severity": mapped_alert.get("severity", "")
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return WebhookTestResponse(
            success=True,
            mapped_alert=mapped_alert,
            errors=[],
            warnings=[]
        )
    except Exception as e:
        # Log failed test
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="test",
            source_id=source_id,
            user_id="api_user",
            username="API User",
            details={
                "source_name": db_source.name,
                "test_payload_size": len(str(test_request.test_payload)),
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )

        return WebhookTestResponse(
            success=False,
            mapped_alert=None,
            errors=[str(e)],
            warnings=[]
        )


def map_webhook_payload_to_alert(payload: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
    """Map webhook payload to alert fields using JSON path mapping"""
    mapped_alert = {}

    # Required fields with defaults
    mapped_alert["title"] = extract_json_value(payload, mapping.get("title", "title")) or "Webhook Alert"
    mapped_alert["description"] = extract_json_value(payload, mapping.get("description", "description")) or "Alert from webhook"

    # Map severity
    severity_value = extract_json_value(payload, mapping.get("severity", "severity"))
    if severity_value:
        # Normalize severity values
        severity_lower = str(severity_value).lower()
        if severity_lower in ["critical", "high", "medium", "low"]:
            mapped_alert["severity"] = severity_lower
        elif severity_lower in ["1", "critical", "crit"]:
            mapped_alert["severity"] = "critical"
        elif severity_lower in ["2", "high"]:
            mapped_alert["severity"] = "high"
        elif severity_lower in ["3", "medium", "med"]:
            mapped_alert["severity"] = "medium"
        else:
            mapped_alert["severity"] = "low"
    else:
        mapped_alert["severity"] = "medium"

    # Map source
    mapped_alert["source"] = extract_json_value(payload, mapping.get("source", "source")) or "Webhook"

    # Map additional fields if provided
    for field, path in mapping.items():
        if field not in ["title", "description", "severity", "source"]:
            value = extract_json_value(payload, path)
            if value is not None:
                mapped_alert[field] = value

    return mapped_alert


@app.post("/api/webhooks/{webhook_secret}")
async def receive_webhook(webhook_secret: str, payload: Dict[str, Any], request: Request, db: Session = Depends(get_db)):
    """Receive webhook payload and create alert"""
    start_time = time.time()

    # Find source by webhook secret
    db_source = DatabaseService.get_webhook_source_by_secret(db, webhook_secret)
    if not db_source:
        # Log webhook not found
        logging_service.log_source_event(
            db=db,
            action="webhook_received",
            source_id=0,
            user_id="webhook",
            username="Webhook",
            ip_address=request.client.host,
            details={
                "webhook_secret": webhook_secret[:8] + "...",  # Only log first 8 chars for security
                "payload_size": len(str(payload)),
                "error": "Webhook not found"
            },
            success=False,
            error_message="Webhook not found"
        )
        raise HTTPException(status_code=404, detail="Webhook not found")

    if not db_source.is_active:
        # Log disabled webhook attempt
        logging_service.log_source_event(
            db=db,
            action="webhook_received",
            source_id=db_source.id,
            user_id="webhook",
            username="Webhook",
            ip_address=request.client.host,
            details={
                "source_name": db_source.name,
                "payload_size": len(str(payload)),
                "error": "Webhook source is disabled"
            },
            success=False,
            error_message="Webhook source is disabled"
        )
        raise HTTPException(status_code=403, detail="Webhook source is disabled")

    try:
        # Map payload to alert
        mapped_alert = map_webhook_payload_to_alert(payload, db_source.json_mapping)

        # Create alert
        db_alert = DatabaseService.create_alert(
            db=db,
            title=mapped_alert["title"],
            description=mapped_alert["description"],
            severity=mapped_alert["severity"],
            source=mapped_alert["source"],
            created_by="Webhook",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )

        # Create webhook alert record
        DatabaseService.create_webhook_alert(
            db=db,
            source_id=db_source.id,
            alert_id=db_alert.id,
            raw_payload=payload
        )

        # Update source stats
        DatabaseService.update_webhook_source_stats(db, db_source.id)

        # Log successful webhook processing
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="webhook_received",
            source_id=db_source.id,
            user_id="webhook",
            username="Webhook",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            details={
                "source_name": db_source.name,
                "payload_size": len(str(payload)),
                "alert_id": db_alert.id,
                "alert_title": mapped_alert["title"],
                "alert_severity": mapped_alert["severity"]
            },
            success=True,
            response_time_ms=response_time_ms
        )

        return {"message": "Alert created successfully", "alert_id": db_alert.id}

    except Exception as e:
        # Log webhook processing error
        response_time_ms = int((time.time() - start_time) * 1000)
        logging_service.log_source_event(
            db=db,
            action="webhook_received",
            source_id=db_source.id,
            user_id="webhook",
            username="Webhook",
            ip_address=request.client.host,
            details={
                "source_name": db_source.name,
                "payload_size": len(str(payload)),
                "error": str(e)
            },
            success=False,
            error_message=str(e),
            response_time_ms=response_time_ms
        )
        raise HTTPException(status_code=400, detail=f"Error processing webhook: {str(e)}")


# Tools Status Endpoint

@app.get("/api/tools/status")
async def get_tools_status():
    """Get status of all available AI tools"""
    try:
        tool_manager = get_tool_manager()
        status = tool_manager.get_tool_status()

        return {
            "tools": status,
            "total_tools": len(status),
            "available_tools": sum(1 for tool in status.values() if tool.get("available", False)),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting tools status: {str(e)}")


@app.get("/api/tools/test")
async def test_tools():
    """Test tool initialization and logging"""
    try:
        # Import and initialize OllamaService to trigger tool logging
        from ollama_service import ollama_service

        # Test IOC extraction
        test_text = "Suspicious activity from IP ************ and domain evil-domain.com"
        iocs = ollama_service._extract_iocs_from_text(test_text)

        # Test tool checking (this should generate logs)
        tool_results = await ollama_service._check_iocs_with_tools(
            test_text,
            alert_id=999,
            user_id=1
        )

        return {
            "success": True,
            "message": "Tool test completed - check backend logs for tool usage",
            "iocs_found": iocs,
            "tool_results_length": len(tool_results) if tool_results else 0,
            "tools_enabled": ollama_service.tools_enabled,
            "available_tools": ollama_service.tool_manager.get_tool_names()
        }
    except Exception as e:
        import traceback
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }


# System Logs Endpoints

@app.get("/api/system-logs", response_model=SystemLogResponse)
async def get_system_logs(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    event_types: Optional[str] = None,
    user_ids: Optional[str] = None,
    usernames: Optional[str] = None,
    ip_addresses: Optional[str] = None,
    resource_types: Optional[str] = None,
    resource_ids: Optional[str] = None,
    actions: Optional[str] = None,
    success: Optional[bool] = None,
    log_levels: Optional[str] = None,
    search_query: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get system logs with filtering and pagination"""
    try:
        # Parse comma-separated lists
        def parse_list(value: Optional[str]) -> Optional[List[str]]:
            return value.split(',') if value else None

        # Parse dates
        def parse_date(date_str: Optional[str]) -> Optional[datetime]:
            if not date_str:
                return None
            try:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            except ValueError:
                return None

        # Create filter object
        filters = SystemLogFilter(
            start_date=parse_date(start_date),
            end_date=parse_date(end_date),
            event_types=parse_list(event_types),
            user_ids=parse_list(user_ids),
            usernames=parse_list(usernames),
            ip_addresses=parse_list(ip_addresses),
            resource_types=parse_list(resource_types),
            resource_ids=parse_list(resource_ids),
            actions=parse_list(actions),
            success=success,
            log_levels=parse_list(log_levels),
            search_query=search_query,
            limit=min(limit, 1000),  # Cap at 1000 for performance
            offset=offset
        )

        # Get logs
        db_logs, total_count = DatabaseService.get_system_logs(db, filters)

        # Convert to Pydantic models
        logs = [DatabaseService.db_system_log_to_pydantic(log) for log in db_logs]

        return SystemLogResponse(
            logs=logs,
            total_count=total_count,
            filtered_count=len(logs),
            has_more=offset + len(logs) < total_count
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching system logs: {str(e)}")


@app.get("/api/system-logs/stats", response_model=SystemLogStats)
async def get_system_log_stats(db: Session = Depends(get_db)):
    """Get system log statistics"""
    try:
        stats = DatabaseService.get_system_log_stats(db)
        return SystemLogStats(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching log stats: {str(e)}")


@app.get("/api/system-logs/filter-options")
async def get_log_filter_options(db: Session = Depends(get_db)):
    """Get available filter options for system logs"""
    try:
        return {
            "event_types": DatabaseService.get_unique_log_values(db, "event_types"),
            "usernames": DatabaseService.get_unique_log_values(db, "usernames"),
            "ip_addresses": DatabaseService.get_unique_log_values(db, "ip_addresses"),
            "resource_types": DatabaseService.get_unique_log_values(db, "resource_types"),
            "actions": DatabaseService.get_unique_log_values(db, "actions"),
            "log_levels": DatabaseService.get_unique_log_values(db, "log_levels")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filter options: {str(e)}")


@app.get("/api/system-logs/export")
async def export_system_logs(
    format: str = "json",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    event_types: Optional[str] = None,
    user_ids: Optional[str] = None,
    usernames: Optional[str] = None,
    ip_addresses: Optional[str] = None,
    resource_types: Optional[str] = None,
    resource_ids: Optional[str] = None,
    actions: Optional[str] = None,
    success: Optional[bool] = None,
    log_levels: Optional[str] = None,
    search_query: Optional[str] = None,
    limit: int = 10000,
    db: Session = Depends(get_db)
):
    """Export system logs in specified format"""
    try:
        from fastapi.responses import Response

        # Parse parameters (same as get_system_logs)
        def parse_list(value: Optional[str]) -> Optional[List[str]]:
            return value.split(',') if value else None

        def parse_date(date_str: Optional[str]) -> Optional[datetime]:
            if not date_str:
                return None
            try:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            except ValueError:
                return None

        filters = SystemLogFilter(
            start_date=parse_date(start_date),
            end_date=parse_date(end_date),
            event_types=parse_list(event_types),
            user_ids=parse_list(user_ids),
            usernames=parse_list(usernames),
            ip_addresses=parse_list(ip_addresses),
            resource_types=parse_list(resource_types),
            resource_ids=parse_list(resource_ids),
            actions=parse_list(actions),
            success=success,
            log_levels=parse_list(log_levels),
            search_query=search_query,
            limit=limit,
            offset=0
        )

        # Export logs
        exported_data = DatabaseService.export_system_logs(db, filters, format)

        # Set appropriate content type and filename
        if format.lower() == 'json':
            media_type = "application/json"
            filename = f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        elif format.lower() == 'csv':
            media_type = "text/csv"
            filename = f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            raise HTTPException(status_code=400, detail="Unsupported format. Use 'json' or 'csv'")

        return Response(
            content=exported_data,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting logs: {str(e)}")


@app.post("/api/system-logs/client")
async def log_client_event(
    log_data: dict,
    request: Request,
    db: Session = Depends(get_db)
):
    """Receive and log client-side events"""
    try:
        # Extract client IP address
        client_ip = request.client.host
        if "x-forwarded-for" in request.headers:
            client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
        elif "x-real-ip" in request.headers:
            client_ip = request.headers["x-real-ip"]

        # Create system log entry
        logging_service.log_user_action(
            db=db,
            action=log_data.get("action", "unknown"),
            user_id=log_data.get("user_id", "unknown"),
            username=log_data.get("username", "unknown"),
            session_id=log_data.get("session_id"),
            ip_address=client_ip,
            user_agent=log_data.get("details", {}).get("user_agent"),
            details=log_data.get("details", {}),
            success=log_data.get("success", True)
        )

        return {"status": "logged"}

    except Exception as e:
        # Don't raise HTTP exceptions for logging failures to avoid breaking client
        print(f"Error logging client event: {str(e)}")
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8001)
