#!/usr/bin/env python3
"""
Test script to verify tool logging in Bitzy chat

This script tests that tools are properly logged when used in Bitzy chat sessions.
"""

import os
import sys
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Configure logging to see the tool usage logs
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_tool_logging():
    """Test that tool usage is properly logged"""
    print("🧪 Testing Tool Logging in Bitzy Chat")
    print("=" * 50)
    
    try:
        # Import the Ollama service
        from ollama_service import OllamaService
        
        # Create a mock alert with IOCs
        class MockAlert:
            def __init__(self):
                self.id = 123
                self.title = "Suspicious Network Activity"
                self.description = "Connection detected to malicious IP ************ and domain evil-domain.com"
                self.metadata = {"source_ip": "*************", "file_hash": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234"}
                self.assigned_to = 5
        
        # Create service instance
        print("1. Initializing Ollama service...")
        ollama_service = OllamaService()
        
        # Create mock alert
        alert = MockAlert()
        
        print(f"2. Testing tool logging with mock alert (ID: {alert.id})")
        print(f"   Alert contains IOCs: IP, domain, file hash")
        
        # Test IOC extraction
        alert_text = f"{alert.title} {alert.description} {alert.metadata}"
        iocs = ollama_service._extract_iocs_from_text(alert_text)
        
        print(f"3. IOCs extracted: {len(iocs)} found")
        for i, ioc in enumerate(iocs[:5], 1):
            print(f"   {i}. {ioc}")
        
        # Test tool checking (this will generate logs)
        print(f"4. Running tool analysis (check logs for tool usage)...")
        print("   Expected logs:")
        print("   - 🔧 TOOL USAGE - VirusTotal: Analyzing X IOCs...")
        print("   - 🔧 TOOL USAGE - VirusTotal: Execution completed...")
        print("   - 🔧 TOOL USAGE SUMMARY - Session completed...")
        print()
        
        # This will trigger the logging
        import asyncio
        tool_results = asyncio.run(
            ollama_service._check_iocs_with_tools(
                alert_text, 
                alert_id=alert.id, 
                user_id=alert.assigned_to
            )
        )
        
        print(f"5. Tool analysis completed")
        if tool_results:
            print(f"   ✅ Tool results generated (length: {len(tool_results)} characters)")
            print(f"   Preview: {tool_results[:200]}...")
        else:
            print(f"   ℹ️ No tool results (expected if no API key configured)")
        
        print("\n" + "=" * 50)
        print("✅ Tool logging test completed!")
        print("\nTo see tool usage in action:")
        print("1. Start the backend: cd backend && python start_server.py")
        print("2. Open AlertAI: http://localhost:3000")
        print("3. Create an alert with IOCs (IP addresses, domains, hashes)")
        print("4. Chat with Bitzy about the alert")
        print("5. Check backend logs for tool usage messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_log_format():
    """Test the log format and ensure it's readable"""
    print("\n🧪 Testing Log Format")
    print("=" * 30)
    
    # Create a test logger
    test_logger = logging.getLogger("test_tool_logging")
    
    # Test different log types
    test_logger.info("🔧 TOOL SYSTEM - Initializing OllamaService with tools enabled: True")
    test_logger.info("🔧 TOOL SYSTEM - Available tools: ['virustotal']")
    test_logger.info("🔧 TOOL SYSTEM - virustotal: Available=True, Configured=True")
    test_logger.info("🤖 BITZY CHAT - Starting chat session for alert_id=123")
    test_logger.info("🤖 BITZY CHAT - User message: 'Analyze this alert'")
    test_logger.info("🤖 BITZY CHAT - Tools enabled: True")
    test_logger.info("🔧 TOOL USAGE - VirusTotal: Analyzing 3 IOCs for alert_id=123, user_id=5")
    test_logger.info("🔧 TOOL USAGE - VirusTotal: IOCs being analyzed: ['************', 'evil-domain.com', 'a1b2c3d4...']")
    test_logger.info("🔧 TOOL USAGE - VirusTotal: Execution completed in 2.5s")
    test_logger.info("🔧 TOOL USAGE - VirusTotal: Success=True")
    test_logger.info("🔧 TOOL USAGE SUMMARY - Session completed:")
    test_logger.info("🔧   - VirusTotal: Success=True, IOCs=3, Time=2.5s")
    test_logger.info("🤖 BITZY CHAT - Tool results generated, adding to AI response")
    test_logger.info("🤖 BITZY CHAT - Chat session completed for alert_id=123")
    
    print("✅ Log format test completed - check output above for readability")


def main():
    """Run all tests"""
    print("🚀 AlertAI Tool Logging Test Suite")
    print("=" * 60)
    
    # Test 1: Tool logging functionality
    success1 = test_tool_logging()
    
    # Test 2: Log format
    test_log_format()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary")
    print("=" * 60)
    
    if success1:
        print("✅ Tool logging functionality: PASS")
    else:
        print("❌ Tool logging functionality: FAIL")
    
    print("✅ Log format test: PASS")
    
    print("\n📖 How to Monitor Tool Usage:")
    print("=" * 40)
    print("""
1. **Real-time monitoring** (during development):
   tail -f backend/logs/application.log | grep -E "(🔧|🤖)"

2. **Check recent tool usage**:
   grep -E "(🔧|🤖)" backend/logs/application.log | tail -20

3. **Filter by specific tool**:
   grep "🔧 TOOL USAGE - VirusTotal" backend/logs/application.log

4. **Monitor chat sessions**:
   grep "🤖 BITZY CHAT" backend/logs/application.log

5. **Tool summary only**:
   grep "🔧 TOOL USAGE SUMMARY" backend/logs/application.log

The logs use emojis for easy filtering:
- 🔧 = Tool system and usage
- 🤖 = Bitzy chat sessions
""")


if __name__ == "__main__":
    main()
