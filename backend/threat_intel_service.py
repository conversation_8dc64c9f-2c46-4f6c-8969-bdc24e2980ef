"""
Threat Intelligence Service
==========================

This service provides threat intelligence enrichment for alerts, including:
- IP reputation lookups
- Domain reputation checks
- File hash analysis
- MITRE ATT&CK technique mapping
- Threat actor attribution
- IOC (Indicator of Compromise) analysis
"""

import random
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class ThreatLevel(Enum):
    UNKNOWN = "unknown"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class IOCType(Enum):
    IP_ADDRESS = "ip_address"
    DOMAIN = "domain"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    URL = "url"
    USER_AGENT = "user_agent"


@dataclass
class ThreatIntelligence:
    ioc_value: str
    ioc_type: IOCType
    threat_level: ThreatLevel
    confidence: float
    first_seen: datetime
    last_seen: datetime
    sources: List[str]
    threat_actors: List[str]
    malware_families: List[str]
    attack_techniques: List[str]
    description: str
    tags: List[str]


class ThreatIntelService:
    """Mock threat intelligence service that provides realistic threat data"""
    
    def __init__(self):
        self.threat_actors = [
            "APT29 (Cozy Bear)", "APT28 (Fancy Bear)", "Lazarus Group", 
            "FIN7", "Carbanak", "APT1", "Equation Group", "DarkHalo",
            "UNC2452", "APT40", "Kimsuky", "TA505", "Wizard Spider"
        ]
        
        self.malware_families = [
            "Cobalt Strike", "Mimikatz", "PowerShell Empire", "Metasploit",
            "TrickBot", "Emotet", "Ryuk", "Conti", "BlackMatter", "DarkSide",
            "Maze", "Sodinokibi", "Dridex", "IcedID", "BazarLoader"
        ]
        
        self.threat_sources = [
            "VirusTotal", "AlienVault OTX", "Recorded Future", "ThreatConnect",
            "IBM X-Force", "Cisco Talos", "FireEye", "CrowdStrike", "Mandiant",
            "Symantec", "Kaspersky", "MISP", "Abuse.ch", "URLVoid"
        ]

    def analyze_ip(self, ip_address: str) -> Optional[ThreatIntelligence]:
        """Analyze IP address for threat intelligence"""
        # Mock analysis - in real implementation, this would query threat intel APIs
        
        # Simulate some IPs being malicious
        if self._is_suspicious_ip(ip_address):
            return ThreatIntelligence(
                ioc_value=ip_address,
                ioc_type=IOCType.IP_ADDRESS,
                threat_level=random.choice([ThreatLevel.MEDIUM, ThreatLevel.HIGH, ThreatLevel.CRITICAL]),
                confidence=random.uniform(0.7, 0.95),
                first_seen=datetime.now() - timedelta(days=random.randint(1, 365)),
                last_seen=datetime.now() - timedelta(hours=random.randint(1, 48)),
                sources=random.sample(self.threat_sources, random.randint(2, 5)),
                threat_actors=random.sample(self.threat_actors, random.randint(0, 2)),
                malware_families=random.sample(self.malware_families, random.randint(0, 3)),
                attack_techniques=self._get_random_attack_techniques(),
                description=self._generate_ip_description(ip_address),
                tags=self._generate_ip_tags()
            )
        return None

    def analyze_domain(self, domain: str) -> Optional[ThreatIntelligence]:
        """Analyze domain for threat intelligence"""
        # Mock analysis for suspicious domains
        if self._is_suspicious_domain(domain):
            return ThreatIntelligence(
                ioc_value=domain,
                ioc_type=IOCType.DOMAIN,
                threat_level=random.choice([ThreatLevel.MEDIUM, ThreatLevel.HIGH]),
                confidence=random.uniform(0.6, 0.9),
                first_seen=datetime.now() - timedelta(days=random.randint(1, 180)),
                last_seen=datetime.now() - timedelta(hours=random.randint(1, 24)),
                sources=random.sample(self.threat_sources, random.randint(1, 4)),
                threat_actors=random.sample(self.threat_actors, random.randint(0, 1)),
                malware_families=random.sample(self.malware_families, random.randint(0, 2)),
                attack_techniques=self._get_random_attack_techniques(),
                description=self._generate_domain_description(domain),
                tags=self._generate_domain_tags()
            )
        return None

    def analyze_file_hash(self, file_hash: str) -> Optional[ThreatIntelligence]:
        """Analyze file hash for threat intelligence"""
        # Mock analysis for malicious files
        if len(file_hash) >= 32:  # Simulate some hashes being malicious
            return ThreatIntelligence(
                ioc_value=file_hash,
                ioc_type=IOCType.FILE_HASH,
                threat_level=random.choice([ThreatLevel.HIGH, ThreatLevel.CRITICAL]),
                confidence=random.uniform(0.8, 0.98),
                first_seen=datetime.now() - timedelta(days=random.randint(1, 90)),
                last_seen=datetime.now() - timedelta(hours=random.randint(1, 12)),
                sources=random.sample(self.threat_sources, random.randint(3, 6)),
                threat_actors=random.sample(self.threat_actors, random.randint(0, 2)),
                malware_families=random.sample(self.malware_families, random.randint(1, 3)),
                attack_techniques=self._get_random_attack_techniques(),
                description=self._generate_file_description(file_hash),
                tags=self._generate_file_tags()
            )
        return None

    def get_mitre_attack_info(self, technique_id: str) -> Dict[str, Any]:
        """Get MITRE ATT&CK technique information"""
        techniques = {
            "T1078": {
                "name": "Valid Accounts",
                "tactic": "Initial Access",
                "description": "Adversaries may obtain and abuse credentials of existing accounts",
                "detection": "Monitor for unusual account activity and login patterns",
                "mitigation": "Implement MFA and account monitoring"
            },
            "T1110": {
                "name": "Brute Force",
                "tactic": "Credential Access", 
                "description": "Adversaries may use brute force techniques to gain access",
                "detection": "Monitor for multiple failed authentication attempts",
                "mitigation": "Implement account lockout policies and rate limiting"
            },
            "T1059.001": {
                "name": "PowerShell",
                "tactic": "Execution",
                "description": "Adversaries may abuse PowerShell commands and scripts",
                "detection": "Monitor PowerShell execution and command-line arguments",
                "mitigation": "Restrict PowerShell execution and enable logging"
            },
            "T1562": {
                "name": "Impair Defenses",
                "tactic": "Defense Evasion",
                "description": "Adversaries may maliciously modify components to impair defenses",
                "detection": "Monitor for changes to security tools and configurations",
                "mitigation": "Implement configuration management and monitoring"
            }
        }
        return techniques.get(technique_id, {})

    def _is_suspicious_ip(self, ip: str) -> bool:
        """Determine if IP should be flagged as suspicious"""
        # Mock logic - flag certain IP patterns as suspicious
        suspicious_patterns = [
            "185.220.", "203.0.113.", "192.0.2.", "198.51.100."
        ]
        return any(ip.startswith(pattern) for pattern in suspicious_patterns)

    def _is_suspicious_domain(self, domain: str) -> bool:
        """Determine if domain should be flagged as suspicious"""
        suspicious_keywords = [
            "evil", "malicious", "badactor", "c2-server", "phishing",
            "malware", "trojan", "ransomware", "suspicious"
        ]
        return any(keyword in domain.lower() for keyword in suspicious_keywords)

    def _get_random_attack_techniques(self) -> List[str]:
        """Get random MITRE ATT&CK techniques"""
        techniques = [
            "T1078", "T1110", "T1059.001", "T1562", "T1055", "T1003.001",
            "T1566.001", "T1021.002", "T1213", "T1041", "T1071.001",
            "T1046", "T1134", "T1547.001", "T1553.004", "T1072"
        ]
        return random.sample(techniques, random.randint(1, 3))

    def _generate_ip_description(self, ip: str) -> str:
        """Generate description for suspicious IP"""
        descriptions = [
            f"IP address {ip} has been observed in multiple malicious campaigns",
            f"Known command and control server associated with {random.choice(self.threat_actors)}",
            f"IP {ip} identified as part of botnet infrastructure",
            f"Malicious IP hosting phishing and malware distribution sites",
            f"IP address linked to credential harvesting operations"
        ]
        return random.choice(descriptions)

    def _generate_domain_description(self, domain: str) -> str:
        """Generate description for suspicious domain"""
        descriptions = [
            f"Domain {domain} identified as command and control infrastructure",
            f"Malicious domain used for phishing and credential theft",
            f"Domain associated with malware distribution campaigns",
            f"Suspicious domain hosting exploit kits and malicious payloads",
            f"Domain {domain} linked to data exfiltration activities"
        ]
        return random.choice(descriptions)

    def _generate_file_description(self, file_hash: str) -> str:
        """Generate description for malicious file"""
        descriptions = [
            f"Malicious executable detected with hash {file_hash[:16]}...",
            f"Known malware sample from {random.choice(self.malware_families)} family",
            f"Trojan dropper used in targeted attacks",
            f"Ransomware payload identified in multiple campaigns",
            f"Credential stealing malware with persistence capabilities"
        ]
        return random.choice(descriptions)

    def _generate_ip_tags(self) -> List[str]:
        """Generate tags for IP threats"""
        tag_options = [
            "botnet", "c2", "malware", "phishing", "scanning", 
            "bruteforce", "tor-exit", "proxy", "vpn", "compromised"
        ]
        return random.sample(tag_options, random.randint(1, 4))

    def _generate_domain_tags(self) -> List[str]:
        """Generate tags for domain threats"""
        tag_options = [
            "phishing", "malware", "c2", "dga", "typosquatting",
            "suspicious", "newly-registered", "fast-flux", "bulletproof"
        ]
        return random.sample(tag_options, random.randint(1, 3))

    def _generate_file_tags(self) -> List[str]:
        """Generate tags for file threats"""
        tag_options = [
            "malware", "trojan", "ransomware", "backdoor", "dropper",
            "stealer", "keylogger", "rat", "worm", "rootkit"
        ]
        return random.sample(tag_options, random.randint(1, 3))


# Global threat intelligence service instance
threat_intel_service = ThreatIntelService()
