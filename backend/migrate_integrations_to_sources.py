#!/usr/bin/env python3
"""
Migration script to rename webhook_integrations table to webhook_sources
and update related foreign key references.
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database(db_path):
    """Create a backup of the database before migration"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    return backup_path

def migrate_database(db_path):
    """Migrate the database from integrations to sources naming"""
    
    # Create backup first
    backup_path = backup_database(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting migration...")
        
        # Check if old table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='webhook_integrations'")
        if not cursor.fetchone():
            print("webhook_integrations table not found. Migration may have already been completed.")
            return
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # 1. Create new webhook_sources table with same structure
        print("Creating webhook_sources table...")
        cursor.execute("""
            CREATE TABLE webhook_sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR NOT NULL,
                description TEXT,
                webhook_secret VARCHAR NOT NULL UNIQUE,
                auth_type VARCHAR NOT NULL,
                auth_header_name VARCHAR,
                auth_header_value VARCHAR,
                auth_bearer_token VARCHAR,
                json_mapping JSON NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME,
                last_used_at DATETIME,
                alert_count INTEGER DEFAULT 0
            )
        """)
        
        # 2. Copy data from webhook_integrations to webhook_sources
        print("Copying data from webhook_integrations to webhook_sources...")
        cursor.execute("""
            INSERT INTO webhook_sources 
            SELECT * FROM webhook_integrations
        """)
        
        # 3. Update webhook_alerts table to reference source_id instead of integration_id
        print("Updating webhook_alerts table...")
        
        # Create new webhook_alerts table with source_id
        cursor.execute("""
            CREATE TABLE webhook_alerts_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_id INTEGER NOT NULL,
                alert_id INTEGER NOT NULL,
                raw_payload JSON NOT NULL,
                processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_id) REFERENCES webhook_sources (id),
                FOREIGN KEY (alert_id) REFERENCES alerts (id)
            )
        """)
        
        # Copy data from old webhook_alerts to new one, renaming integration_id to source_id
        cursor.execute("""
            INSERT INTO webhook_alerts_new (id, source_id, alert_id, raw_payload, processed_at)
            SELECT id, integration_id, alert_id, raw_payload, processed_at 
            FROM webhook_alerts
        """)
        
        # 4. Drop old tables
        print("Dropping old tables...")
        cursor.execute("DROP TABLE webhook_alerts")
        cursor.execute("DROP TABLE webhook_integrations")
        
        # 5. Rename new table
        cursor.execute("ALTER TABLE webhook_alerts_new RENAME TO webhook_alerts")
        
        # Commit transaction
        cursor.execute("COMMIT")
        
        print("Migration completed successfully!")
        print("Tables renamed:")
        print("  webhook_integrations -> webhook_sources")
        print("  webhook_alerts.integration_id -> webhook_alerts.source_id")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        print("Rolling back changes...")
        cursor.execute("ROLLBACK")
        
        # Restore from backup
        print(f"Restoring database from backup: {backup_path}")
        shutil.copy2(backup_path, db_path)
        
        raise e
    
    finally:
        conn.close()

def main():
    """Main migration function"""
    db_path = "alertai.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return
    
    print(f"Migrating database: {db_path}")
    migrate_database(db_path)
    print("Migration process completed.")

if __name__ == "__main__":
    main()
