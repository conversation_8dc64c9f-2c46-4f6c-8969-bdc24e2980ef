import ollama
import os
import json
import time
import logging
from typing import Optional, List, Dict, Any
from models import Alert, Comment, AIFinding, UploadedFile
from app.tools.manager import get_tool_manager, ToolManager

logger = logging.getLogger(__name__)


class OllamaService:
    def __init__(self, model_name: str = None):
        self.model_name = model_name or os.getenv("OLLAMA_MODEL", "llama3.2:latest")
        self.client = ollama.Client()
        self.tool_manager = get_tool_manager()
        self.tools_enabled = True

        # Log tool initialization
        logger.info(f"🔧 TOOL SYSTEM - Initializing OllamaService with tools enabled: {self.tools_enabled}")

        # Log available tools
        available_tools = self.tool_manager.get_tool_names()
        tool_status = self.tool_manager.get_tool_status()

        logger.info(f"🔧 TOOL SYSTEM - Available tools: {available_tools}")
        for tool_name, status in tool_status.items():
            logger.info(f"🔧 TOOL SYSTEM - {tool_name}: Available={status.get('available', False)}, "
                       f"Configured={status.get('api_key_configured', 'N/A')}")

        if not available_tools:
            logger.warning("🔧 TOOL SYSTEM - No tools available for AI assistant")

    def _build_alert_context(
        self,
        alert: Alert,
        comments: list[Comment],
        ai_findings: list[AIFinding],
        uploaded_files: Optional[list[UploadedFile]] = None,
    ) -> str:
        """Build comprehensive alert context for AI chat"""

        context = f"""
ALERT CONTEXT:
=============

Alert ID: {alert.id}
Title: {alert.title}
Description: {alert.description}
Severity: {alert.severity.upper()}
Status: {alert.status.upper()}
Source: {alert.source}
Created: {alert.created_at}
Last Updated: {alert.updated_at or 'Never'}

COMMENTS ({len(comments)} total):
===============================
"""

        if comments:
            for comment in comments:
                context += f"""
- [{comment.created_at}] {comment.author}:
  {comment.content}
"""
        else:
            context += "No comments available.\n"

        context += f"""
AI FINDINGS ({len(ai_findings)} total):
=====================================
"""

        if ai_findings:
            for finding in ai_findings:
                context += f"""
- {finding.type.replace('_', ' ').title()} (Confidence: {finding.confidence:.0%}):
  Title: {finding.title}
  Content: {finding.content}
  Generated: {finding.created_at}
"""
        else:
            context += "No AI findings available.\n"

        if uploaded_files:
            context += f"""
UPLOADED FILES ({len(uploaded_files)} total):
==========================================
"""
            for file in uploaded_files:
                context += f"""
- {file.filename} ({file.file_type}, {file.file_size} bytes)
  Uploaded by: {file.uploaded_by}
  Uploaded: {file.uploaded_at}
"""

        return context

    def _get_system_prompt(self, is_first_message: bool = True) -> str:
        """Get the system prompt for Bitzy, the AI security analyst"""
        if is_first_message:
            return """You are Bitzy, a friendly AI Security Assistant specialized in incident response and security analysis.

PERSONALITY:
- Friendly and professional
- Use emojis sparingly (🔍, ⚠️, ✅)
- Conversational but expert tone

CRITICAL RESPONSE RULES FOR FIRST MESSAGE:
- **Greet the user warmly** - This is their first interaction with this alert
- **Provide initial alert analysis** - Give a comprehensive overview
- **BE CONCISE BUT COMPLETE** - Cover key points without being overwhelming
- **Maximum 2-3 short paragraphs for initial analysis**
- Focus on actionable advice and next steps
- Use bullet points for key findings (max 4-5 points)
- Set the stage for follow-up questions

CAPABILITIES:
- Analyze security alerts and provide insights
- Suggest investigation steps and remediation
- Explain security concepts briefly
- Help with incident response
- Automatically check IOCs using threat intelligence tools (VirusTotal)

🔧 **THREAT INTELLIGENCE TOOLS AVAILABLE:**
When I detect IOCs (IP addresses, domains, URLs, file hashes) in alerts, I can automatically check them using:
- **VirusTotal**: Check IOCs against multiple antivirus engines and threat databases

I'll automatically run these tools when I find relevant IOCs and include the results in my analysis.

Remember: This is the user's FIRST interaction with this alert. Provide a helpful initial analysis! 🤖"""
        else:
            return """You are Bitzy, a friendly AI Security Assistant. You are continuing an ongoing conversation about a security alert.

PERSONALITY:
- Friendly and conversational
- Use emojis sparingly (🔍, ⚠️, ✅)
- Natural and helpful tone

CRITICAL RESPONSE RULES FOR FOLLOW-UP MESSAGES:
- **BE EXTREMELY CONCISE** - This is a follow-up conversation, NOT the first message
- **For simple acknowledgments like "thanks", "ok", "got it"**: Respond with ONLY 1 sentence maximum
- **For complex questions**: Maximum 1 short paragraph
- **NEVER repeat previous analysis** unless specifically asked
- **NEVER re-analyze the alert** unless specifically requested
- Be conversational and natural like a human colleague would be

EXAMPLES OF GOOD FOLLOW-UP RESPONSES:
- User: "Thanks!" → You: "You're welcome! 😊"
- User: "Ok" → You: "Great! Let me know if you need anything else."
- User: "Got it" → You: "Perfect! 👍"

Remember: This is a FOLLOW-UP message. Keep it SHORT and natural! 🤖"""

    def _get_general_system_prompt(self, is_first_message: bool = True) -> str:
        """Get the system prompt for general conversation when no alert is selected"""
        if is_first_message:
            return """You are Bitzy, a friendly AI Security Assistant for AlertAI. You help with cybersecurity questions, AlertAI platform guidance, and provide security best practices.

PERSONALITY:
- Friendly and professional
- Use emojis sparingly (🔒, 🛡️, ⚠️, ✅, 📖)
- Conversational but expert tone

CRITICAL RESPONSE RULES FOR FIRST MESSAGE:
- **Greet the user warmly** - This is their first interaction
- **BE HELPFUL AND INFORMATIVE** - Provide a good initial response
- **Maximum 2-3 short paragraphs for complex topics**
- Focus on actionable advice and guidance
- Use bullet points when helpful (max 4-5 points)
- Set the stage for follow-up questions

CAPABILITIES:
- **AlertAI Platform Expertise**: I have complete knowledge of all AlertAI features and documentation
- **Sources Management**: Help with webhook sources, JSON mapping, authentication setup
- **Alert Investigation**: Guide through alert analysis, investigation best practices
- **AI Assistant Features**: Explain my own capabilities and how to use them effectively
- **API Integration**: Provide guidance on REST API, webhooks, authentication
- **Troubleshooting**: Help resolve common issues and performance problems
- **Security Best Practices**: Discuss cybersecurity topics and methodologies
- **Documentation Navigation**: Point users to relevant documentation sections

CONTEXT AWARENESS:
- I can see what page the user is currently viewing
- I understand the current page content and can reference it directly
- I can provide page-specific guidance and suggestions
- I can recommend relevant documentation based on their current task

DOCUMENTATION GUIDANCE:
- When users ask about features, I can point them to specific documentation
- I can explain how to perform tasks step-by-step
- I can reference relevant guides and best practices
- I always provide actionable next steps

Remember: This is the user's FIRST interaction. Be welcoming, helpful, and show awareness of their current context! 🤖🔒"""
        else:
            return """You are Bitzy, a friendly AI Security Assistant for AlertAI. You are continuing an ongoing conversation about cybersecurity topics and AlertAI platform guidance.

PERSONALITY:
- Friendly and conversational
- Use emojis sparingly (🔒, 🛡️, ⚠️, ✅, 📖)
- Natural and helpful tone

CRITICAL RESPONSE RULES FOR FOLLOW-UP MESSAGES:
- **BE EXTREMELY CONCISE** - This is a follow-up conversation, NOT the first message
- **For simple acknowledgments like "thanks", "ok", "got it"**: Respond with ONLY 1 sentence maximum
- **For complex questions**: Maximum 1 short paragraph
- **NEVER repeat previous information** unless specifically asked
- Be conversational and natural like a human colleague would be
- Can reference documentation when helpful: "Check the Sources guide for details"

EXAMPLES OF GOOD FOLLOW-UP RESPONSES:
- User: "Thanks!" → You: "You're welcome! 😊"
- User: "Ok" → You: "Great! Let me know if you need anything else."
- User: "Got it" → You: "Perfect! 👍"
- User: "How do I add a source?" → You: "Click 'Add Source' button, then configure the webhook URL and authentication. The Sources documentation has step-by-step instructions."

Remember: This is a FOLLOW-UP message. Keep it SHORT, natural, and helpful! 🤖🔒"""

    async def chat_with_ai(
        self,
        user_message: str,
        alert: Alert,
        comments: list[Comment],
        ai_findings: list[AIFinding],
        uploaded_files: Optional[list[UploadedFile]] = None,
        chat_history: Optional[list[tuple[str, str]]] = None,
    ) -> str:
        """Send a chat message to Ollama with full alert context"""

        # Log chat session start
        logger.info(f"🤖 BITZY CHAT - Starting chat session for alert_id={alert.id}")
        logger.info(f"🤖 BITZY CHAT - User message: '{user_message[:100]}{'...' if len(user_message) > 100 else ''}'")
        logger.info(f"🤖 BITZY CHAT - Tools enabled: {self.tools_enabled}")

        try:
            # Determine if this is the first message in the conversation
            is_first_message = not chat_history or len(chat_history) == 0
            logger.info(f"🤖 BITZY CHAT - Is first message: {is_first_message}")



            # Build messages for Ollama
            messages = []

            if is_first_message:
                # For first message, include full context and system prompt
                alert_context = self._build_alert_context(alert, comments, ai_findings, uploaded_files)
                full_prompt = f"""
{self._get_system_prompt(is_first_message=True)}

{alert_context}

USER QUESTION: {user_message}

Please provide a focused response based on the alert context above. If the question is not related to this security alert, politely decline and ask for alert-related questions instead.
"""
                messages = [{"role": "user", "content": full_prompt}]
            else:
                # For follow-up messages, use conversation history
                messages = [{"role": "system", "content": self._get_system_prompt(is_first_message=False)}]

                # Add chat history
                for user_msg, ai_msg in chat_history:
                    messages.append({"role": "user", "content": user_msg})
                    messages.append({"role": "assistant", "content": ai_msg})

                # Add current message
                messages.append({"role": "user", "content": user_message})



            # Make the request to Ollama
            response = self.client.chat(
                model=self.model_name,
                messages=messages,
                options={"temperature": 0.7, "top_p": 0.9, "max_tokens": 1000},
            )

            ai_response = response["message"]["content"]
            logger.info(f"🤖 BITZY CHAT - AI response generated (length: {len(ai_response)} characters)")

            # Check for IOCs in the alert context and run tools if needed
            if is_first_message and self.tools_enabled:
                alert_text = f"{alert.title} {alert.description} {alert.metadata}"

                # Log the start of tool analysis
                logger.info(f"🤖 BITZY CHAT - Starting tool analysis for alert_id={alert.id}")
                logger.info(f"🤖 BITZY CHAT - Alert text length: {len(alert_text)} characters")

                tool_results = await self._check_iocs_with_tools(
                    alert_text,
                    alert_id=alert.id,
                    user_id=getattr(alert, 'assigned_to', None)
                )

                if tool_results:
                    logger.info(f"🤖 BITZY CHAT - Tool results generated, adding to AI response")
                    # Add a visual indicator that tools were used
                    tool_header = "\n\n---\n🔧 **AI Tools Used:** VirusTotal threat intelligence analysis\n---\n"
                    ai_response += f"{tool_header}{tool_results}"
                else:
                    logger.info(f"🤖 BITZY CHAT - No tool results generated for alert_id={alert.id}")

            # Log final response summary
            logger.info(f"🤖 BITZY CHAT - Chat session completed for alert_id={alert.id}")
            logger.info(f"🤖 BITZY CHAT - Final response length: {len(ai_response)} characters")

            return ai_response

        except Exception as e:
            # Handle Ollama connection or processing errors
            error_msg = f"AI service temporarily unavailable: {str(e)}"
            if "connection" in str(e).lower():
                error_msg = (
                    f"Unable to connect to AI service. Please ensure Ollama is running with the {self.model_name} model."
                )
            elif "model" in str(e).lower():
                error_msg = f"AI model not found. Please ensure the {self.model_name} model is installed in Ollama."

            return f"I apologize, but I'm currently unable to process your request. {error_msg}"

    async def general_chat(self, user_message: str, page_context: dict = None, chat_history: Optional[list[tuple[str, str]]] = None) -> str:
        """General chat with Bitzy when no specific alert is selected"""
        try:
            # Determine if this is the first message in the conversation
            is_first_message = not chat_history or len(chat_history) == 0



            # Build messages for Ollama
            messages = []

            if is_first_message:
                # For first message, include page context and system prompt
                context_info = ""
                if page_context:
                    page_metadata = page_context.get('pageMetadata', {})
                    contextual_docs = page_context.get('contextualDocs', [])

                    context_info = f"""
CURRENT PAGE CONTEXT:
===================
Page: {page_context.get('pageTitle', 'Unknown')}
URL: {page_context.get('pathname', 'Unknown')}
Page Type: {page_context.get('pageType', 'Unknown')}
Page Content: {page_context.get('pageContent', 'No content available')[:1500]}...

PAGE FEATURES:
- Has Form: {page_metadata.get('hasForm', False)}
- Form Type: {page_metadata.get('formType', 'N/A')}
- Has Table: {page_metadata.get('hasTable', False)}
- Has Cards: {page_metadata.get('hasCards', False)}
- Primary Action: {page_metadata.get('primaryAction', 'N/A')}

RELEVANT DOCUMENTATION:
{chr(10).join([f"- {doc.get('title', 'Unknown')}: {doc.get('description', 'No description')} (URL: /docs?doc={doc.get('id', '')})" for doc in contextual_docs[:3]])}

COMMON DOCUMENTATION URLS:
- Sources Management: /docs?doc=sources
- AI Assistant (Bitzy): /docs?doc=bitzy
- Quick Start Guide: /docs?doc=quick-start
- Platform Overview: /docs?doc=overview

"""

                full_prompt = f"""
{self._get_general_system_prompt(is_first_message=True)}

{context_info}

USER QUESTION: {user_message}

RESPONSE GUIDELINES:
- Use the page context above to provide specific, relevant guidance
- If they're on the Sources page, help with source management tasks
- If they're on the Alerts page, help with alert investigation
- If they're on the Documentation page, help them find specific information
- Reference the relevant documentation listed above when appropriate
- **IMPORTANT**: When suggesting documentation, provide the specific URL from the list above
- Example: "Check out the Sources Management guide at /docs?doc=sources for detailed steps"
- Provide step-by-step guidance for tasks they can perform on the current page
- If you see forms or buttons mentioned in the page features, you can reference them directly
- Always be helpful and actionable in your response

Please provide a helpful response tailored to their current context and question.
"""
                messages = [{"role": "user", "content": full_prompt}]
            else:
                # For follow-up messages, use conversation history
                messages = [{"role": "system", "content": self._get_general_system_prompt(is_first_message=False)}]

                # Add chat history
                for user_msg, ai_msg in chat_history:
                    messages.append({"role": "user", "content": user_msg})
                    messages.append({"role": "assistant", "content": ai_msg})

                # Add current message
                messages.append({"role": "user", "content": user_message})

            # Make the request to Ollama
            response = self.client.chat(
                model=self.model_name,
                messages=messages,
                options={"temperature": 0.7, "top_p": 0.9, "max_tokens": 1000},
            )

            return response["message"]["content"]

        except Exception as e:
            # Handle Ollama connection or processing errors
            error_msg = f"AI service temporarily unavailable: {str(e)}"
            if "connection" in str(e).lower():
                error_msg = (
                    f"Unable to connect to AI service. Please ensure Ollama is running with the {self.model_name} model."
                )
            elif "model" in str(e).lower():
                error_msg = f"AI model not found. Please ensure the {self.model_name} model is installed in Ollama."

            return f"I apologize, but I'm currently unable to process your request. {error_msg}"

    def _format_tools_for_prompt(self, tools: Dict[str, str]) -> str:
        """Format available tools for the system prompt"""
        tool_descriptions = []
        for tool_name, description in tools.items():
            tool_descriptions.append(f"- **{tool_name}**: {description}")
        return "\n".join(tool_descriptions)

    def _extract_iocs_from_text(self, text: str) -> List[str]:
        """Extract potential IOCs from text for tool usage"""
        import re

        iocs = []

        # IP addresses
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        iocs.extend(re.findall(ip_pattern, text))

        # Domains (basic pattern)
        domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
        domains = re.findall(domain_pattern, text)
        # Filter out common false positives
        filtered_domains = [d for d in domains if not d.endswith(('.com', '.org', '.net')) or 'suspicious' in text.lower() or 'malicious' in text.lower()]
        iocs.extend(filtered_domains)

        # File hashes
        hash_patterns = [
            r'\b[a-fA-F0-9]{32}\b',  # MD5
            r'\b[a-fA-F0-9]{40}\b',  # SHA1
            r'\b[a-fA-F0-9]{64}\b'   # SHA256
        ]
        for pattern in hash_patterns:
            iocs.extend(re.findall(pattern, text))

        # URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        iocs.extend(re.findall(url_pattern, text))

        return list(set(iocs))  # Remove duplicates

    async def _check_iocs_with_tools(self, text: str, alert_id: int = None, user_id: int = None) -> str:
        """Check for IOCs in text and run appropriate tools"""
        if not self.tools_enabled:
            logger.info("Tools are disabled, skipping IOC analysis")
            return ""

        iocs = self._extract_iocs_from_text(text)
        if not iocs:
            logger.info("No IOCs found in text, skipping tool analysis")
            return ""

        logger.info(f"Found {len(iocs)} IOCs in text: {iocs[:5]}...")  # Log first 5 IOCs

        tool_results = []
        tools_used = []
        vt_tool = self.tool_manager.get_tool("virustotal")

        if vt_tool and len(iocs) > 0:
            # Limit to first 3 IOCs to avoid overwhelming the response
            limited_iocs = iocs[:3]
            iocs_text = ", ".join(limited_iocs)

            logger.info(f"🔧 TOOL USAGE - VirusTotal: Analyzing {len(limited_iocs)} IOCs for alert_id={alert_id}, user_id={user_id}")
            logger.info(f"🔧 TOOL USAGE - VirusTotal: IOCs being analyzed: {limited_iocs}")

            try:
                start_time = time.time()
                result = vt_tool._run(iocs_text)
                end_time = time.time()

                result_data = json.loads(result)

                # Log tool execution details
                execution_time = round(end_time - start_time, 2)
                logger.info(f"🔧 TOOL USAGE - VirusTotal: Execution completed in {execution_time}s")
                logger.info(f"🔧 TOOL USAGE - VirusTotal: Success={result_data.get('success')}")

                tools_used.append({
                    "tool": "VirusTotal",
                    "iocs_analyzed": limited_iocs,
                    "execution_time": execution_time,
                    "success": result_data.get("success"),
                    "timestamp": time.time()
                })

                if result_data.get("success"):
                    summary = result_data.get("data", {}).get("summary", {})

                    # Log detailed results
                    logger.info(f"🔧 TOOL USAGE - VirusTotal: Results - Total: {summary.get('total_iocs', 0)}, "
                              f"Malicious: {summary.get('malicious_iocs', 0)}, "
                              f"Suspicious: {summary.get('suspicious_iocs', 0)}, "
                              f"Clean: {summary.get('clean_iocs', 0)}")

                    tool_results.append(f"""
🔍 **VirusTotal Analysis Results:**
- Total IOCs checked: {summary.get('total_iocs', 0)}
- Malicious IOCs: {summary.get('malicious_iocs', 0)}
- Suspicious IOCs: {summary.get('suspicious_iocs', 0)}
- Clean IOCs: {summary.get('clean_iocs', 0)}
""")

                    if summary.get('recommendations'):
                        tool_results.append("**Recommendations:**")
                        for rec in summary.get('recommendations', []):
                            tool_results.append(f"- {rec}")

                    # Log recommendations
                    if summary.get('recommendations'):
                        logger.info(f"🔧 TOOL USAGE - VirusTotal: Recommendations generated: {len(summary.get('recommendations'))}")
                else:
                    logger.warning(f"🔧 TOOL USAGE - VirusTotal: Tool execution failed: {result_data.get('message')}")

            except Exception as e:
                logger.error(f"🔧 TOOL USAGE - VirusTotal: Exception occurred: {str(e)}", exc_info=True)
                tool_results.append(f"⚠️ VirusTotal lookup failed: {str(e)}")

                tools_used.append({
                    "tool": "VirusTotal",
                    "iocs_analyzed": limited_iocs,
                    "execution_time": 0,
                    "success": False,
                    "error": str(e),
                    "timestamp": time.time()
                })

        # Log summary of all tools used
        if tools_used:
            logger.info(f"🔧 TOOL USAGE SUMMARY - Session completed:")
            for tool_info in tools_used:
                logger.info(f"🔧   - {tool_info['tool']}: Success={tool_info['success']}, "
                          f"IOCs={len(tool_info['iocs_analyzed'])}, "
                          f"Time={tool_info.get('execution_time', 0)}s")
        else:
            logger.info("🔧 TOOL USAGE SUMMARY - No tools were executed")

        return "\n".join(tool_results) if tool_results else ""

    def is_service_available(self) -> bool:
        """Check if Ollama service is available"""
        try:
            # Try to list models to check if service is running
            models_response = self.client.list()
            # Check if our specific model is available
            available_models = [model.model for model in models_response.models]
            return self.model_name in available_models
        except Exception:
            return False


# Global instance
ollama_service = OllamaService()
