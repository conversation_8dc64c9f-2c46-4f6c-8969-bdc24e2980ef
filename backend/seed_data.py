from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from db_service import DatabaseService
from datetime import datetime, timedelta
import random


def seed_analysts(db: Session):
    """Seed analysts data"""
    analysts_data = [
        {
            "username": "john.doe",
            "full_name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "Senior Security Analyst",
            "avatar_url": "/avatars/john.jpg"
        },
        {
            "username": "jane.smith",
            "full_name": "<PERSON>", 
            "email": "<EMAIL>",
            "role": "Security Engineer",
            "avatar_url": "/avatars/jane.jpg"
        },
        {
            "username": "mike.johnson",
            "full_name": "<PERSON>",
            "email": "<EMAIL>", 
            "role": "SOC Analyst",
            "avatar_url": "/avatars/mike.jpg"
        },
        {
            "username": "sarah.wilson",
            "full_name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "Incident Response Lead",
            "avatar_url": "/avatars/sarah.jpg"
        },
        {
            "username": "alex.chen",
            "full_name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "Security Analyst",
            "avatar_url": "/avatars/alex.jpg"
        }
    ]
    
    for analyst_data in analysts_data:
        DatabaseService.create_analyst(db, **analyst_data)
    
    print(f"✅ Seeded {len(analysts_data)} analysts")


def seed_alerts(db: Session):
    """Seed alerts data"""
    severities = ["low", "medium", "high", "critical"]
    statuses = ["open", "acknowledged", "resolved"]
    sources = ["Server Monitor", "Database", "API Gateway", "Load Balancer", "Security Scanner"]

    alert_templates = [
        ("High CPU Usage", "CPU usage exceeded 90% threshold"),
        ("Database Connection Failed", "Unable to connect to primary database"),
        ("API Response Time High", "API response time exceeded 5 seconds"),
        ("Disk Space Low", "Disk space usage above 85%"),
        ("Memory Leak Detected", "Memory usage continuously increasing"),
        ("SSL Certificate Expiring", "SSL certificate expires in 7 days"),
        ("Failed Login Attempts", "Multiple failed login attempts detected"),
        ("Service Unavailable", "Service health check failed"),
        ("Network Latency High", "Network latency above acceptable threshold"),
        ("Backup Failed", "Scheduled backup process failed"),
        ("Suspicious File Upload", "Potentially malicious file detected in upload"),
        ("Brute Force Attack", "Multiple failed authentication attempts from same IP"),
        ("Data Exfiltration Alert", "Unusual data transfer patterns detected"),
        ("Privilege Escalation", "User attempting to access unauthorized resources"),
        ("Malware Detection", "Suspicious executable behavior detected")
    ]

    for i in range(15):
        title, description = random.choice(alert_templates)
        source = random.choice(sources)
        
        alert = DatabaseService.create_alert(
            db=db,
            title=f"{title} - {source}",
            description=description,
            severity=random.choice(severities),
            status=random.choice(statuses),
            source=source
        )
        
        # Update created_at to simulate historical data
        hours_ago = random.randint(1, 72)
        minutes_ago = random.randint(0, 59)
        alert.created_at = datetime.now() - timedelta(hours=hours_ago, minutes=minutes_ago)
        
        # Sometimes update the updated_at field
        if random.choice([True, False]):
            alert.updated_at = alert.created_at + timedelta(minutes=random.randint(0, 120))
        
        db.commit()
    
    print(f"✅ Seeded 15 alerts")


def seed_comments(db: Session):
    """Seed comments data"""
    alerts = DatabaseService.get_alerts(db)
    analysts = DatabaseService.get_analysts(db)
    
    comment_templates = [
        "Investigating this issue. Initial analysis suggests it might be related to recent deployment.",
        "I've seen similar issues before. This could be caused by memory pressure on the server.",
        "Escalating to the infrastructure team for immediate attention.",
        "Applied temporary fix. Monitoring the situation closely.",
        "Root cause identified. Working on permanent solution.",
        "Issue resolved. Implementing preventive measures to avoid recurrence.",
        "Adding monitoring alerts to catch this earlier in the future.",
        "This appears to be a false positive. Adjusting detection rules.",
        "Coordinating with the development team for a proper fix.",
        "Documented the incident and updated our runbooks."
    ]
    
    total_comments = 0
    for alert in alerts:
        # Each alert gets 1-4 comments
        num_comments = random.randint(1, 4)
        for _ in range(num_comments):
            analyst = random.choice(analysts)
            content = random.choice(comment_templates)
            
            comment = DatabaseService.create_comment(
                db=db,
                alert_id=alert.id,
                author=analyst.full_name,
                author_id=analyst.id,
                content=content
            )
            
            # Update created_at to be after alert creation
            hours_after = random.randint(0, 24)
            minutes_after = random.randint(0, 59)
            comment.created_at = alert.created_at + timedelta(hours=hours_after, minutes=minutes_after)
            db.commit()
            total_comments += 1
    
    print(f"✅ Seeded {total_comments} comments")


def seed_ai_findings(db: Session):
    """Seed AI findings data"""
    alerts = DatabaseService.get_alerts(db)
    
    finding_types = ["root_cause", "recommendation", "similar_incidents"]
    
    findings_templates = {
        "root_cause": [
            ("Potential Root Cause Analysis", "Based on system metrics and historical patterns, this alert appears to be triggered by increased memory usage following the recent application deployment."),
            ("Infrastructure Bottleneck", "Analysis indicates this issue stems from insufficient database connection pool size during peak traffic hours."),
            ("Configuration Drift", "Root cause appears to be configuration changes that were not properly synchronized across all servers.")
        ],
        "recommendation": [
            ("Immediate Action Required", "Recommend restarting the affected services and implementing memory usage monitoring to prevent recurrence."),
            ("Performance Optimization", "Suggest implementing connection pooling and adding horizontal scaling to handle increased load."),
            ("Security Hardening", "Recommend implementing rate limiting and enhanced authentication mechanisms.")
        ],
        "similar_incidents": [
            ("Historical Pattern Match", "Found 3 similar incidents in the past 30 days with comparable patterns. Previous incidents were resolved by service restart."),
            ("Recurring Issue", "This type of alert has occurred 5 times in the last month, suggesting a systemic issue that needs addressing."),
            ("Known Issue Pattern", "Similar incidents typically resolve within 2-4 hours with standard mitigation procedures.")
        ]
    }
    
    total_findings = 0
    for alert in alerts:
        # Each alert gets 1-3 AI findings
        num_findings = random.randint(1, 3)
        used_types = []
        
        for _ in range(num_findings):
            # Ensure we don't duplicate finding types for the same alert
            available_types = [t for t in finding_types if t not in used_types]
            if not available_types:
                break
                
            finding_type = random.choice(available_types)
            used_types.append(finding_type)
            
            title, content = random.choice(findings_templates[finding_type])
            confidence = round(random.uniform(0.6, 0.95), 2)
            
            DatabaseService.create_ai_finding(
                db=db,
                alert_id=alert.id,
                finding_type=finding_type,
                title=title,
                content=content,
                confidence=confidence
            )
            total_findings += 1
    
    print(f"✅ Seeded {total_findings} AI findings")


def seed_file_resources(db: Session):
    """Seed file resources data"""
    alerts = DatabaseService.get_alerts(db)
    analysts = DatabaseService.get_analysts(db)
    
    file_types = [
        ("screenshot.png", "image/png", True, 245760, {"width": 1920, "height": 1080}),
        ("error_log.txt", "text/plain", False, 15360, {"lines": 342}),
        ("network_trace.pcap", "application/vnd.tcpdump.pcap", False, 1048576, {"packets": 15420}),
        ("system_report.pdf", "application/pdf", False, 524288, {"pages": 12}),
        ("memory_dump.dmp", "application/octet-stream", False, 2097152, {"size_mb": 2}),
    ]
    
    total_resources = 0
    for alert in alerts:
        # Each alert gets 0-3 file resources
        num_resources = random.randint(0, 3)
        
        for i in range(num_resources):
            filename, file_type, is_image, size, metadata = random.choice(file_types)
            analyst = random.choice(analysts)
            
            # Use placeholder images for demo
            if is_image:
                file_path = f"https://picsum.photos/400/300?random={alert.id}{i}"
                thumbnail_path = f"https://picsum.photos/100/100?random={alert.id}{i}"
            else:
                file_path = f"/uploads/{alert.id}/{filename}"
                thumbnail_path = None
            
            DatabaseService.create_file_resource(
                db=db,
                alert_id=alert.id,
                filename=filename,
                file_path=file_path,
                file_type=file_type,
                file_size=size,
                is_image=is_image,
                uploaded_by=analyst.full_name,
                uploaded_by_id=analyst.id,
                thumbnail_path=thumbnail_path,
                metadata=metadata
            )
            total_resources += 1
    
    print(f"✅ Seeded {total_resources} file resources")


def seed_database():
    """Seed the database with initial data"""
    print("🌱 Starting database seeding...")
    
    # Initialize database
    init_db()
    
    # Create session
    db = SessionLocal()
    
    try:
        # Check if data already exists
        existing_alerts = DatabaseService.get_alerts(db)
        if existing_alerts:
            print("📊 Database already contains data. Skipping seeding.")
            return
        
        # Seed data in order (analysts first due to foreign keys)
        seed_analysts(db)
        seed_alerts(db)
        seed_comments(db)
        seed_ai_findings(db)
        seed_file_resources(db)
        
        print("🎉 Database seeding completed successfully!")
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    seed_database()
