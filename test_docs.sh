#!/bin/bash

# Test script to verify documentation accessibility

echo "🧪 Testing AlertAI Documentation Accessibility"
echo "=============================================="

# Check if frontend is running
echo "1. Checking frontend service..."
if curl -s http://localhost:3001 > /dev/null; then
  echo "✅ Frontend is running"
else
  echo "❌ Frontend is not running - start with 'npm run dev' in frontend directory"
  exit 1
fi

# Test documentation API endpoints
echo "2. Testing documentation API..."

# Test main README
echo "   Testing README.md..."
RESPONSE=$(curl -s "http://localhost:3001/api/docs/README.md")
if echo "$RESPONSE" | grep -q "AlertAI Documentation"; then
  echo "   ✅ README.md accessible"
else
  echo "   ❌ README.md not accessible"
fi

# Test overview
echo "   Testing overview.md..."
RESPONSE=$(curl -s "http://localhost:3001/api/docs/overview.md")
if echo "$RESPONSE" | grep -q "AlertAI Overview"; then
  echo "   ✅ overview.md accessible"
else
  echo "   ❌ overview.md not accessible"
fi

# Test authentication docs
echo "   Testing authentication/README.md..."
RESPONSE=$(curl -s "http://localhost:3001/api/docs/authentication/README.md")
if echo "$RESPONSE" | grep -q "Authentication & Security"; then
  echo "   ✅ authentication/README.md accessible"
else
  echo "   ❌ authentication/README.md not accessible"
fi

# Test API docs
echo "   Testing api/README.md..."
RESPONSE=$(curl -s "http://localhost:3001/api/docs/api/README.md")
if echo "$RESPONSE" | grep -q "AlertAI API Reference"; then
  echo "   ✅ api/README.md accessible"
else
  echo "   ❌ api/README.md not accessible"
fi

# Test docs page
echo "3. Testing docs page..."
RESPONSE=$(curl -s "http://localhost:3001/docs")
if echo "$RESPONSE" | grep -q "AlertAI Documentation"; then
  echo "✅ Docs page accessible"
else
  echo "❌ Docs page not accessible"
fi

echo "=============================================="
echo "Documentation test complete!"
echo ""
echo "📖 To access documentation:"
echo "   Web Interface: http://localhost:3001/docs"
echo "   API Endpoint: http://localhost:3001/api/docs/{path}"
echo ""
echo "📁 Available documentation sections:"
echo "   - Getting Started: overview.md, quick-start.md, installation.md"
echo "   - Authentication: authentication/README.md, authentication/setup.md"
echo "   - Core Features: alerts/README.md, bitzy/comprehensive.md"
echo "   - API Reference: api/README.md, api/alerts.md"
echo "   - Troubleshooting: troubleshooting/common-issues.md"
