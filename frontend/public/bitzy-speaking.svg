<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient4)" stroke="#F59E0B" stroke-width="2"/>
  
  <!-- Robot body -->
  <ellipse cx="32" cy="38" rx="18" ry="16" fill="#FBBF24"/>
  
  <!-- Robot head -->
  <circle cx="32" cy="24" r="12" fill="#FBBF24"/>
  
  <!-- Eyes (excited) -->
  <circle cx="28" cy="22" r="2" fill="#1E293B"/>
  <circle cx="36" cy="22" r="2" fill="#1E293B"/>
  
  <!-- Mouth (open/speaking) -->
  <ellipse cx="32" cy="28" rx="3" ry="2" fill="#1E293B"/>
  
  <!-- Antenna -->
  <line x1="32" y1="12" x2="32" y2="8" stroke="#F59E0B" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="8" r="2" fill="#F59E0B"/>
  
  <!-- Speech waves -->
  <path d="M 44 20 Q 46 22 44 24" stroke="#F59E0B" stroke-width="2" fill="none" stroke-linecap="round"/>
  <path d="M 46 18 Q 49 22 46 26" stroke="#F59E0B" stroke-width="2" fill="none" stroke-linecap="round"/>
  <path d="M 48 16 Q 52 22 48 28" stroke="#F59E0B" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FDE68A;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
