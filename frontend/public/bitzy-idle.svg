<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient1)" stroke="#3B82F6" stroke-width="2"/>
  
  <!-- Robot body -->
  <ellipse cx="32" cy="38" rx="18" ry="16" fill="#60A5FA"/>
  
  <!-- Robot head -->
  <circle cx="32" cy="24" r="12" fill="#60A5FA"/>
  
  <!-- Eyes -->
  <circle cx="28" cy="22" r="2" fill="#1E293B"/>
  <circle cx="36" cy="22" r="2" fill="#1E293B"/>
  
  <!-- Mouth -->
  <path d="M 28 28 Q 32 30 36 28" stroke="#1E293B" stroke-width="1.5" fill="none" stroke-linecap="round"/>
  
  <!-- Antenna -->
  <line x1="32" y1="12" x2="32" y2="8" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="8" r="2" fill="#3B82F6"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DBEAFE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#93C5FD;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
