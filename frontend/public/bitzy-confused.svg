<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient5)" stroke="#EF4444" stroke-width="2"/>
  
  <!-- Robot body -->
  <ellipse cx="32" cy="38" rx="18" ry="16" fill="#F87171"/>
  
  <!-- Robot head -->
  <circle cx="32" cy="24" r="12" fill="#F87171"/>
  
  <!-- Eyes (confused/worried) -->
  <path d="M 26 20 Q 28 18 30 20" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  <path d="M 34 20 Q 36 18 38 20" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Mouth (frown) -->
  <path d="M 28 30 Q 32 26 36 30" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Antenna -->
  <line x1="32" y1="12" x2="32" y2="8" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="8" r="2" fill="#EF4444"/>
  
  <!-- Confusion lines -->
  <path d="M 20 14 L 22 16" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
  <path d="M 22 14 L 20 16" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
  <path d="M 42 14 L 44 16" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
  <path d="M 44 14 L 42 16" stroke="#EF4444" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEE2E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FECACA;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
