<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient3)" stroke="#8B5CF6" stroke-width="2"/>
  
  <!-- Robot body -->
  <ellipse cx="32" cy="38" rx="18" ry="16" fill="#A78BFA"/>
  
  <!-- Robot head -->
  <circle cx="32" cy="24" r="12" fill="#A78BFA"/>
  
  <!-- Eyes (thinking) -->
  <circle cx="28" cy="22" r="2" fill="#1E293B"/>
  <circle cx="36" cy="22" r="2" fill="#1E293B"/>
  
  <!-- Mouth (neutral/thinking) -->
  <line x1="28" y1="28" x2="36" y2="28" stroke="#1E293B" stroke-width="1.5" stroke-linecap="round"/>
  
  <!-- Antenna -->
  <line x1="32" y1="12" x2="32" y2="8" stroke="#8B5CF6" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="8" r="2" fill="#8B5CF6"/>
  
  <!-- Thought bubble -->
  <circle cx="44" cy="14" r="4" fill="#F3F4F6" stroke="#6B7280" stroke-width="1"/>
  <circle cx="40" cy="18" r="2" fill="#F3F4F6" stroke="#6B7280" stroke-width="1"/>
  <circle cx="38" cy="20" r="1" fill="#F3F4F6" stroke="#6B7280" stroke-width="1"/>
  
  <!-- Question mark in thought bubble -->
  <text x="44" y="18" font-family="Arial, sans-serif" font-size="6" fill="#6B7280" text-anchor="middle">?</text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EDE9FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C4B5FD;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
