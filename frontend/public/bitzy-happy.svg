<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient2)" stroke="#10B981" stroke-width="2"/>
  
  <!-- Robot body -->
  <ellipse cx="32" cy="38" rx="18" ry="16" fill="#34D399"/>
  
  <!-- Robot head -->
  <circle cx="32" cy="24" r="12" fill="#34D399"/>
  
  <!-- Eyes (happy) -->
  <path d="M 26 20 Q 28 22 30 20" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  <path d="M 34 20 Q 36 22 38 20" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Mouth (smile) -->
  <path d="M 26 26 Q 32 32 38 26" stroke="#1E293B" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Antenna -->
  <line x1="32" y1="12" x2="32" y2="8" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="8" r="2" fill="#10B981"/>
  
  <!-- Sparkles -->
  <circle cx="20" cy="16" r="1" fill="#FCD34D"/>
  <circle cx="44" cy="18" r="1" fill="#FCD34D"/>
  <circle cx="18" cy="32" r="1" fill="#FCD34D"/>
  <circle cx="46" cy="30" r="1" fill="#FCD34D"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D1FAE5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6EE7B7;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
