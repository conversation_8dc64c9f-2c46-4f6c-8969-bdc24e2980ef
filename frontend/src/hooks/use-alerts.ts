/**
 * use<PERSON>lerts Hook
 * 
 * Custom hook for managing alert state and operations.
 * Provides a clean interface for alert-related functionality.
 */

import { useState, useCallback, useEffect } from 'react';
import { Alert } from '@/types/alert';
import { alertsApi } from '@/services/api/alerts';
import { useAsync } from './use-async';

interface UseAlertsOptions {
  initialLimit?: number;
  initialOffset?: number;
  autoFetch?: boolean;
}

interface AlertFilters {
  search?: string;
  severity?: string;
  status?: string;
  source?: string;
  assignedTo?: string;
}

interface AlertSorting {
  field: string;
  direction: 'asc' | 'desc';
}

interface AlertPagination {
  limit: number;
  offset: number;
  total: number;
}

export function useAlerts(options: UseAlertsOptions = {}) {
  const {
    initialLimit = 100,
    initialOffset = 0,
    autoFetch = false
  } = options;

  // State
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [pagination, setPagination] = useState<AlertPagination>({
    limit: initialLimit,
    offset: initialOffset,
    total: 0
  });
  const [filters, setFilters] = useState<AlertFilters>({});
  const [sorting, setSorting] = useState<AlertSorting>({
    field: 'created_at',
    direction: 'desc'
  });

  // Async operations
  const {
    execute: fetchAlertsAsync,
    loading,
    error
  } = useAsync(alertsApi.getAlerts);

  const {
    execute: createAlertAsync,
    loading: creating
  } = useAsync(alertsApi.createAlert);

  const {
    execute: updateAlertAsync,
    loading: updating
  } = useAsync(alertsApi.updateAlert);

  const {
    execute: deleteAlertAsync,
    loading: deleting
  } = useAsync(alertsApi.deleteAlert);

  // Fetch alerts with current parameters
  const fetchAlerts = useCallback(async () => {
    try {
      const params = {
        limit: pagination.limit,
        offset: pagination.offset,
        ...filters,
        sortBy: sorting.field,
        sortOrder: sorting.direction
      };

      const response = await fetchAlertsAsync(params);
      
      if (response) {
        setAlerts(response.alerts);
        setPagination(prev => ({
          ...prev,
          total: response.total
        }));
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
    }
  }, [pagination.limit, pagination.offset, filters, sorting, fetchAlertsAsync]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<AlertFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, offset: 0 })); // Reset to first page
  }, []);

  // Update sorting
  const updateSorting = useCallback((newSorting: Partial<AlertSorting>) => {
    setSorting(prev => ({ ...prev, ...newSorting }));
    setPagination(prev => ({ ...prev, offset: 0 })); // Reset to first page
  }, []);

  // Update pagination
  const updatePagination = useCallback((newPagination: Partial<AlertPagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Create alert
  const createAlert = useCallback(async (alertData: any) => {
    try {
      const newAlert = await createAlertAsync(alertData);
      if (newAlert) {
        // Refresh alerts list
        await fetchAlerts();
        return newAlert;
      }
    } catch (error) {
      console.error('Error creating alert:', error);
      throw error;
    }
  }, [createAlertAsync, fetchAlerts]);

  // Update alert
  const updateAlert = useCallback(async (alertId: number, updateData: any) => {
    try {
      const updatedAlert = await updateAlertAsync(alertId, updateData);
      if (updatedAlert) {
        // Update local state
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? updatedAlert : alert
        ));
        return updatedAlert;
      }
    } catch (error) {
      console.error('Error updating alert:', error);
      throw error;
    }
  }, [updateAlertAsync]);

  // Delete alert
  const deleteAlert = useCallback(async (alertId: number, permanent = false) => {
    try {
      const result = await deleteAlertAsync(alertId, permanent);
      if (result) {
        // Remove from local state or refresh
        if (permanent) {
          setAlerts(prev => prev.filter(alert => alert.id !== alertId));
        } else {
          await fetchAlerts(); // Refresh to get updated state
        }
        return result;
      }
    } catch (error) {
      console.error('Error deleting alert:', error);
      throw error;
    }
  }, [deleteAlertAsync, fetchAlerts]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchAlerts();
    }
  }, [autoFetch, fetchAlerts]);

  return {
    // Data
    alerts,
    pagination,
    filters,
    sorting,

    // Loading states
    loading,
    creating,
    updating,
    deleting,

    // Error state
    error,

    // Actions
    fetchAlerts,
    createAlert,
    updateAlert,
    deleteAlert,

    // State updates
    updateFilters,
    updateSorting,
    updatePagination,

    // Computed values
    hasAlerts: alerts.length > 0,
    totalPages: Math.ceil(pagination.total / pagination.limit),
    currentPage: Math.floor(pagination.offset / pagination.limit) + 1,
  };
}
