import { useState, useEffect, useCallback, useRef } from 'react';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface AsyncOptions {
  immediate?: boolean;
  retryCount?: number;
  retryDelay?: number;
  timeout?: number;
}

export interface AsyncActions<T> {
  execute: (...args: any[]) => Promise<T | undefined>;
  reset: () => void;
  cancel: () => void;
  retry: () => Promise<T | undefined>;
}

export function useAsync<T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: AsyncOptions = {}
): [AsyncState<T>, AsyncActions<T>] {
  const {
    immediate = false,
    retryCount = 0,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const lastArgsRef = useRef<any[]>([]);
  const retryCountRef = useRef(0);
  const isCancelledRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const asyncFunctionRef = useRef(asyncFunction);

  // Update ref when function changes
  useEffect(() => {
    asyncFunctionRef.current = asyncFunction;
  }, [asyncFunction]);

  const execute = useCallback(
    async (...args: any[]): Promise<T | undefined> => {
      lastArgsRef.current = args;
      isCancelledRef.current = false;
      retryCountRef.current = 0;

      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      const attemptExecution = async (attempt: number = 0): Promise<T | undefined> => {
        try {
          if (isCancelledRef.current) {
            return undefined;
          }

          const result = await asyncFunctionRef.current(...args);

          if (!isCancelledRef.current) {
            setState({
              data: result,
              loading: false,
              error: null,
              lastUpdated: new Date(),
            });
          }

          return result;
        } catch (error) {
          if (isCancelledRef.current) {
            return undefined;
          }

          const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

          // Retry logic
          if (attempt < retryCount && !errorMessage.includes('cancelled')) {
            retryCountRef.current = attempt + 1;
            
            // Wait before retrying
            await new Promise(resolve => {
              timeoutRef.current = setTimeout(resolve, retryDelay * Math.pow(2, attempt));
            });

            if (!isCancelledRef.current) {
              return attemptExecution(attempt + 1);
            }
          }

          if (!isCancelledRef.current) {
            setState({
              data: null,
              loading: false,
              error: errorMessage,
              lastUpdated: new Date(),
            });
          }

          throw error;
        }
      };

      return attemptExecution();
    },
    [retryCount, retryDelay]
  );

  const reset = useCallback(() => {
    isCancelledRef.current = true;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  const cancel = useCallback(() => {
    isCancelledRef.current = true;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setState(prev => ({
      ...prev,
      loading: false,
      error: prev.error || 'Operation cancelled',
    }));
  }, []);

  const retry = useCallback(async (): Promise<T | undefined> => {
    return execute(...lastArgsRef.current);
  }, [execute]);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }

    // Cleanup on unmount
    return () => {
      isCancelledRef.current = true;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [immediate]); // Remove execute from dependencies to prevent infinite loop

  return [
    state,
    {
      execute,
      reset,
      cancel,
      retry,
    },
  ];
}

// Specialized hook for API calls with caching
export function useAsyncAPI<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  cacheKey?: string,
  options: AsyncOptions = {}
): [AsyncState<T>, AsyncActions<T>] {
  const cacheRef = useRef<Map<string, { data: T; timestamp: number }>>(new Map());
  const apiFunctionRef = useRef(apiFunction);
  const cacheKeyRef = useRef(cacheKey);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Update refs when props change
  useEffect(() => {
    apiFunctionRef.current = apiFunction;
    cacheKeyRef.current = cacheKey;
  }, [apiFunction, cacheKey]);

  const cachedFunction = useCallback(
    async (...args: any[]): Promise<T> => {
      const key = cacheKeyRef.current || JSON.stringify(args);
      const cached = cacheRef.current.get(key);

      // Return cached data if it's still fresh
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.data;
      }

      // Fetch fresh data
      const result = await apiFunctionRef.current(...args);

      // Cache the result
      cacheRef.current.set(key, {
        data: result,
        timestamp: Date.now(),
      });

      return result;
    },
    [] // No dependencies to prevent recreation
  );

  return useAsync(cachedFunction, options);
}

// Hook for managing multiple async operations
export function useAsyncQueue() {
  const [operations, setOperations] = useState<Map<string, AsyncState<any>>>(new Map());

  const addOperation = useCallback((id: string, operation: AsyncState<any>) => {
    setOperations(prev => new Map(prev).set(id, operation));
  }, []);

  const removeOperation = useCallback((id: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.delete(id);
      return newMap;
    });
  }, []);

  const isAnyLoading = Array.from(operations.values()).some(op => op.loading);
  const hasErrors = Array.from(operations.values()).some(op => op.error);

  return {
    operations,
    addOperation,
    removeOperation,
    isAnyLoading,
    hasErrors,
  };
}
