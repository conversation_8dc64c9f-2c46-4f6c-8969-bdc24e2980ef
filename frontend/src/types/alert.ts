export type AlertSeverity = "low" | "medium" | "high" | "critical";
export type AlertStatus = "open" | "acknowledged" | "resolved";

export interface Alert {
  id: number;
  title: string;
  description: string;
  severity: AlertSeverity;
  status: AlertStatus;
  created_at: string;
  updated_at?: string;
  source: string;
  assigned_analyst_id?: number;
  assigned_analyst_name?: string;
  tags?: string[];
  due_date?: string;
  priority?: number; // 1-5 scale
  investigation_notes?: string;
  mitre_techniques?: string[];
  escalation_level?: number; // 0-3 scale
  sla_deadline?: string;
  is_deleted?: boolean;
}

export interface AlertResponse {
  alerts: Alert[];
  total: number;
}

export interface Comment {
  id: number;
  alert_id: number;
  author: string;
  content: string;
  created_at: string;
}

export interface CommentCreate {
  content: string;
  author: string;
}

export type AIFindingType = "root_cause" | "similar_incidents" | "recommended_actions" | "impact_analysis";

export interface AIFinding {
  id: number;
  alert_id: number;
  type: AIFindingType;
  title: string;
  content: string;
  confidence: number;
  created_at: string;
}

export interface UploadedFile {
  id: number;
  alert_id: number;
  filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  uploaded_by: string;
  uploaded_at: string;
}

export interface ChatMessage {
  id: number;
  alert_id: number;
  message: string;
  response: string;
  user_id: string;
  created_at: string;
}

export interface ChatRequest {
  message: string;
  user_id?: string;
}

export interface ChatResponse {
  message: string;
  response: string;
  created_at: string;
}

export interface Analyst {
  id: number;
  username: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  role: string;
  is_active: boolean;
}

export interface CommentMention {
  id: number;
  comment_id: number;
  analyst_id: number;
  analyst_username: string;
  created_at: string;
}

export interface EnhancedComment {
  id: number;
  alert_id: number;
  author: string;
  author_id: number;
  content: string;
  original_content?: string;
  is_beautified: boolean;
  mentions: CommentMention[];
  created_at: string;
  updated_at?: string;
}

export interface CommentBeautifyRequest {
  content: string;
}

export interface CommentBeautifyResponse {
  original_content: string;
  beautified_content: string;
  created_at: string;
}

export interface AlertCreateRequest {
  title: string;
  description: string;
  severity: AlertSeverity;
  source: string;
  status?: AlertStatus;
  assigned_analyst_id?: number;
  tags?: string[];
  due_date?: string;
}

export interface AlertCreateResponse {
  alert: Alert;
  message: string;
}

export interface AlertUpdateRequest {
  title?: string;
  description?: string;
  severity?: AlertSeverity;
  status?: AlertStatus;
  source?: string;
  assigned_analyst_id?: number;
  tags?: string[];
  due_date?: string;
  priority?: number;
  investigation_notes?: string;
  mitre_techniques?: string[];
  escalation_level?: number;
  sla_deadline?: string;
}

export interface AlertUpdateResponse {
  alert: Alert;
  message: string;
}

export interface AlertDeleteResponse {
  message: string;
  deleted_alert_id: number;
}

export interface AlertChangeLog {
  id: number;
  alert_id: number;
  changed_by: string;
  changed_at: string;
  change_type: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
}

export interface AlertChangeLogResponse {
  change_logs: AlertChangeLog[];
  total_count: number;
}

export interface FileResource {
  id: number;
  alert_id: number;
  filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  is_image: boolean;
  thumbnail_path?: string;
  uploaded_by: string;
  uploaded_by_id: number;
  uploaded_at: string;
  metadata?: Record<string, any>;
}
