export interface SystemLog {
  id: number;
  timestamp: string;
  event_type: string;
  user_id?: string;
  username?: string;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  resource_type?: string;
  resource_id?: string;
  action: string;
  details?: Record<string, any>;
  success: boolean;
  error_message?: string;
  response_time_ms?: number;
  request_size_bytes?: number;
  response_size_bytes?: number;
  log_level: string;
  checksum?: string;
}

export interface SystemLogFilter {
  start_date?: string;
  end_date?: string;
  event_types?: string[];
  user_ids?: string[];
  usernames?: string[];
  ip_addresses?: string[];
  resource_types?: string[];
  resource_ids?: string[];
  actions?: string[];
  success?: boolean;
  log_levels?: string[];
  search_query?: string;
  limit?: number;
  offset?: number;
}

export interface SystemLogResponse {
  logs: SystemLog[];
  total_count: number;
  filtered_count: number;
  has_more: boolean;
}

export interface SystemLogStats {
  total_logs: number;
  logs_today: number;
  error_rate_24h: number;
  top_event_types: Array<{ event_type: string; count: number }>;
  top_users: Array<{ username: string; count: number }>;
  top_ip_addresses: Array<{ ip_address: string; count: number }>;
  avg_response_time_ms?: number;
}

export interface SystemLogFilterOptions {
  event_types: string[];
  usernames: string[];
  ip_addresses: string[];
  resource_types: string[];
  actions: string[];
  log_levels: string[];
}

export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

export type EventType = 
  | 'alert_management'
  | 'bitzy_interaction'
  | 'source_management'
  | 'user_action'
  | 'file_operation'
  | 'comment_management'
  | 'system_event';

export type ActionType = 
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'view'
  | 'search'
  | 'export'
  | 'login'
  | 'logout'
  | 'upload'
  | 'download';
