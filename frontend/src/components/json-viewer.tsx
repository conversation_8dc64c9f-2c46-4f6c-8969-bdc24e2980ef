"use client";

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface JsonViewerProps {
  data: any;
  name?: string;
  isRoot?: boolean;
  onCopy?: () => void;
}

interface JsonNodeProps {
  data: any;
  name?: string;
  level?: number;
  isLast?: boolean;
}

const JsonNode: React.FC<JsonNodeProps> = ({ data, name, level = 0, isLast = true }) => {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Auto-expand first 2 levels
  
  const getValueColor = (value: any): string => {
    if (value === null) return 'text-muted-foreground italic';
    if (typeof value === 'string') return 'text-emerald-600 dark:text-emerald-400';
    if (typeof value === 'number') return 'text-blue-600 dark:text-blue-400 font-medium';
    if (typeof value === 'boolean') return 'text-purple-600 dark:text-purple-400 font-medium';
    if (Array.isArray(value)) return 'text-orange-600 dark:text-orange-400 font-medium';
    if (typeof value === 'object') return 'text-indigo-600 dark:text-indigo-400 font-medium';
    return 'text-muted-foreground';
  };

  const getValueDisplay = (value: any): string => {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'number') return value.toString();
    if (Array.isArray(value)) return `Array(${value.length})`;
    if (typeof value === 'object') return `Object(${Object.keys(value).length})`;
    return String(value);
  };

  const isExpandable = (value: any): boolean => {
    return (typeof value === 'object' && value !== null) || Array.isArray(value);
  };

  const copyValue = (value: any) => {
    const textToCopy = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
    navigator.clipboard.writeText(textToCopy);
    toast.success('Value copied to clipboard');
  };

  const renderValue = (value: any, key?: string) => {
    if (!isExpandable(value)) {
      return (
        <div className="flex items-center gap-2 group">
          <span className={`font-mono ${getValueColor(value)}`}>
            {getValueDisplay(value)}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              copyValue(value);
            }}
            className="opacity-0 group-hover:opacity-100 h-5 w-5 p-0 transition-opacity"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      );
    }

    return (
      <div>
        <div
          className="flex items-center gap-1 cursor-pointer hover:bg-accent/50 rounded px-1 py-0.5 -mx-1 transition-colors group"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
          ) : (
            <ChevronRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
          )}
          <span className={`font-mono text-sm ${getValueColor(value)} group-hover:text-primary transition-colors`}>
            {getValueDisplay(value)}
          </span>
        </div>
        
        {isExpanded && (
          <div className="ml-4 border-l border-border pl-3 mt-1 animate-in slide-in-from-top-1 duration-200">
            {Array.isArray(value) ? (
              value.map((item, index) => (
                <div key={index} className="py-0.5">
                  <div className="flex items-start gap-2">
                    <span className="text-amber-600 dark:text-amber-400 font-mono text-sm min-w-[30px] font-medium">
                      [{index}]:
                    </span>
                    <JsonNode
                      data={item}
                      level={level + 1}
                      isLast={index === value.length - 1}
                    />
                  </div>
                </div>
              ))
            ) : (
              Object.entries(value).map(([key, val], index, array) => (
                <div key={key} className="py-0.5">
                  <div className="flex items-start gap-2">
                    <span className="text-blue-700 dark:text-blue-300 font-mono text-sm font-semibold min-w-fit">
                      "{key}":
                    </span>
                    <JsonNode
                      data={val}
                      name={key}
                      level={level + 1}
                      isLast={index === array.length - 1}
                    />
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    );
  };

  return renderValue(data, name);
};

export const JsonViewer: React.FC<JsonViewerProps> = ({ 
  data, 
  name = "root", 
  isRoot = true,
  onCopy 
}) => {
  const copyFullJson = () => {
    navigator.clipboard.writeText(JSON.stringify(data, null, 2));
    toast.success('Full JSON copied to clipboard');
    onCopy?.();
  };

  return (
    <div className="bg-muted/30 rounded-lg border p-4">
      {isRoot && (
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-foreground">Full Log Details</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={copyFullJson}
            className="h-6 px-2 text-xs flex items-center gap-1"
          >
            <Copy className="h-3 w-3" />
            Copy JSON
          </Button>
        </div>
      )}
      
      <div className="bg-background rounded border p-3 font-mono text-sm overflow-x-auto">
        <div className="space-y-1">
          {typeof data === 'object' && data !== null ? (
            Array.isArray(data) ? (
              data.map((item, index) => (
                <div key={index} className="flex items-start gap-2">
                  <span className="text-amber-600 dark:text-amber-400 font-mono text-sm min-w-[30px] font-medium">
                    [{index}]:
                  </span>
                  <JsonNode data={item} level={0} isLast={index === data.length - 1} />
                </div>
              ))
            ) : (
              Object.entries(data).map(([key, value], index, array) => (
                <div key={key} className="flex items-start gap-2">
                  <span className="text-blue-700 dark:text-blue-300 font-mono text-sm font-semibold min-w-fit">
                    "{key}":
                  </span>
                  <JsonNode
                    data={value}
                    name={key}
                    level={0}
                    isLast={index === array.length - 1}
                  />
                </div>
              ))
            )
          ) : (
            <JsonNode data={data} level={0} />
          )}
        </div>
      </div>
    </div>
  );
};

export default JsonViewer;
