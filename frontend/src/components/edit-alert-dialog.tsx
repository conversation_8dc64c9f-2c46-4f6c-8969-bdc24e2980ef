"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { updateAlert } from '@/lib/api';
import { Alert, AlertUpdateRequest, AlertSeverity, AlertStatus } from '@/types/alert';
import { Edit, Loader2, AlertTriangle, X, Plus, Calendar, User, Flag, Target } from 'lucide-react';
import { toast } from 'sonner';

interface EditAlertDialogProps {
  alert: Alert;
  onAlertUpdated?: (updatedAlert: Alert) => void;
  trigger?: React.ReactNode;
}

interface FormErrors {
  title?: string;
  description?: string;
  severity?: string;
  status?: string;
  source?: string;
  submit?: string;
}

export function EditAlertDialog({ alert, onAlertUpdated, trigger }: EditAlertDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [newTag, setNewTag] = useState('');
  
  const [formData, setFormData] = useState<AlertUpdateRequest>({
    title: alert.title,
    description: alert.description,
    severity: alert.severity,
    status: alert.status,
    source: alert.source,
    assigned_analyst_id: alert.assigned_analyst_id,
    tags: alert.tags || [],
    due_date: alert.due_date ? alert.due_date.split('T')[0] : '', // Format for date input
    priority: alert.priority || 3,
    investigation_notes: alert.investigation_notes || '',
    escalation_level: alert.escalation_level || 0,
  });

  // Reset form when alert changes
  useEffect(() => {
    setFormData({
      title: alert.title,
      description: alert.description,
      severity: alert.severity,
      status: alert.status,
      source: alert.source,
      assigned_analyst_id: alert.assigned_analyst_id,
      tags: alert.tags || [],
      due_date: alert.due_date ? alert.due_date.split('T')[0] : '',
      priority: alert.priority || 3,
      investigation_notes: alert.investigation_notes || '',
      escalation_level: alert.escalation_level || 0,
    });
    setErrors({});
  }, [alert]);

  const handleInputChange = (field: keyof AlertUpdateRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title?.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description?.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.severity) {
      newErrors.severity = 'Severity is required';
    }

    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    if (!formData.source?.trim()) {
      newErrors.source = 'Source is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      
      // Prepare update data - only send changed fields
      const updateData: AlertUpdateRequest = {};
      
      if (formData.title !== alert.title) updateData.title = formData.title;
      if (formData.description !== alert.description) updateData.description = formData.description;
      if (formData.severity !== alert.severity) updateData.severity = formData.severity;
      if (formData.status !== alert.status) updateData.status = formData.status;
      if (formData.source !== alert.source) updateData.source = formData.source;
      if (formData.assigned_analyst_id !== alert.assigned_analyst_id) updateData.assigned_analyst_id = formData.assigned_analyst_id;
      if (JSON.stringify(formData.tags) !== JSON.stringify(alert.tags)) updateData.tags = formData.tags;
      if (formData.due_date !== (alert.due_date ? alert.due_date.split('T')[0] : '')) {
        updateData.due_date = formData.due_date ? `${formData.due_date}T23:59:59Z` : undefined;
      }
      if (formData.priority !== alert.priority) updateData.priority = formData.priority;
      if (formData.investigation_notes !== alert.investigation_notes) updateData.investigation_notes = formData.investigation_notes;
      if (formData.escalation_level !== alert.escalation_level) updateData.escalation_level = formData.escalation_level;

      // Only update if there are changes
      if (Object.keys(updateData).length === 0) {
        toast.info('No changes detected');
        setIsOpen(false);
        return;
      }

      const response = await updateAlert(alert.id, updateData);
      
      setIsOpen(false);
      toast.success('Alert updated successfully');
      
      // Call callback if provided
      if (onAlertUpdated) {
        onAlertUpdated(response.alert);
      }
    } catch (error) {
      console.error('Failed to update alert:', error);
      setErrors({ submit: 'Failed to update alert. Please try again.' });
      toast.error('Failed to update alert');
    } finally {
      setIsSubmitting(false);
    }
  };

  const severityOptions: { value: AlertSeverity; label: string; color: string }[] = [
    { value: 'low', label: 'Low', color: 'bg-green-500' },
    { value: 'medium', label: 'Medium', color: 'bg-yellow-500' },
    { value: 'high', label: 'High', color: 'bg-orange-500' },
    { value: 'critical', label: 'Critical', color: 'bg-red-500' },
  ];

  const statusOptions: { value: AlertStatus; label: string; color: string }[] = [
    { value: 'open', label: 'Open', color: 'bg-red-100 text-red-800' },
    { value: 'acknowledged', label: 'Acknowledged', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'resolved', label: 'Resolved', color: 'bg-green-100 text-green-800' },
  ];

  const priorityOptions = [
    { value: 1, label: 'Very Low' },
    { value: 2, label: 'Low' },
    { value: 3, label: 'Medium' },
    { value: 4, label: 'High' },
    { value: 5, label: 'Critical' },
  ];

  const escalationOptions = [
    { value: 0, label: 'None' },
    { value: 1, label: 'Level 1' },
    { value: 2, label: 'Level 2' },
    { value: 3, label: 'Level 3' },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Alert
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Alert #{alert.id}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Title */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="title">Alert Title *</Label>
              <Input
                id="title"
                value={formData.title || ''}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Brief description of the alert"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Detailed description of the security alert"
                rows={3}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Severity */}
            <div className="space-y-2">
              <Label htmlFor="severity">Severity *</Label>
              <Select
                value={formData.severity || ''}
                onValueChange={(value) => handleInputChange('severity', value as AlertSeverity)}
              >
                <SelectTrigger className={errors.severity ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  {severityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${option.color}`} />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.severity && (
                <p className="text-sm text-red-600">{errors.severity}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status || ''}
                onValueChange={(value) => handleInputChange('status', value as AlertStatus)}
              >
                <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <Badge variant="secondary" className={option.color}>
                        {option.label}
                      </Badge>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-600">{errors.status}</p>
              )}
            </div>
          </div>

          {/* Source and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Source */}
            <div className="space-y-2">
              <Label htmlFor="source">Source *</Label>
              <Input
                id="source"
                value={formData.source || ''}
                onChange={(e) => handleInputChange('source', e.target.value)}
                placeholder="Alert source system"
                className={errors.source ? 'border-red-500' : ''}
              />
              {errors.source && (
                <p className="text-sm text-red-600">{errors.source}</p>
              )}
            </div>

            {/* Priority */}
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={formData.priority?.toString() || '3'}
                onValueChange={(value) => handleInputChange('priority', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      <div className="flex items-center gap-2">
                        <Flag className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Due Date and Escalation */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Due Date */}
            <div className="space-y-2">
              <Label htmlFor="due_date">Due Date</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="due_date"
                  type="date"
                  value={formData.due_date || ''}
                  onChange={(e) => handleInputChange('due_date', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Escalation Level */}
            <div className="space-y-2">
              <Label htmlFor="escalation_level">Escalation Level</Label>
              <Select
                value={formData.escalation_level?.toString() || '0'}
                onValueChange={(value) => handleInputChange('escalation_level', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select escalation level" />
                </SelectTrigger>
                <SelectContent>
                  {escalationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button type="button" onClick={addTag} variant="outline" size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.tags?.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-red-500"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Investigation Notes */}
          <div className="space-y-2">
            <Label htmlFor="investigation_notes">Investigation Notes</Label>
            <Textarea
              id="investigation_notes"
              value={formData.investigation_notes || ''}
              onChange={(e) => handleInputChange('investigation_notes', e.target.value)}
              placeholder="Internal investigation notes and findings..."
              rows={4}
            />
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Update Alert
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
