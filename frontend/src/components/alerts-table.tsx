"use client";

import { <PERSON><PERSON>, AlertSeverity, AlertStatus } from '@/types/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useState, useMemo } from 'react';
import { ChevronUp, ChevronDown, ChevronsUpDown, Edit, Trash2 } from 'lucide-react';
import { EditAlertDialog } from '@/components/edit-alert-dialog';
import { DeleteAlertDialog } from '@/components/delete-alert-dialog';

interface AlertsTableProps {
  alerts: Alert[];
  onSortChange?: (sortConfig: SortConfig) => void;
  selectedAlerts?: Alert[];
  onSelectionChange?: (selectedAlerts: Alert[]) => void;
  onAlertUpdated?: (updatedAlert: Alert) => void;
  onAlertDeleted?: (deletedAlertId: number) => void;
}

type SortField = 'id' | 'title' | 'description' | 'severity' | 'status' | 'source' | 'created_at' | 'updated_at';
type SortDirection = 'asc' | 'desc' | null;

interface SortConfig {
  field: SortField | null;
  direction: SortDirection;
}

function getSeverityColor(severity: AlertSeverity): string {
  switch (severity) {
    case 'critical':
      return 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700';
    case 'high':
      return 'bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700';
    case 'medium':
      return 'bg-yellow-500 hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700';
    case 'low':
      return 'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700';
    default:
      return 'bg-muted hover:bg-muted/80';
  }
}

function getStatusColor(status: AlertStatus): string {
  switch (status) {
    case 'open':
      return 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30';
    case 'acknowledged':
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/30';
    case 'resolved':
      return 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30';
    default:
      return 'bg-muted text-muted-foreground hover:bg-muted/80';
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}

export function AlertsTable({
  alerts,
  onSortChange,
  selectedAlerts = [],
  onSelectionChange,
  onAlertUpdated,
  onAlertDeleted
}: AlertsTableProps) {
  const router = useRouter();
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: null, direction: null });

  const handleRowClick = (alertId: number, e: React.MouseEvent) => {
    // Don't navigate if clicking on checkbox or action buttons
    if ((e.target as HTMLElement).closest('[data-no-navigate]')) {
      return;
    }
    router.push(`/alerts/${alertId}`);
  };

  const handleSelectAlert = (alert: Alert, checked: boolean) => {
    if (!onSelectionChange) return;

    if (checked) {
      onSelectionChange([...selectedAlerts, alert]);
    } else {
      onSelectionChange(selectedAlerts.filter(a => a.id !== alert.id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return;

    if (checked) {
      onSelectionChange(sortedAlerts);
    } else {
      onSelectionChange([]);
    }
  };



  const handleSort = (field: SortField) => {
    setSortConfig(prevConfig => {
      let newConfig: SortConfig;
      if (prevConfig.field === field) {
        // Cycle through: asc -> desc -> null
        if (prevConfig.direction === 'asc') {
          newConfig = { field, direction: 'desc' };
        } else if (prevConfig.direction === 'desc') {
          newConfig = { field: null, direction: null };
        } else {
          newConfig = { field, direction: 'asc' };
        }
      } else {
        // New field, start with ascending
        newConfig = { field, direction: 'asc' };
      }

      // Call the callback if provided
      onSortChange?.(newConfig);

      return newConfig;
    });
  };

  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return <ChevronsUpDown className="h-4 w-4 text-muted-foreground" />;
    }
    if (sortConfig.direction === 'asc') {
      return <ChevronUp className="h-4 w-4 text-primary" />;
    } else if (sortConfig.direction === 'desc') {
      return <ChevronDown className="h-4 w-4 text-primary" />;
    }
    return <ChevronsUpDown className="h-4 w-4 text-gray-400" />;
  };

  const sortedAlerts = useMemo(() => {
    if (!sortConfig.field || !sortConfig.direction) {
      return alerts;
    }

    return [...alerts].sort((a, b) => {
      const aValue = a[sortConfig.field!];
      const bValue = b[sortConfig.field!];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;
      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;

      // Handle different data types
      let comparison = 0;

      // Special handling for date fields
      if (sortConfig.field === 'created_at' || sortConfig.field === 'updated_at') {
        const aDate = new Date(aValue as string);
        const bDate = new Date(bValue as string);
        comparison = aDate.getTime() - bDate.getTime();
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else if (typeof aValue === 'string' && typeof bValue === 'string') {
        // For severity, use custom order
        if (sortConfig.field === 'severity') {
          const severityOrder = { 'low': 1, 'medium': 2, 'high': 3, 'critical': 4 };
          const aOrder = severityOrder[aValue as keyof typeof severityOrder] || 0;
          const bOrder = severityOrder[bValue as keyof typeof severityOrder] || 0;
          comparison = aOrder - bOrder;
        } else if (sortConfig.field === 'status') {
          // For status, use custom order
          const statusOrder = { 'resolved': 1, 'acknowledged': 2, 'open': 3 };
          const aOrder = statusOrder[aValue as keyof typeof statusOrder] || 0;
          const bOrder = statusOrder[bValue as keyof typeof statusOrder] || 0;
          comparison = aOrder - bOrder;
        } else {
          comparison = aValue.localeCompare(bValue);
        }
      } else {
        // Convert to strings for comparison
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [alerts, sortConfig]);

  // Selection logic (after sortedAlerts is defined)
  const isSelected = (alert: Alert) => selectedAlerts.some(a => a.id === alert.id);
  const isAllSelected = sortedAlerts.length > 0 && sortedAlerts.every(alert => isSelected(alert));

  const SortableHeader = ({ field, children, className }: {
    field: SortField;
    children: React.ReactNode;
    className?: string;
  }) => {
    const isActive = sortConfig.field === field;
    return (
      <TableHead
        className={cn(
          "cursor-pointer hover:bg-muted/50 transition-colors select-none",
          isActive && "bg-accent text-accent-foreground",
          className
        )}
        onClick={() => handleSort(field)}
        title={`Click to sort by ${field.replace('_', ' ')}. Current: ${
          isActive
            ? sortConfig.direction === 'asc'
              ? 'ascending'
              : sortConfig.direction === 'desc'
                ? 'descending'
                : 'none'
            : 'none'
        }`}
      >
        <div className="flex items-center gap-2">
          <span className={cn(isActive && "font-semibold")}>{children}</span>
          {getSortIcon(field)}
        </div>
      </TableHead>
    );
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {onSelectionChange && (
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all alerts"
                  data-no-navigate
                />
              </TableHead>
            )}
            <SortableHeader field="id" className="w-[100px]">ID</SortableHeader>
            <SortableHeader field="title">Title</SortableHeader>
            <SortableHeader field="description">Description</SortableHeader>
            <SortableHeader field="severity">Severity</SortableHeader>
            <SortableHeader field="status">Status</SortableHeader>
            <SortableHeader field="source">Source</SortableHeader>
            <SortableHeader field="created_at">Created</SortableHeader>
            <SortableHeader field="updated_at">Updated</SortableHeader>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedAlerts.map((alert) => (
            <TableRow
              key={alert.id}
              className={cn(
                "cursor-pointer hover:bg-muted/50 transition-colors",
                isSelected(alert) && "bg-accent/50"
              )}
              onClick={(e) => handleRowClick(alert.id, e)}
            >
              {onSelectionChange && (
                <TableCell data-no-navigate>
                  <Checkbox
                    checked={isSelected(alert)}
                    onCheckedChange={(checked: boolean) => handleSelectAlert(alert, checked)}
                    aria-label={`Select alert ${alert.id}`}
                  />
                </TableCell>
              )}
              <TableCell className="font-medium">{alert.id}</TableCell>
              <TableCell className="font-medium">{alert.title}</TableCell>
              <TableCell className="max-w-xs truncate">{alert.description}</TableCell>
              <TableCell>
                <Badge className={cn('text-white', getSeverityColor(alert.severity))}>
                  {alert.severity.toUpperCase()}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge variant="secondary" className={getStatusColor(alert.status)}>
                  {alert.status.toUpperCase()}
                </Badge>
              </TableCell>
              <TableCell>{alert.source}</TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {formatDate(alert.created_at)}
              </TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {alert.updated_at ? formatDate(alert.updated_at) : '-'}
              </TableCell>
              <TableCell data-no-navigate>
                <div className="flex items-center gap-2">
                  <EditAlertDialog
                    alert={alert}
                    onAlertUpdated={onAlertUpdated}
                    trigger={
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    }
                  />
                  <DeleteAlertDialog
                    alert={alert}
                    onAlertDeleted={onAlertDeleted}
                    trigger={
                      <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive/80">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    }
                  />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
