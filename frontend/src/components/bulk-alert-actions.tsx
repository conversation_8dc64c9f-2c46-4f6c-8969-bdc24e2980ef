"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { updateAlert } from '@/lib/api';
import { Alert, AlertSeverity, AlertStatus } from '@/types/alert';
import { CheckSquare, Loader2, AlertTriangle, Users, Flag, Target } from 'lucide-react';
import { toast } from 'sonner';

interface BulkAlertActionsProps {
  selectedAlerts: Alert[];
  onAlertsUpdated?: (updatedAlerts: Alert[]) => void;
  onClearSelection?: () => void;
}

interface BulkAction {
  field: 'status' | 'severity' | 'priority' | 'escalation_level';
  value: any;
  label: string;
}

export function BulkAlertActions({ selectedAlerts, onAlertsUpdated, onClearSelection }: BulkAlertActionsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedAction, setSelectedAction] = useState<BulkAction | null>(null);

  const bulkActions: BulkAction[] = [
    // Status actions
    { field: 'status', value: 'open', label: 'Mark as Open' },
    { field: 'status', value: 'acknowledged', label: 'Mark as Acknowledged' },
    { field: 'status', value: 'resolved', label: 'Mark as Resolved' },
    
    // Severity actions
    { field: 'severity', value: 'low', label: 'Set Severity: Low' },
    { field: 'severity', value: 'medium', label: 'Set Severity: Medium' },
    { field: 'severity', value: 'high', label: 'Set Severity: High' },
    { field: 'severity', value: 'critical', label: 'Set Severity: Critical' },
    
    // Priority actions
    { field: 'priority', value: 1, label: 'Set Priority: Very Low' },
    { field: 'priority', value: 2, label: 'Set Priority: Low' },
    { field: 'priority', value: 3, label: 'Set Priority: Medium' },
    { field: 'priority', value: 4, label: 'Set Priority: High' },
    { field: 'priority', value: 5, label: 'Set Priority: Critical' },
    
    // Escalation actions
    { field: 'escalation_level', value: 0, label: 'Set Escalation: None' },
    { field: 'escalation_level', value: 1, label: 'Set Escalation: Level 1' },
    { field: 'escalation_level', value: 2, label: 'Set Escalation: Level 2' },
    { field: 'escalation_level', value: 3, label: 'Set Escalation: Level 3' },
  ];

  const handleBulkUpdate = async () => {
    if (!selectedAction) {
      toast.error('Please select an action');
      return;
    }

    try {
      setIsUpdating(true);
      
      const updatePromises = selectedAlerts.map(alert => 
        updateAlert(alert.id, { [selectedAction.field]: selectedAction.value })
      );
      
      const results = await Promise.allSettled(updatePromises);
      
      // Count successful and failed updates
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');
      
      if (successful.length > 0) {
        toast.success(`${successful.length} alert(s) updated successfully`);
        
        // Extract updated alerts from successful results
        const updatedAlerts = successful
          .map(result => result.status === 'fulfilled' ? result.value.alert : null)
          .filter(Boolean) as Alert[];
        
        if (onAlertsUpdated) {
          onAlertsUpdated(updatedAlerts);
        }
      }
      
      if (failed.length > 0) {
        toast.error(`${failed.length} alert(s) failed to update`);
      }
      
      setIsOpen(false);
      setSelectedAction(null);
      
      if (onClearSelection) {
        onClearSelection();
      }
    } catch (error) {
      console.error('Bulk update failed:', error);
      toast.error('Bulk update failed');
    } finally {
      setIsUpdating(false);
    }
  };

  const getActionIcon = (field: string) => {
    switch (field) {
      case 'status':
        return <CheckSquare className="h-4 w-4" />;
      case 'severity':
        return <AlertTriangle className="h-4 w-4" />;
      case 'priority':
        return <Flag className="h-4 w-4" />;
      case 'escalation_level':
        return <Target className="h-4 w-4" />;
      default:
        return <CheckSquare className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: AlertStatus) => {
    const statusColors = {
      open: 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800',
      acknowledged: 'bg-yellow-50 dark:bg-yellow-950/20 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
      resolved: 'bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
    };

    return (
      <Badge variant="secondary" className={statusColors[status]}>
        {status}
      </Badge>
    );
  };

  const getSeverityBadge = (severity: AlertSeverity) => {
    const severityColors = {
      low: 'bg-green-500 dark:bg-green-600',
      medium: 'bg-yellow-500 dark:bg-yellow-600',
      high: 'bg-orange-500 dark:bg-orange-600',
      critical: 'bg-red-500 dark:bg-red-600',
    };

    return (
      <div className="flex items-center gap-2 text-foreground">
        <div className={`w-3 h-3 rounded-full ${severityColors[severity]}`} />
        <span className="capitalize">{severity}</span>
      </div>
    );
  };

  if (selectedAlerts.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Bulk Actions ({selectedAlerts.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Actions - {selectedAlerts.length} Alert(s) Selected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selected Alerts Preview */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Selected Alerts:</h4>
            <div className="max-h-32 overflow-y-auto border rounded-md p-2 bg-gray-50">
              {selectedAlerts.map((alert) => (
                <div key={alert.id} className="flex items-center justify-between py-1 text-sm">
                  <span className="font-medium">#{alert.id}</span>
                  <span className="truncate mx-2 flex-1">{alert.title}</span>
                  <div className="flex items-center gap-2">
                    {getSeverityBadge(alert.severity)}
                    {getStatusBadge(alert.status)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Selection */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Select Action:</h4>
            <Select
              value={selectedAction ? `${selectedAction.field}-${selectedAction.value}` : ''}
              onValueChange={(value) => {
                const [field, actionValue] = value.split('-');
                const action = bulkActions.find(a => 
                  a.field === field && a.value.toString() === actionValue
                );
                setSelectedAction(action || null);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose an action to apply to all selected alerts" />
              </SelectTrigger>
              <SelectContent>
                {/* Group actions by type */}
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 mb-2">Status Actions</div>
                  {bulkActions.filter(a => a.field === 'status').map((action) => (
                    <SelectItem key={`${action.field}-${action.value}`} value={`${action.field}-${action.value}`}>
                      <div className="flex items-center gap-2">
                        {getActionIcon(action.field)}
                        {action.label}
                      </div>
                    </SelectItem>
                  ))}
                </div>
                
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 mb-2">Severity Actions</div>
                  {bulkActions.filter(a => a.field === 'severity').map((action) => (
                    <SelectItem key={`${action.field}-${action.value}`} value={`${action.field}-${action.value}`}>
                      <div className="flex items-center gap-2">
                        {getActionIcon(action.field)}
                        {action.label}
                      </div>
                    </SelectItem>
                  ))}
                </div>
                
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 mb-2">Priority Actions</div>
                  {bulkActions.filter(a => a.field === 'priority').map((action) => (
                    <SelectItem key={`${action.field}-${action.value}`} value={`${action.field}-${action.value}`}>
                      <div className="flex items-center gap-2">
                        {getActionIcon(action.field)}
                        {action.label}
                      </div>
                    </SelectItem>
                  ))}
                </div>
                
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 mb-2">Escalation Actions</div>
                  {bulkActions.filter(a => a.field === 'escalation_level').map((action) => (
                    <SelectItem key={`${action.field}-${action.value}`} value={`${action.field}-${action.value}`}>
                      <div className="flex items-center gap-2">
                        {getActionIcon(action.field)}
                        {action.label}
                      </div>
                    </SelectItem>
                  ))}
                </div>
              </SelectContent>
            </Select>
          </div>

          {/* Preview of Action */}
          {selectedAction && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center gap-2 text-blue-800">
                {getActionIcon(selectedAction.field)}
                <span className="font-medium">Preview:</span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                {selectedAction.label} will be applied to {selectedAlerts.length} alert(s)
              </p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkUpdate}
              disabled={isUpdating || !selectedAction}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Apply to {selectedAlerts.length} Alert(s)
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
