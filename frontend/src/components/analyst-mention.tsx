"use client";

import { useState, useEffect, useRef } from 'react';
import { fetchAnalysts, beautifyComment } from '@/lib/api';
import { Analyst, CommentBeautifyRequest } from '@/types/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>rk<PERSON>, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalystMentionProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
}

interface MentionSuggestion {
  analyst: Analyst;
  position: number;
}

export function AnalystMention({
  value,
  onChange,
  placeholder = "Write your comment here...",
  rows = 3,
  className
}: AnalystMentionProps) {
  const [analysts, setAnalysts] = useState<Analyst[]>([]);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mentionStart, setMentionStart] = useState(-1);
  const [isBeautifying, setIsBeautifying] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    async function loadAnalysts() {
      try {
        const data = await fetchAnalysts();
        setAnalysts(data);
      } catch (error) {
        console.error('Failed to load analysts:', error);
      }
    }
    loadAnalysts();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const cursorPosition = e.target.selectionStart;
    
    onChange(newValue);
    
    // Check for @ mentions
    const textBeforeCursor = newValue.substring(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');
    
    if (lastAtIndex !== -1) {
      const textAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
      
      // Check if we're in a valid mention context (no spaces after @)
      if (!textAfterAt.includes(' ') && textAfterAt.length >= 0) {
        const filteredAnalysts = analysts.filter(analyst =>
          analyst.username.toLowerCase().includes(textAfterAt.toLowerCase()) ||
          analyst.full_name.toLowerCase().includes(textAfterAt.toLowerCase())
        );
        
        if (filteredAnalysts.length > 0) {
          setSuggestions(filteredAnalysts.map(analyst => ({ analyst, position: lastAtIndex })));
          setShowSuggestions(true);
          setSelectedIndex(0);
          setMentionStart(lastAtIndex);
          return;
        }
      }
    }
    
    setShowSuggestions(false);
    setSuggestions([]);
    setMentionStart(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % suggestions.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
        break;
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        insertMention(suggestions[selectedIndex].analyst);
        break;
      case 'Escape':
        setShowSuggestions(false);
        break;
    }
  };

  const insertMention = (analyst: Analyst) => {
    if (mentionStart === -1 || !textareaRef.current) return;

    const textarea = textareaRef.current;
    const cursorPosition = textarea.selectionStart;
    const textBeforeMention = value.substring(0, mentionStart);
    const textAfterCursor = value.substring(cursorPosition);
    
    const mentionText = `@${analyst.username}`;
    const newValue = textBeforeMention + mentionText + ' ' + textAfterCursor;
    
    onChange(newValue);
    setShowSuggestions(false);
    setSuggestions([]);
    setMentionStart(-1);
    
    // Set cursor position after the mention
    setTimeout(() => {
      const newCursorPosition = mentionStart + mentionText.length + 1;
      textarea.setSelectionRange(newCursorPosition, newCursorPosition);
      textarea.focus();
    }, 0);
  };

  const handleSuggestionClick = (analyst: Analyst) => {
    insertMention(analyst);
  };

  const handleBeautify = async () => {
    if (!value.trim() || isBeautifying) return;

    try {
      setIsBeautifying(true);
      const request: CommentBeautifyRequest = { content: value.trim() };
      const response = await beautifyComment(request);
      onChange(response.beautified_content);
    } catch (error) {
      console.error('Failed to beautify comment:', error);
    } finally {
      setIsBeautifying(false);
    }
  };

  const handlePaste = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        e.preventDefault();
        const file = item.getAsFile();
        if (file) {
          // Create a placeholder for the image in the text
          const imagePlaceholder = `[Image: ${file.name || 'pasted-image.png'}]`;
          const textarea = textareaRef.current;
          if (textarea) {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const newValue = value.substring(0, start) + imagePlaceholder + value.substring(end);
            onChange(newValue);

            // Set cursor position after the placeholder
            setTimeout(() => {
              textarea.setSelectionRange(start + imagePlaceholder.length, start + imagePlaceholder.length);
              textarea.focus();
            }, 0);
          }

          // Trigger the onImagePaste callback if provided
          if (typeof window !== 'undefined' && (window as any).onImagePaste) {
            (window as any).onImagePaste(file);
          }
        }
        break;
      }
    }
  };

  return (
    <div className="relative">
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          placeholder={placeholder}
          rows={rows}
          disabled={isBeautifying}
          className={cn(
            "w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
            isBeautifying && "opacity-50 cursor-not-allowed",
            className
          )}
        />

        {/* Beautify Icon */}
        <button
          type="button"
          onClick={handleBeautify}
          disabled={!value.trim() || isBeautifying}
          className={cn(
            "absolute top-2 right-2 p-1 rounded hover:bg-gray-100 transition-opacity",
            !value.trim() ? "opacity-0" : "opacity-0 hover:opacity-100"
          )}
          title="Beautify comment with AI"
        >
          {isBeautifying ? (
            <Loader2 className="h-4 w-4 animate-spin text-purple-600" />
          ) : (
            <Sparkles className="h-4 w-4 text-purple-600" />
          )}
        </button>

        {/* Loading Overlay */}
        {isBeautifying && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
            <div className="flex items-center gap-2 text-purple-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Beautifying...</span>
            </div>
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.analyst.id}
              className={cn(
                "flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-gray-50",
                index === selectedIndex && "bg-blue-50"
              )}
              onClick={() => handleSuggestionClick(suggestion.analyst)}
            >
              <Avatar className="h-6 w-6">
                <AvatarImage src={suggestion.analyst.avatar_url} />
                <AvatarFallback className="text-xs">
                  {suggestion.analyst.full_name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900">
                  {suggestion.analyst.full_name}
                </div>
                <div className="text-xs text-gray-500">
                  @{suggestion.analyst.username} • {suggestion.analyst.role}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
