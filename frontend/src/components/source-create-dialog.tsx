"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Copy, Plus, Trash2, HelpCircle } from 'lucide-react';
import { toast } from 'sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface SourceCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface JsonMapping {
  [key: string]: string;
}

interface SourceFormData {
  name: string;
  description: string;
  auth_type: 'custom_header' | 'bearer_token';
  auth_header_name: string;
  auth_header_value: string;
  auth_bearer_token: string;
  json_mapping: JsonMapping;
}

const defaultMappings = {
  title: 'title',
  description: 'description',
  severity: 'severity',
  source: 'source',
};

export function SourceCreateDialog({ open, onOpenChange, onSuccess }: SourceCreateDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<SourceFormData>({
    name: '',
    description: '',
    auth_type: 'custom_header',
    auth_header_name: '',
    auth_header_value: '',
    auth_bearer_token: '',
    json_mapping: { ...defaultMappings },
  });

  const [customMappings, setCustomMappings] = useState<Array<{ key: string; value: string }>>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Source name is required');
      return;
    }

    // Validate authentication
    if (formData.auth_type === 'custom_header') {
      if (!formData.auth_header_name.trim() || !formData.auth_header_value.trim()) {
        toast.error('Custom header name and value are required');
        return;
      }
    } else if (formData.auth_type === 'bearer_token') {
      if (!formData.auth_bearer_token.trim()) {
        toast.error('Bearer token is required');
        return;
      }
    }

    // Combine default and custom mappings
    const finalMapping = { ...formData.json_mapping };
    customMappings.forEach(({ key, value }) => {
      if (key.trim() && value.trim()) {
        finalMapping[key.trim()] = value.trim();
      }
    });

    try {
      setLoading(true);
      
      const requestData = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        auth_type: formData.auth_type,
        auth_header_name: formData.auth_type === 'custom_header' ? formData.auth_header_name.trim() : null,
        auth_header_value: formData.auth_type === 'custom_header' ? formData.auth_header_value.trim() : null,
        auth_bearer_token: formData.auth_type === 'bearer_token' ? formData.auth_bearer_token.trim() : null,
        json_mapping: finalMapping,
      };

      const response = await fetch('/api/sources', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to create source');
      }

      const result = await response.json();
      
      toast.success('Source created successfully!');
      
      // Copy webhook URL to clipboard
      if (result.webhook_url) {
        try {
          await navigator.clipboard.writeText(result.webhook_url);
          toast.success('Webhook URL copied to clipboard');
        } catch (error) {
          // Clipboard copy failed, but source was created successfully
        }
      }
      
      onSuccess();
      onOpenChange(false);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        auth_type: 'custom_header',
        auth_header_name: '',
        auth_header_value: '',
        auth_bearer_token: '',
        json_mapping: { ...defaultMappings },
      });
      setCustomMappings([]);
      
    } catch (error) {
      console.error('Error creating source:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create source');
    } finally {
      setLoading(false);
    }
  };

  const addCustomMapping = () => {
    setCustomMappings([...customMappings, { key: '', value: '' }]);
  };

  const removeCustomMapping = (index: number) => {
    setCustomMappings(customMappings.filter((_, i) => i !== index));
  };

  const updateCustomMapping = (index: number, field: 'key' | 'value', value: string) => {
    const updated = [...customMappings];
    updated[index][field] = value;
    setCustomMappings(updated);
  };

  const updateDefaultMapping = (field: string, value: string) => {
    setFormData({
      ...formData,
      json_mapping: {
        ...formData.json_mapping,
        [field]: value,
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="large-modal-content overflow-y-auto p-8">
        <DialogHeader>
          <DialogTitle>Create New Source</DialogTitle>
          <DialogDescription>
            Configure a webhook source to receive alerts from external systems.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Source Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="e.g., Production Monitoring"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Optional description"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Authentication</CardTitle>
              <CardDescription>
                Configure how external systems will authenticate with your webhook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="auth_type">Authentication Type</Label>
                <Select
                  value={formData.auth_type}
                  onValueChange={(value: 'custom_header' | 'bearer_token') =>
                    setFormData({ ...formData, auth_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="custom_header">Custom Header</SelectItem>
                    <SelectItem value="bearer_token">Bearer Token</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.auth_type === 'custom_header' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="auth_header_name">Header Name *</Label>
                    <Input
                      id="auth_header_name"
                      value={formData.auth_header_name}
                      onChange={(e) => setFormData({ ...formData, auth_header_name: e.target.value })}
                      placeholder="e.g., X-API-Key"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="auth_header_value">Header Value *</Label>
                    <Input
                      id="auth_header_value"
                      type="password"
                      value={formData.auth_header_value}
                      onChange={(e) => setFormData({ ...formData, auth_header_value: e.target.value })}
                      placeholder="Secret value"
                      required
                    />
                  </div>
                </div>
              )}

              {formData.auth_type === 'bearer_token' && (
                <div>
                  <Label htmlFor="auth_bearer_token">Bearer Token *</Label>
                  <Input
                    id="auth_bearer_token"
                    type="password"
                    value={formData.auth_bearer_token}
                    onChange={(e) => setFormData({ ...formData, auth_bearer_token: e.target.value })}
                    placeholder="Bearer token value"
                    required
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* JSON Schema Mapping */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                JSON Schema Mapping
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Define how incoming JSON fields map to alert properties.<br />
                      Use dot notation for nested fields (e.g., "payload.alert.severity")</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardTitle>
              <CardDescription>
                Map JSON fields from incoming webhooks to alert properties
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Default Mappings */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title_mapping">Title Field *</Label>
                  <Input
                    id="title_mapping"
                    value={formData.json_mapping.title}
                    onChange={(e) => updateDefaultMapping('title', e.target.value)}
                    placeholder="e.g., title or alert.title"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="description_mapping">Description Field *</Label>
                  <Input
                    id="description_mapping"
                    value={formData.json_mapping.description}
                    onChange={(e) => updateDefaultMapping('description', e.target.value)}
                    placeholder="e.g., description or alert.message"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="severity_mapping">Severity Field *</Label>
                  <Input
                    id="severity_mapping"
                    value={formData.json_mapping.severity}
                    onChange={(e) => updateDefaultMapping('severity', e.target.value)}
                    placeholder="e.g., severity or alert.level"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="source_mapping">Source Field</Label>
                  <Input
                    id="source_mapping"
                    value={formData.json_mapping.source}
                    onChange={(e) => updateDefaultMapping('source', e.target.value)}
                    placeholder="e.g., source or system.name"
                  />
                </div>
              </div>

              {/* Custom Mappings */}
              {customMappings.length > 0 && (
                <div className="space-y-2">
                  <Label>Additional Field Mappings</Label>
                  {customMappings.map((mapping, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        placeholder="Alert field name"
                        value={mapping.key}
                        onChange={(e) => updateCustomMapping(index, 'key', e.target.value)}
                        className="flex-1"
                      />
                      <span className="text-gray-400">→</span>
                      <Input
                        placeholder="JSON path (e.g., data.timestamp)"
                        value={mapping.value}
                        onChange={(e) => updateCustomMapping(index, 'value', e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCustomMapping(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <Button
                type="button"
                variant="outline"
                onClick={addCustomMapping}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Custom Field Mapping
              </Button>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Source'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
