"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { beautifyComment } from '@/lib/api';
import { CommentBeautifyRequest, CommentBeautifyResponse } from '@/types/alert';
import { Sparkles, Loader2, Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CommentBeautifierProps {
  content: string;
  onApply: (beautifiedContent: string) => void;
}

export function CommentBeautifier({ content, onApply }: CommentBeautifierProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [beautifyResponse, setBeautifyResponse] = useState<CommentBeautifyResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleBeautify = async () => {
    if (!content.trim()) return;

    setIsLoading(true);
    setError(null);
    setBeautifyResponse(null);

    try {
      const request: CommentBeautifyRequest = { content: content.trim() };
      const response = await beautifyComment(request);
      setBeautifyResponse(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to beautify comment');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApply = () => {
    if (beautifyResponse) {
      onApply(beautifyResponse.beautified_content);
      setIsOpen(false);
      setBeautifyResponse(null);
    }
  };

  const handleCancel = () => {
    setIsOpen(false);
    setBeautifyResponse(null);
    setError(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={!content.trim()}
          className="text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800 hover:bg-purple-50 dark:hover:bg-purple-950/20"
        >
          <Sparkles className="h-4 w-4 mr-1" />
          Beautify
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            AI Comment Beautifier
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Original Content */}
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">
              Original Comment
            </label>
            <Textarea
              value={content}
              readOnly
              rows={4}
              className="bg-muted/50 resize-none"
            />
          </div>

          {/* Beautify Button */}
          {!beautifyResponse && (
            <div className="flex justify-center">
              <Button
                onClick={handleBeautify}
                disabled={isLoading || !content.trim()}
                className="bg-primary hover:bg-primary/90"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Beautifying...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Beautify Comment
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-red-600">
                <X className="h-4 w-4" />
                <span className="font-medium">Error</span>
              </div>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          )}

          {/* Beautified Content */}
          {beautifyResponse && (
            <>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Beautified Comment
                </label>
                <Textarea
                  value={beautifyResponse.beautified_content}
                  readOnly
                  rows={6}
                  className="bg-green-50 border-green-200 resize-none"
                />
              </div>

              {/* Comparison */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-blue-600 mb-2">
                  <Check className="h-4 w-4" />
                  <span className="font-medium">Improvements Made</span>
                </div>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Enhanced clarity and professionalism</li>
                  <li>• Improved grammar and structure</li>
                  <li>• Maintained technical accuracy</li>
                  <li>• Better readability</li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleApply}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Check className="h-4 w-4 mr-2" />
                  Apply Changes
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
