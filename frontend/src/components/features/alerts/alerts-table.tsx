/**
 * AlertsTable Component
 * 
 * Displays a table of alerts with sorting, filtering, and pagination.
 * This is a restructured version of the original alerts-table component
 * with better separation of concerns and improved maintainability.
 */

"use client";

import { useState, useEffect } from 'react';
import { Alert } from '@/types/alert';
import { useAlerts } from '@/hooks/use-alerts';
import { DataTable } from '@/components/common/data-table';
import { LoadingSpinner } from '@/components/common/loading-spinner';
import { ErrorBoundary } from '@/components/common/error-boundary';

interface AlertsTableProps {
  onAlertSelect?: (alert: Alert) => void;
  onAlertsUpdate?: () => void;
  selectedAlerts?: Alert[];
  onSelectionChange?: (alerts: Alert[]) => void;
}

export function AlertsTable({
  onAlertSelect,
  onAlertsUpdate,
  selectedAlerts = [],
  onSelectionChange
}: AlertsTableProps) {
  const {
    alerts,
    loading,
    error,
    pagination,
    filters,
    sorting,
    fetchAlerts,
    updateFilters,
    updateSorting,
    updatePagination
  } = useAlerts();

  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  const handleAlertClick = (alert: Alert) => {
    onAlertSelect?.(alert);
  };

  const handleSelectionChange = (selectedIds: string[]) => {
    const selected = alerts.filter(alert => 
      selectedIds.includes(alert.id.toString())
    );
    onSelectionChange?.(selected);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Error loading alerts: {error}</p>
        <button 
          onClick={() => fetchAlerts()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  const columns = [
    {
      key: 'id',
      label: 'ID',
      sortable: true,
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true,
    },
    {
      key: 'severity',
      label: 'Severity',
      sortable: true,
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
    },
    {
      key: 'source',
      label: 'Source',
      sortable: true,
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
    },
  ];

  return (
    <ErrorBoundary>
      <DataTable
        data={alerts}
        columns={columns}
        pagination={pagination}
        sorting={sorting}
        filters={filters}
        selectedIds={selectedAlerts.map(a => a.id.toString())}
        onRowClick={handleAlertClick}
        onSelectionChange={handleSelectionChange}
        onSortingChange={updateSorting}
        onFiltersChange={updateFilters}
        onPaginationChange={updatePagination}
      />
    </ErrorBoundary>
  );
}

// TODO: Move the original alerts-table.tsx logic here
// This is a placeholder showing the new structure
