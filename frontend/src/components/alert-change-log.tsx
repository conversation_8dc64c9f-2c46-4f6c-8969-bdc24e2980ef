"use client";

import { useState, useEffect } from 'react';
import { AlertChangeLog } from '@/types/alert';
import { fetchAlertChangeLogs } from '@/lib/api';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  History,
  User,
  Calendar,
  Edit,
  Trash2,
  Plus,
  AlertTriangle,
  Clock,
  FileText,
  Tag,
  Flag,
  Target,
  UserCheck,
  Loader2,
  MessageSquare,
  Upload,
  Image,
  File
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface AlertChangeLogProps {
  alertId: number;
}

export function AlertChangeLogTab({ alertId }: AlertChangeLogProps) {
  const [changeLogs, setChangeLogs] = useState<AlertChangeLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadChangeLogs();
  }, [alertId]);

  const loadChangeLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchAlertChangeLogs(alertId);
      setChangeLogs(response.change_logs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load change logs');
    } finally {
      setLoading(false);
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'created':
        return <Plus className="h-4 w-4 text-green-600" />;
      case 'updated':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case 'soft_deleted':
        return <Trash2 className="h-4 w-4 text-orange-600" />;
      case 'hard_deleted':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'file_uploaded':
        return <Upload className="h-4 w-4 text-indigo-600" />;
      default:
        return <History className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getChangeTypeBadge = (changeType: string) => {
    const variants = {
      created: 'bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
      updated: 'bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      soft_deleted: 'bg-orange-50 dark:bg-orange-950/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800',
      hard_deleted: 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800',
      comment_added: 'bg-purple-50 dark:bg-purple-950/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800',
      file_uploaded: 'bg-indigo-50 dark:bg-indigo-950/20 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800',
    };

    const labels = {
      created: 'CREATED',
      updated: 'UPDATED',
      soft_deleted: 'DELETED',
      hard_deleted: 'PERMANENTLY DELETED',
      comment_added: 'COMMENT ADDED',
      file_uploaded: 'FILE UPLOADED',
    };

    return (
      <Badge
        variant="secondary"
        className={variants[changeType as keyof typeof variants] || 'bg-muted text-muted-foreground border-border'}
      >
        {labels[changeType as keyof typeof labels] || changeType.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getFieldIcon = (fieldName?: string) => {
    if (!fieldName) return <FileText className="h-3 w-3" />;
    
    switch (fieldName) {
      case 'title':
      case 'description':
        return <FileText className="h-3 w-3" />;
      case 'status':
        return <AlertTriangle className="h-3 w-3" />;
      case 'severity':
        return <Flag className="h-3 w-3" />;
      case 'priority':
        return <Flag className="h-3 w-3" />;
      case 'escalation_level':
        return <Target className="h-3 w-3" />;
      case 'assigned_analyst_id':
      case 'assigned_analyst_name':
        return <UserCheck className="h-3 w-3" />;
      case 'tags':
      case 'mitre_techniques':
        return <Tag className="h-3 w-3" />;
      case 'due_date':
      case 'sla_deadline':
        return <Calendar className="h-3 w-3" />;
      default:
        return <FileText className="h-3 w-3" />;
    }
  };

  const formatFieldName = (fieldName?: string) => {
    if (!fieldName) return '';
    
    const fieldLabels = {
      title: 'Title',
      description: 'Description',
      severity: 'Severity',
      status: 'Status',
      source: 'Source',
      assigned_analyst_id: 'Assigned Analyst ID',
      assigned_analyst_name: 'Assigned Analyst',
      priority: 'Priority',
      escalation_level: 'Escalation Level',
      due_date: 'Due Date',
      investigation_notes: 'Investigation Notes',
      tags: 'Tags',
      mitre_techniques: 'MITRE Techniques',
      sla_deadline: 'SLA Deadline'
    };
    
    return fieldLabels[fieldName as keyof typeof fieldLabels] || fieldName.replace('_', ' ');
  };

  const formatValue = (value?: string) => {
    if (!value || value === 'null' || value === 'None') return 'None';
    
    // Handle JSON arrays
    if (value.startsWith('[') && value.endsWith(']')) {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed.join(', ') : value;
      } catch {
        return value;
      }
    }
    
    // Truncate long values
    if (value.length > 100) {
      return value.substring(0, 100) + '...';
    }
    
    return value;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading change history...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8 text-red-600">
        <AlertTriangle className="h-5 w-5 mr-2" />
        <span>Error loading change logs: {error}</span>
      </div>
    );
  }

  if (changeLogs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-500">
        <History className="h-12 w-12 mb-4 text-gray-300" />
        <h3 className="text-lg font-medium mb-2">No Changes Yet</h3>
        <p className="text-sm text-center max-w-md">
          This alert hasn't been modified since creation. Changes will appear here when the alert is updated.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Change History</h3>
          <Badge variant="secondary">{changeLogs.length} changes</Badge>
        </div>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
        
        {/* Change log entries */}
        <div className="space-y-6">
          {changeLogs.map((log, index) => (
            <div key={log.id} className="relative flex gap-4">
              {/* Timeline dot */}
              <div className="relative z-10 flex items-center justify-center w-12 h-12 bg-white border-2 border-gray-200 rounded-full">
                {getChangeTypeIcon(log.change_type)}
              </div>
              
              {/* Change content */}
              <div className="flex-1 min-w-0">
                <Card className="shadow-sm">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getChangeTypeBadge(log.change_type)}
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <User className="h-4 w-4" />
                          <span className="font-medium">{log.changed_by}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Clock className="h-4 w-4" />
                        <span>{formatDistanceToNow(new Date(log.changed_at), { addSuffix: true })}</span>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    {/* Description */}
                    {log.description && (
                      <p className="text-sm text-gray-700 mb-3">{log.description}</p>
                    )}
                    
                    {/* Field change details */}
                    {log.field_name && (
                      <div className="bg-gray-50 rounded-md p-3">
                        <div className="flex items-center gap-2 mb-2">
                          {getFieldIcon(log.field_name)}
                          <span className="text-sm font-medium text-gray-700">
                            {formatFieldName(log.field_name)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          {log.old_value && (
                            <div>
                              <span className="text-gray-500 block mb-1">Previous value:</span>
                              <div className="bg-red-50 border border-red-200 rounded px-2 py-1 text-red-800 font-mono text-xs">
                                {formatValue(log.old_value)}
                              </div>
                            </div>
                          )}
                          
                          {log.new_value && (
                            <div>
                              <span className="text-gray-500 block mb-1">New value:</span>
                              <div className="bg-green-50 border border-green-200 rounded px-2 py-1 text-green-800 font-mono text-xs">
                                {formatValue(log.new_value)}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Metadata */}
                    <div className="flex items-center gap-4 mt-3 pt-3 border-t text-xs text-gray-500">
                      <span>Change #{log.id}</span>
                      <span>•</span>
                      <span>{new Date(log.changed_at).toLocaleString()}</span>
                      {log.ip_address && (
                        <>
                          <span>•</span>
                          <span>IP: {log.ip_address}</span>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
