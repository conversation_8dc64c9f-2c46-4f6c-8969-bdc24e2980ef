"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { Comment, FileResource } from '@/types/alert';
import { fetchAlertComments, fetchAlertResources, createComment, uploadFile, beautifyComment } from '@/lib/api';
import { AlertChangeLogTab } from './alert-change-log';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { AnalystMention } from './analyst-mention';
import { CommentDisplay } from './comment-display';
import { ImageThumbnail } from './image-thumbnail';
import MarkdownEditor from './MarkdownEditor';
import MarkdownRenderer from './MarkdownRenderer';
import { User, Clock, FileText, Image, Download, Upload, Paperclip, Loader2, X, History } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface EnhancedCommentsProps {
  alertId: number;
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}

function getFileIcon(fileType: string, isImage: boolean) {
  if (isImage) return <Image className="h-4 w-4" />;
  return <FileText className="h-4 w-4" />;
}

export function EnhancedComments({ alertId }: EnhancedCommentsProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [resources, setResources] = useState<FileResource[]>([]);
  const [newComment, setNewComment] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Simple loading states
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isLoadingResources, setIsLoadingResources] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const loadData = useCallback(async () => {
    if (!alertId || isNaN(alertId)) return;

    try {
      setIsLoadingComments(true);
      setIsLoadingResources(true);

      const [commentsData, resourcesData] = await Promise.all([
        fetchAlertComments(alertId),
        fetchAlertResources(alertId)
      ]);

      setComments(commentsData);
      setResources(resourcesData);
    } catch (error) {
      console.error('Failed to load data:', error);
      toast.error('Failed to load data', {
        action: {
          label: 'Retry',
          onClick: () => loadData(),
        },
      });
    } finally {
      setIsLoadingComments(false);
      setIsLoadingResources(false);
    }
  }, [alertId]);

  // Load data on mount and when alertId changes
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle image paste in markdown editor
  const handleImagePaste = async (file: File): Promise<string> => {
    try {
      const uploadedFile = await uploadFile(alertId, file);
      toast.success('Image uploaded successfully');

      // Reload resources to show the new image
      const resourcesData = await fetchAlertResources(alertId);
      setResources(resourcesData);

      // Return the URL for the markdown editor
      return uploadedFile.file_path;
    } catch (error) {
      toast.error('Failed to upload pasted image');
      throw error;
    }
  };

  // Handle AI beautification
  const handleBeautify = async (text: string): Promise<string> => {
    try {
      const response = await beautifyComment({ content: text });
      toast.success('Text beautified with AI');
      return response.beautified_content;
    } catch (error) {
      toast.error('Failed to beautify text');
      throw error;
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim() || isSubmitting) return;

    const commentData = {
      content: newComment,
      author: 'Current User' // In a real app, this would come from auth
    };

    try {
      setIsSubmitting(true);
      const createdComment = await createComment(alertId, commentData);

      // Optimistic update
      setComments([...comments, createdComment]);
      setNewComment('');
      toast.success('Comment posted successfully');
    } catch (error) {
      toast.error('Failed to post comment', {
        action: {
          label: 'Retry',
          onClick: () => handleSubmitComment(),
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || isUploading) return;

    try {
      setIsUploading(true);
      await uploadFile(alertId, file);
      toast.success('File uploaded successfully');

      // Reload resources to show the new file
      const resourcesData = await fetchAlertResources(alertId);
      setResources(resourcesData);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      toast.error('Failed to upload file', {
        action: {
          label: 'Retry',
          onClick: () => handleFileUpload(event),
        },
      });
    } finally {
      setIsUploading(false);
    }
  };



  const isLoading = isLoadingComments || isLoadingResources;

  if (isLoading && !comments.length && !resources.length) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading comments and resources...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Comments, Resources & Change Log
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="comments" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="comments" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Comments ({comments.length})
            </TabsTrigger>
            <TabsTrigger value="resources" className="flex items-center gap-2">
              <Paperclip className="h-4 w-4" />
              Resources ({resources.length})
            </TabsTrigger>
            <TabsTrigger value="changelog" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Change Log
            </TabsTrigger>
          </TabsList>

          <TabsContent value="comments" className="space-y-4 mt-4">
            {/* Existing Comments */}
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {comments.map((comment) => (
                <CommentDisplay key={comment.id} comment={comment} />
              ))}
              {comments.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  No comments yet. Be the first to add one!
                </div>
              )}
            </div>

            <Separator />

            {/* Add New Comment */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Add Comment</label>
              <MarkdownEditor
                value={newComment}
                onChange={setNewComment}
                placeholder="Write your comment here... Supports **bold**, *italic*, `code`, [links](url), lists, quotes, and more!"
                disabled={isSubmitting}
                onImagePaste={handleImagePaste}
                onSubmit={handleSubmitComment}
                onBeautify={handleBeautify}
              />
              <div className="flex items-center gap-3">
                <Button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Posting...
                    </>
                  ) : (
                    'Post Comment'
                  )}
                </Button>



                {/* File Upload Icon */}
                <div className="relative">
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isUploading}
                    accept="image/*,.pdf,.txt,.doc,.docx,.log"
                  />
                  <Button variant="outline" size="sm" disabled={isUploading}>
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                  </Button>
                </div>


              </div>
              <div className="text-xs text-gray-500">
                💡 Tip: Use the toolbar above to format your text with Markdown. You can also paste images directly from your clipboard (Ctrl+V/Cmd+V).
              </div>
            </div>
          </TabsContent>

          <TabsContent value="resources" className="space-y-4 mt-4">
            {/* File Resources */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {resources.map((resource) => (
                <div key={resource.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-shrink-0">
                    <ImageThumbnail resource={resource} size="small" allResources={resources} />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm truncate">{resource.filename}</span>
                      {resource.is_image && (
                        <Badge variant="secondary" className="text-xs">Image</Badge>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatFileSize(resource.file_size)} • Uploaded by {resource.uploaded_by} • {formatDate(resource.uploaded_at)}
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(resource.file_path, '_blank')}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
              ))}

              {resources.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  <Paperclip className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No files uploaded yet.</p>
                  <p className="text-sm">Upload files using the Comments tab or drag and drop here.</p>
                </div>
              )}
            </div>

            {/* Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <div className="relative">
                <input
                  type="file"
                  onChange={handleFileUpload}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  disabled={isUploading}
                  accept="image/*,.pdf,.txt,.doc,.docx,.log"
                  multiple
                />
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600">
                  {isUploading ? 'Uploading...' : 'Click to upload or drag and drop files here'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Supports images, PDFs, documents, and log files
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="changelog" className="mt-4">
            <AlertChangeLogTab alertId={alertId} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
