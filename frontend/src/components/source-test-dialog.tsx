"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Copy, Play } from 'lucide-react';
import { toast } from 'sonner';

interface WebhookSource {
  id: number;
  name: string;
  description?: string;
  webhook_url: string;
  webhook_secret: string;
  auth_type: 'custom_header' | 'bearer_token';
  auth_header_name?: string;
  auth_header_value?: string;
  auth_bearer_token?: string;
  json_mapping: Record<string, string>;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  last_used_at?: string;
  alert_count: number;
}

interface SourceTestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  source: WebhookSource;
}

interface TestResult {
  success: boolean;
  mapped_alert?: Record<string, any>;
  errors: string[];
  warnings: string[];
}

const samplePayloads = {
  basic: {
    title: "High CPU Usage Alert",
    description: "CPU usage has exceeded 90% for the last 5 minutes",
    severity: "high",
    source: "monitoring-system",
    timestamp: "2024-01-15T10:30:00Z"
  },
  nested: {
    alert: {
      title: "Database Connection Failed",
      message: "Unable to connect to primary database",
      level: "critical"
    },
    system: {
      name: "production-db",
      environment: "production"
    },
    metadata: {
      timestamp: "2024-01-15T10:30:00Z",
      region: "us-east-1"
    }
  },
  complex: {
    payload: {
      alert: {
        summary: "Memory leak detected",
        details: "Application memory usage has been steadily increasing",
        priority: "medium",
        tags: ["memory", "performance"]
      },
      source: {
        application: "web-server",
        instance: "web-01"
      },
      context: {
        created_at: "2024-01-15T10:30:00Z",
        environment: "staging"
      }
    }
  }
};

export function SourceTestDialog({ open, onOpenChange, source }: SourceTestDialogProps) {
  const [testPayload, setTestPayload] = useState(JSON.stringify(samplePayloads.basic, null, 2));
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleTest = async () => {
    try {
      setLoading(true);
      setTestResult(null);

      // Parse the test payload
      let parsedPayload;
      try {
        parsedPayload = JSON.parse(testPayload);
      } catch (error) {
        setTestResult({
          success: false,
          errors: ['Invalid JSON format in test payload'],
          warnings: []
        });
        return;
      }

      const response = await fetch(`/api/sources/${source.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source_id: source.id,
          test_payload: parsedPayload,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to test source');
      }

      const result: TestResult = await response.json();
      setTestResult(result);

      if (result.success) {
        toast.success('Source test completed successfully!');
      } else {
        toast.error('Source test failed');
      }

    } catch (error) {
      console.error('Error testing source:', error);
      setTestResult({
        success: false,
        errors: [error instanceof Error ? error.message : 'Failed to test source'],
        warnings: []
      });
      toast.error('Failed to test source');
    } finally {
      setLoading(false);
    }
  };

  const loadSamplePayload = (type: keyof typeof samplePayloads) => {
    setTestPayload(JSON.stringify(samplePayloads[type], null, 2));
    setTestResult(null);
  };

  const copyWebhookUrl = async () => {
    try {
      await navigator.clipboard.writeText(source.webhook_url);
      toast.success('Webhook URL copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy webhook URL');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="large-modal-content overflow-y-auto p-8">
        <DialogHeader>
          <DialogTitle>Test Source: {source.name}</DialogTitle>
          <DialogDescription>
            Test your webhook source with sample payloads to verify the JSON mapping configuration.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Test Configuration */}
          <div className="space-y-4">
            {/* Webhook URL */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Webhook URL</CardTitle>
                <CardDescription>
                  Use this URL to send test requests from external systems
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <code className="flex-1 text-sm bg-gray-100 px-3 py-2 rounded border">
                    {source.webhook_url}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyWebhookUrl}
                    className="flex items-center gap-1"
                  >
                    <Copy className="h-4 w-4" />
                    Copy
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Sample Payloads */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Sample Payloads</CardTitle>
                <CardDescription>
                  Load a sample payload to test your mapping configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadSamplePayload('basic')}
                  >
                    Basic
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadSamplePayload('nested')}
                  >
                    Nested
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadSamplePayload('complex')}
                  >
                    Complex
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Test Payload */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Payload</CardTitle>
                <CardDescription>
                  Enter or modify the JSON payload to test
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="test_payload">JSON Payload</Label>
                  <Textarea
                    id="test_payload"
                    value={testPayload}
                    onChange={(e) => setTestPayload(e.target.value)}
                    placeholder="Enter JSON payload..."
                    className="font-mono text-sm min-h-[200px]"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Test Button */}
            <Button
              onClick={handleTest}
              disabled={loading || !testPayload.trim()}
              className="w-full flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {loading ? 'Testing...' : 'Test Source'}
            </Button>
          </div>

          {/* Right Column - Test Results */}
          <div className="space-y-4">
            {/* Current Mapping */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Mapping Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(source.json_mapping).map(([field, path]) => (
                    <div key={field} className="flex items-center justify-between text-sm">
                      <span className="font-medium">{field}:</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">{path}</code>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Test Results */}
            {testResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    Test Results
                    {testResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Status */}
                  <div>
                    <Badge variant={testResult.success ? "default" : "destructive"}>
                      {testResult.success ? "Success" : "Failed"}
                    </Badge>
                  </div>

                  {/* Errors */}
                  {testResult.errors.length > 0 && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="space-y-1">
                          {testResult.errors.map((error, index) => (
                            <div key={index}>{error}</div>
                          ))}
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Warnings */}
                  {testResult.warnings.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="space-y-1">
                          {testResult.warnings.map((warning, index) => (
                            <div key={index}>{warning}</div>
                          ))}
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Mapped Alert */}
                  {testResult.mapped_alert && (
                    <div>
                      <Label className="text-sm font-medium">Mapped Alert Data:</Label>
                      <pre className="mt-2 bg-gray-100 p-3 rounded text-xs overflow-auto max-h-[300px]">
                        {JSON.stringify(testResult.mapped_alert, null, 2)}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Source Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Source Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge variant={source.is_active ? "default" : "secondary"}>
                    {source.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Authentication:</span>
                  <span>{source.auth_type === 'custom_header' ? 'Custom Header' : 'Bearer Token'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Alerts Created:</span>
                  <span>{source.alert_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Used:</span>
                  <span>
                    {source.last_used_at
                      ? new Date(source.last_used_at).toLocaleDateString()
                      : 'Never'
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
