"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { deleteAlert } from '@/lib/api';
import { Alert } from '@/types/alert';
import { Trash2, Loader2, AlertTriangle, Archive } from 'lucide-react';
import { toast } from 'sonner';

interface DeleteAlertDialogProps {
  alert: Alert;
  onAlertDeleted?: (alertId: number) => void;
  trigger?: React.ReactNode;
}

export function DeleteAlertDialog({ alert, onAlertDeleted, trigger }: DeleteAlertDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hardDelete, setHardDelete] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  const expectedConfirmText = `DELETE-${alert.id}`;
  const isConfirmValid = confirmText === expectedConfirmText;

  const handleDelete = async () => {
    if (!isConfirmValid) {
      toast.error('Please enter the correct confirmation text');
      return;
    }

    try {
      setIsDeleting(true);
      
      await deleteAlert(alert.id, hardDelete);
      
      setIsOpen(false);
      setConfirmText('');
      setHardDelete(false);
      
      const deleteType = hardDelete ? 'permanently deleted' : 'moved to trash';
      toast.success(`Alert #${alert.id} ${deleteType} successfully`);
      
      // Call callback if provided
      if (onAlertDeleted) {
        onAlertDeleted(alert.id);
      }
    } catch (error) {
      console.error('Failed to delete alert:', error);
      toast.error('Failed to delete alert');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setConfirmText('');
      setHardDelete(false);
    }
    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="destructive" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Alert
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Alert #{alert.id}
          </DialogTitle>
          <DialogDescription>
            This action will {hardDelete ? 'permanently delete' : 'move to trash'} the alert and all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Alert Summary */}
          <div className="p-3 bg-gray-50 rounded-md border">
            <h4 className="font-medium text-sm text-gray-900 mb-1">Alert Details</h4>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Title:</span> {alert.title}
            </p>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Severity:</span> {alert.severity}
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Status:</span> {alert.status}
            </p>
          </div>

          {/* Delete Type Selection */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hard-delete"
                checked={hardDelete}
                onCheckedChange={(checked) => setHardDelete(checked as boolean)}
              />
              <label
                htmlFor="hard-delete"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Permanently delete (cannot be undone)
              </label>
            </div>
            
            <div className="text-xs text-gray-500 ml-6">
              {hardDelete ? (
                <div className="flex items-center gap-1 text-red-600">
                  <Trash2 className="h-3 w-3" />
                  Alert will be permanently removed from the database
                </div>
              ) : (
                <div className="flex items-center gap-1 text-orange-600">
                  <Archive className="h-3 w-3" />
                  Alert will be moved to trash and can be restored later
                </div>
              )}
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-900">
              Type <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">{expectedConfirmText}</code> to confirm:
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={expectedConfirmText}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>

          {/* Warning Message */}
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <p className="font-medium mb-1">Warning:</p>
                <ul className="text-xs space-y-1">
                  <li>• All comments and AI findings will be {hardDelete ? 'permanently deleted' : 'archived'}</li>
                  <li>• All uploaded files will be {hardDelete ? 'permanently deleted' : 'archived'}</li>
                  <li>• Chat history will be {hardDelete ? 'permanently deleted' : 'archived'}</li>
                  {hardDelete && <li className="text-red-800 font-medium">• This action cannot be undone</li>}
                </ul>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              disabled={isDeleting || !isConfirmValid}
              variant="destructive"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  {hardDelete ? (
                    <Trash2 className="h-4 w-4 mr-2" />
                  ) : (
                    <Archive className="h-4 w-4 mr-2" />
                  )}
                  {hardDelete ? 'Permanently Delete' : 'Move to Trash'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
