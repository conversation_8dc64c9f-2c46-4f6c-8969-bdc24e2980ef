"use client";

import { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { FileResource } from '@/types/alert';
import { Image as ImageIcon, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImageThumbnailProps {
  resource: FileResource;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  allResources?: FileResource[]; // For gallery navigation
}

export function ImageThumbnail({ resource, size = 'medium', className, allResources }: ImageThumbnailProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Filter to only image resources for gallery
  const imageResources = allResources?.filter(r => r.is_image) || [resource];
  const currentResource = imageResources[currentImageIndex] || resource;

  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-16 h-16',
    large: 'w-24 h-24'
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleOpenGallery = () => {
    // Find the index of the current resource in the image resources
    const index = imageResources.findIndex(r => r.id === resource.id);
    setCurrentImageIndex(index >= 0 ? index : 0);
    setIsOpen(true);
  };

  const navigateGallery = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentImageIndex(prev => prev > 0 ? prev - 1 : imageResources.length - 1);
    } else {
      setCurrentImageIndex(prev => prev < imageResources.length - 1 ? prev + 1 : 0);
    }
    // Reset image states for new image
    setImageError(false);
    setImageLoaded(false);
  };

  if (!resource.is_image) {
    return (
      <div className={cn(
        'flex items-center justify-center bg-gray-100 rounded border',
        sizeClasses[size],
        className
      )}>
        <ImageIcon className="h-4 w-4 text-gray-400" />
      </div>
    );
  }

  return (
    <>
      <div
        className={cn(
          'relative cursor-pointer rounded border overflow-hidden hover:opacity-80 transition-opacity bg-gray-50',
          sizeClasses[size],
          className
        )}
        onClick={handleOpenGallery}
        title={`Click to view ${resource.filename}`}
      >
        {!imageError ? (
          <>
            {!imageLoaded && (
              <div className="w-full h-full flex items-center justify-center bg-gray-100 absolute inset-0">
                <div className="animate-pulse bg-gray-200 w-full h-full rounded"></div>
              </div>
            )}
            <img
              src={resource.file_path}
              alt={resource.filename}
              className={cn(
                "w-full h-full object-cover transition-opacity duration-300",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              onError={handleImageError}
              onLoad={handleImageLoad}
            />
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-100">
            <ImageIcon className="h-4 w-4 text-gray-400" />
          </div>
        )}
        
        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 hover:opacity-100 transition-opacity">
            <div className="bg-white bg-opacity-90 rounded-full p-1">
              <ImageIcon className="h-3 w-3 text-gray-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Full-size image modal with gallery */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogTitle className="sr-only">
            Image Preview: {currentResource.filename}
          </DialogTitle>
          <div className="relative">
            {/* Close button - positioned outside image area */}
            <button
              onClick={() => setIsOpen(false)}
              className="absolute top-2 right-2 z-20 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-2 transition-all"
            >
              <X className="h-4 w-4" />
            </button>

            <div className="p-4">
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{currentResource.filename}</h3>
                    <p className="text-sm text-gray-500">
                      {currentResource.file_type} • {formatFileSize(currentResource.file_size)} •
                      Uploaded by {currentResource.uploaded_by}
                    </p>
                  </div>
                  {imageResources.length > 1 && (
                    <div className="text-sm text-gray-500">
                      {currentImageIndex + 1} of {imageResources.length}
                    </div>
                  )}
                </div>
              </div>

              {/* Image container with navigation */}
              <div className="relative flex justify-center">
                {/* Navigation arrows positioned outside the image */}
                {imageResources.length > 1 && (
                  <>
                    <button
                      onClick={() => navigateGallery('prev')}
                      className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-12 z-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                      title="Previous image"
                    >
                      <ChevronLeft className="h-6 w-6" />
                    </button>
                    <button
                      onClick={() => navigateGallery('next')}
                      className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 z-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                      title="Next image"
                    >
                      <ChevronRight className="h-6 w-6" />
                    </button>
                  </>
                )}

                {!imageError ? (
                  <img
                    key={currentResource.id} // Force re-render when image changes
                    src={currentResource.file_path}
                    alt={currentResource.filename}
                    className="max-w-full max-h-[70vh] object-contain rounded"
                    onError={handleImageError}
                    onLoad={handleImageLoad}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                    <ImageIcon className="h-16 w-16 mb-4" />
                    <p>Unable to load image</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
