"use client";

import { Comment } from '@/types/alert';
import { Clock } from 'lucide-react';
import MarkdownRenderer from './MarkdownRenderer';

interface CommentDisplayProps {
  comment: Comment;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}

function renderCommentWithMentions(content: string) {
  const mentionRegex = /@(\w+(?:\.\w+)*)/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    // Add text before mention
    if (match.index > lastIndex) {
      parts.push(content.substring(lastIndex, match.index));
    }
    
    // Add styled mention
    const username = match[1];
    parts.push(
      <span
        key={match.index}
        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-950/30 cursor-pointer transition-colors border border-blue-200 dark:border-blue-800"
        title={`@${username}`}
      >
        @{username}
      </span>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < content.length) {
    parts.push(content.substring(lastIndex));
  }
  
  return parts.length > 0 ? parts : [content];
}

function renderCommentWithImages(content: string) {
  // Look for image placeholders like [Image: filename.png]
  const imagePlaceholderRegex = /\[Image: ([^\]]+)\]/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = imagePlaceholderRegex.exec(content)) !== null) {
    // Add text before image placeholder
    if (match.index > lastIndex) {
      const textPart = content.substring(lastIndex, match.index);
      parts.push(renderCommentWithMentions(textPart));
    }
    
    // Add image thumbnail placeholder
    const filename = match[1];
    parts.push(
      <span 
        key={match.index}
        className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded text-xs text-gray-600 border"
      >
        <span>📷</span>
        <span>{filename}</span>
      </span>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < content.length) {
    const remainingText = content.substring(lastIndex);
    parts.push(renderCommentWithMentions(remainingText));
  }
  
  return parts.length > 0 ? parts : renderCommentWithMentions(content);
}

export function CommentDisplay({ comment }: CommentDisplayProps) {
  return (
    <div className="border-l-4 border-blue-200 pl-4 py-2">
      <div className="flex items-center justify-between mb-2">
        <span className="font-medium text-sm">{comment.author}</span>
        <span className="text-xs text-gray-500 flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {formatDate(comment.created_at)}
        </span>
      </div>
      <div className="text-sm">
        <MarkdownRenderer content={comment.content} />
      </div>
    </div>
  );
}
