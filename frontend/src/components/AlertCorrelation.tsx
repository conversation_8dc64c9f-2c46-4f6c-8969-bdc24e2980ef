import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Link2, Clock, Users, Server, Zap, AlertTriangle, Eye, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface TimelineEvent {
  timestamp: string;
  alert_id: number;
  title: string;
  severity: string;
}

interface Correlation {
  correlation_type: string;
  confidence: number;
  related_alert_ids: number[];
  description: string;
  timeline: TimelineEvent[];
  indicators: string[];
  risk_score: number;
}

interface CorrelationData {
  alert_id: number;
  time_window_hours: number;
  correlations: Correlation[];
  total_correlations: number;
}

interface AlertCorrelationProps {
  alertId: number;
}

const AlertCorrelation: React.FC<AlertCorrelationProps> = ({ alertId }) => {
  const [correlationData, setCorrelationData] = useState<CorrelationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeWindow, setTimeWindow] = useState(24);
  const router = useRouter();

  const fetchCorrelations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8001/api/alerts/${alertId}/correlations?time_window_hours=${timeWindow}`);
      if (!response.ok) {
        throw new Error('Failed to fetch correlations');
      }
      const data = await response.json();
      setCorrelationData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [alertId, timeWindow]);

  useEffect(() => {
    fetchCorrelations();
  }, [fetchCorrelations]);

  const getCorrelationIcon = (type: string) => {
    switch (type) {
      case 'same_source_ip': return <Server className="w-4 h-4" />;
      case 'same_user': return <Users className="w-4 h-4" />;
      case 'same_asset': return <Server className="w-4 h-4" />;
      case 'temporal_proximity': return <Clock className="w-4 h-4" />;
      case 'attack_chain': return <Link2 className="w-4 h-4" />;
      case 'campaign': return <Zap className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getCorrelationColor = (type: string) => {
    switch (type) {
      case 'same_source_ip': return 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800';
      case 'same_user': return 'bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800';
      case 'same_asset': return 'bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800';
      case 'temporal_proximity': return 'bg-yellow-50 dark:bg-yellow-950/20 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800';
      case 'attack_chain': return 'bg-purple-50 dark:bg-purple-950/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800';
      case 'campaign': return 'bg-orange-50 dark:bg-orange-950/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 dark:text-green-400';
    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 0.7) return 'bg-red-100 text-red-800';
    if (score >= 0.4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-muted-foreground';
    }
  };

  const formatCorrelationType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const navigateToAlert = (alertId: number) => {
    router.push(`/alerts/${alertId}`);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link2 className="w-5 h-5" />
            Alert Correlations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-8">
        <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
        <p>Failed to load correlations: {error}</p>
      </div>
    );
  }

  if (!correlationData) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Link2 className="w-5 h-5" />
            Alert Correlations
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Time Window:</span>
            <select
              value={timeWindow}
              onChange={(e) => setTimeWindow(Number(e.target.value))}
              className="text-sm border rounded px-2 py-1"
            >
              <option value={6}>6 hours</option>
              <option value={12}>12 hours</option>
              <option value={24}>24 hours</option>
              <option value={48}>48 hours</option>
              <option value={72}>72 hours</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {correlationData.total_correlations === 0 ? (
          <Alert>
            <Eye className="h-4 w-4" />
            <AlertDescription>
              No correlations found within the {timeWindow}-hour time window. This alert appears to be isolated.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{correlationData.total_correlations}</div>
                <div className="text-xs text-muted-foreground">Total Correlations</div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {correlationData.correlations.filter(c => c.confidence >= 0.8).length}
                </div>
                <div className="text-xs text-muted-foreground">High Confidence</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.max(...correlationData.correlations.map(c => c.related_alert_ids.length))}
                </div>
                <div className="text-xs text-gray-500">Max Related Alerts</div>
              </div>
            </div>

            {/* Correlations */}
            {correlationData.correlations.map((correlation, index) => (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCorrelationIcon(correlation.correlation_type)}
                      <h3 className="font-semibold">{formatCorrelationType(correlation.correlation_type)}</h3>
                      <Badge className={getCorrelationColor(correlation.correlation_type)}>
                        {correlation.related_alert_ids.length} alerts
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getConfidenceColor(correlation.confidence)}>
                        {Math.round(correlation.confidence * 100)}% confidence
                      </Badge>
                      <Badge className={getRiskScoreColor(correlation.risk_score)}>
                        Risk: {Math.round(correlation.risk_score * 100)}%
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-gray-600 mb-4">{correlation.description}</p>
                  
                  {/* Indicators */}
                  {correlation.indicators.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                        Common Indicators
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {correlation.indicators.map((indicator, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {indicator}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Timeline */}
                  <div className="mb-4">
                    <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
                      Timeline ({correlation.timeline.length} events)
                    </h4>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {correlation.timeline.map((event, idx) => (
                        <div key={idx} className="flex items-center justify-between p-2 bg-muted/50 rounded text-xs">
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500">{formatTimestamp(event.timestamp)}</span>
                            <Button
                              variant="link"
                              size="sm"
                              className="h-auto p-0 text-blue-600 hover:text-blue-800"
                              onClick={() => navigateToAlert(event.alert_id)}
                            >
                              #{event.alert_id}
                            </Button>
                            <span className="font-medium">{event.title}</span>
                          </div>
                          <Badge variant="outline" className={getSeverityColor(event.severity)}>
                            {event.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Related Alerts */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      Related Alert IDs: {correlation.related_alert_ids.join(', ')}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        // Navigate to first related alert
                        if (correlation.related_alert_ids.length > 0) {
                          navigateToAlert(correlation.related_alert_ids[0]);
                        }
                      }}
                    >
                      View Related <ChevronRight className="w-3 h-3 ml-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AlertCorrelation;
