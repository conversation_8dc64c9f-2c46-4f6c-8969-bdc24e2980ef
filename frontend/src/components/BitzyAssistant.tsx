"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { X, Send, Minimize2, Maximize2, Expand, History, Plus, Trash2, Eye, <PERSON>Off, <PERSON><PERSON>, Check, Edit3, Save, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { chatWithAI, generalChatWithAI, cancelRequest } from '@/lib/api';
import { toast } from 'sonner';
import { useBitzy } from '@/contexts/BitzyContext';
import { useChatSession } from '@/contexts/ChatSessionContext';
import { chatService, ChatMessage as ChatMessageType, EditMessageRequest } from '@/services/chatService';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';

interface BitzyMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  dbMessageId?: number; // Database message ID for editing
  suggestions?: QuickAction[]; // Smart suggestions for AI messages only
  context?: MessageContext; // Additional context for the message
}

interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: string;
  description?: string;
}

interface MessageContext {
  detectedEntities?: string[]; // IPs, domains, hashes, etc.
  suggestedActions?: string[];
  confidence?: number;
}

interface BitzyAssistantProps {
  className?: string;
}

type BitzyState = 'idle' | 'thinking' | 'happy' | 'confused' | 'speaking';

// Constants for better maintainability
const BITZY_STATE_TIMEOUT = 2000;
const COPY_FEEDBACK_TIMEOUT = 2000;
const SCROLL_BEHAVIOR: ScrollBehavior = 'smooth';
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

// Smart action detection patterns
const ENTITY_PATTERNS = {
  ip: /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
  domain: /\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}\b/g,
  hash: /\b[a-fA-F0-9]{32,64}\b/g,
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  url: /https?:\/\/[^\s]+/g,
};

// Generate smart actions based on message content
const generateSmartActions = (content: string, isAiMessage: boolean, pageContext?: any): QuickAction[] => {
  const actions: QuickAction[] = [];
  const lowerContent = content.toLowerCase();

  // Detect entities
  const ips = content.match(ENTITY_PATTERNS.ip) || [];
  const domains = content.match(ENTITY_PATTERNS.domain) || [];
  const hashes = content.match(ENTITY_PATTERNS.hash) || [];

  // Add documentation suggestions based on content
  if (lowerContent.includes('source') || lowerContent.includes('webhook')) {
    actions.push({
      id: 'docs-sources',
      label: 'Sources Documentation',
      icon: '📖',
      action: 'open-docs:/docs?doc=sources',
      description: 'Learn about managing webhook sources'
    });
  }

  if (lowerContent.includes('alert') || lowerContent.includes('investigation') || lowerContent.includes('bitzy') || lowerContent.includes('ai')) {
    actions.push({
      id: 'docs-bitzy',
      label: 'Alert Investigation Guide',
      icon: '🔍',
      action: 'open-docs:/docs?doc=bitzy',
      description: 'AI assistant and investigation help'
    });
  }

  if (lowerContent.includes('overview') || lowerContent.includes('getting started') || lowerContent.includes('introduction')) {
    actions.push({
      id: 'docs-overview',
      label: 'Platform Overview',
      icon: '📋',
      action: 'open-docs:/docs?doc=overview',
      description: 'Learn about AlertAI platform'
    });
  }

  if (lowerContent.includes('quick start') || lowerContent.includes('setup') || lowerContent.includes('getting started')) {
    actions.push({
      id: 'docs-quickstart',
      label: 'Quick Start Guide',
      icon: '🚀',
      action: 'open-docs:/docs?doc=quick-start',
      description: 'Get started with AlertAI'
    });
  }

  // Add context-specific documentation suggestions
  if (pageContext?.pageType === 'sources') {
    actions.push({
      id: 'help-sources',
      label: 'Sources Help',
      icon: '📖',
      action: 'help-sources',
      description: 'Get help with source management'
    });
  }

  if (pageContext?.pageType === 'alerts-list' || pageContext?.pageType === 'alert-detail') {
    actions.push({
      id: 'help-alerts',
      label: 'Alert Investigation Guide',
      icon: '🔍',
      action: 'help-alerts',
      description: 'Learn alert investigation best practices'
    });
  }

  // Add IP investigation actions
  if (ips.length > 0) {
    actions.push({
      id: 'investigate-ip',
      label: `Investigate IP${ips.length > 1 ? 's' : ''} (${ips.length})`,
      icon: '🔍',
      action: `investigate-ip:${ips.join(',')}`,
      description: `Investigate ${ips.slice(0, 3).join(', ')}${ips.length > 3 ? '...' : ''}`
    });
  }

  // Add domain investigation actions
  if (domains.length > 0) {
    actions.push({
      id: 'investigate-domain',
      label: `Check Domain${domains.length > 1 ? 's' : ''} (${domains.length})`,
      icon: '🌐',
      action: `investigate-domain:${domains.join(',')}`,
      description: `Investigate ${domains.slice(0, 3).join(', ')}${domains.length > 3 ? '...' : ''}`
    });
  }

  // Add hash investigation actions
  if (hashes.length > 0) {
    actions.push({
      id: 'investigate-hash',
      label: `Analyze Hash${hashes.length > 1 ? 'es' : ''} (${hashes.length})`,
      icon: '🔐',
      action: `investigate-hash:${hashes.join(',')}`,
      description: `Analyze ${hashes.slice(0, 2).join(', ')}${hashes.length > 2 ? '...' : ''}`
    });
  }

  // Context-based actions for AI responses
  if (isAiMessage) {
    if (lowerContent.includes('script') || lowerContent.includes('code')) {
      actions.push({
        id: 'run-script',
        label: 'Run Script',
        icon: '▶️',
        action: 'run-script',
        description: 'Execute the provided script safely'
      });
    }

    if (lowerContent.includes('investigate') || lowerContent.includes('check')) {
      actions.push({
        id: 'create-playbook',
        label: 'Create Playbook',
        icon: '📋',
        action: 'create-playbook',
        description: 'Turn this into a reusable playbook'
      });
    }

    if (lowerContent.includes('alert') || lowerContent.includes('incident')) {
      actions.push({
        id: 'create-ticket',
        label: 'Create Ticket',
        icon: '🎫',
        action: 'create-ticket',
        description: 'Create a JIRA/ServiceNow ticket'
      });
    }
  }

  // Only generate actions for AI messages
  if (!isAiMessage) {
    return [];
  }

  return actions.slice(0, 4); // Limit to 4 actions max
};

// Error boundary component for Bitzy
class BitzyErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Bitzy Error Boundary caught an error:', error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="fixed bottom-4 left-4 sm:bottom-6 sm:left-6 z-[9999]">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 max-w-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 rounded-full bg-destructive/20 flex items-center justify-center">
                <span className="text-destructive">⚠️</span>
              </div>
              <h3 className="font-medium text-destructive">Bitzy Error</h3>
            </div>
            <p className="text-sm text-destructive/80 mb-3">
              Something went wrong with Bitzy. Please refresh the page to try again.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="text-sm bg-destructive text-destructive-foreground px-3 py-1 rounded hover:bg-destructive/90 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const BitzyAssistantCore: React.FC<BitzyAssistantProps> = React.memo(({
  className
}) => {
  const { currentAlertId, currentAlertTitle, pageContext, searchDocs, getContextualHelp } = useBitzy();
  const {
    currentSession,
    chatSessions,
    isIncognitoMode,
    startNewSession,
    loadSession,
    endCurrentSession,
    deleteSession,
    toggleIncognitoMode,
    setCurrentSession,
    handleNewSessionFromMessage
  } = useChatSession();

  // State management with better organization
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [messages, setMessages] = useState<BitzyMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [bitzyState, setBitzyState] = useState<BitzyState>('idle');
  const [currentSessionId, setCurrentSessionId] = useState<number | undefined>();
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [copiedCodeId, setCopiedCodeId] = useState<string | null>(null);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState<string>('');
  const [currentRequestId, setCurrentRequestId] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Refs for DOM manipulation and cleanup
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const bitzyStateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const copyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup function for timeouts and abort controllers
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (bitzyStateTimeoutRef.current) {
      clearTimeout(bitzyStateTimeoutRef.current);
      bitzyStateTimeoutRef.current = null;
    }
    if (copyTimeoutRef.current) {
      clearTimeout(copyTimeoutRef.current);
      copyTimeoutRef.current = null;
    }
    if (currentRequestId) {
      cancelRequest(currentRequestId);
      setCurrentRequestId(null);
    }
  }, [currentRequestId]);

  // Mount state for portal rendering
  useEffect(() => {
    setIsMounted(true);
    return () => {
      setIsMounted(false);
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: SCROLL_BEHAVIOR });
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Simple focus management - only focus when expanding
  useEffect(() => {
    if (isExpanded && !isMinimized && textareaRef.current) {
      const timer = setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isExpanded, isMinimized]);

  // Track session changes and clear messages when switching sessions
  useEffect(() => {
    const newSessionId = currentSession?.id;

    // Only clear messages if we're switching to a different existing session
    // Don't clear when creating a new session from current conversation
    if (currentSessionId !== newSessionId && currentSessionId !== undefined && newSessionId !== undefined) {
      setMessages([]);
    }
    setCurrentSessionId(newSessionId);
  }, [currentSession?.id, currentSessionId]);

  // Bitzy state management with proper cleanup
  useEffect(() => {
    // Clear any existing timeout
    if (bitzyStateTimeoutRef.current) {
      clearTimeout(bitzyStateTimeoutRef.current);
      bitzyStateTimeoutRef.current = null;
    }

    if (isLoading) {
      setBitzyState('thinking');
    } else if (messages.length > 0 && !messages[messages.length - 1].isUser) {
      setBitzyState('speaking');
      bitzyStateTimeoutRef.current = setTimeout(() => {
        setBitzyState('happy');
        bitzyStateTimeoutRef.current = null;
      }, BITZY_STATE_TIMEOUT);
    } else {
      setBitzyState('idle');
    }
  }, [isLoading, messages]);

  // Memoized functions for better performance
  const getBitzyImageSrc = useMemo(() => (state: BitzyState) => {
    switch (state) {
      case 'thinking':
        return '/bitzy-thinking.svg';
      case 'happy':
        return '/bitzy-happy.svg';
      case 'confused':
        return '/bitzy-confused.svg';
      case 'speaking':
        return '/bitzy-speaking.svg';
      default:
        return '/bitzy-idle.svg';
    }
  }, []);

  const getBitzyAnimation = useMemo(() => (state: BitzyState) => {
    switch (state) {
      case 'thinking':
        return 'animate-pulse';
      case 'speaking':
        return 'animate-bounce';
      default:
        return '';
    }
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: BitzyMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = inputValue.trim();
    setInputValue('');
    setIsLoading(true);
    setError(null);

    // Generate unique request ID for cancellation
    const requestId = `chat-${Date.now()}`;
    setCurrentRequestId(requestId);

    try {
      let response;

      // Prepare chat request
      const chatRequest = {
        message: messageText,
        user_id: 'current_user',
        session_id: currentSession?.id,
        page_context: pageContext,
        is_incognito: isIncognitoMode
      };

      if (currentAlertId) {
        // Use alert-specific chat when an alert is selected
        response = await chatService.sendAlertMessage(currentAlertId, chatRequest);
      } else {
        // Use general chat when no alert is selected
        response = await chatService.sendMessage(chatRequest);
      }

      // If this created a new session, update the session context without clearing messages
      if (response.is_new_session && response.session_id) {
        const newSession = {
          id: response.session_id,
          user_id: 'current_user',
          alert_id: currentAlertId,
          title: currentAlertTitle ? `Chat about ${currentAlertTitle}` : 'General Chat',
          is_incognito: isIncognitoMode,
          created_at: new Date().toISOString(),
          is_active: true,
          message_count: 1
        };
        setCurrentSession(newSession);
      }

      const aiMessage: BitzyMessage = {
        id: response.message_id.toString(),
        content: response.response,
        isUser: false,
        timestamp: new Date(response.created_at),
        dbMessageId: response.message_id,
        suggestions: generateSmartActions(response.response, true, pageContext),
      };

      // Update the user message with the database message ID
      setMessages(prev => {
        const updatedMessages = [...prev];
        const userMessageIndex = updatedMessages.findIndex(msg => msg.id === userMessage.id);
        if (userMessageIndex !== -1) {
          updatedMessages[userMessageIndex] = {
            ...updatedMessages[userMessageIndex],
            dbMessageId: response.message_id, // Same message ID for both user and AI parts
          };
        }
        return [...updatedMessages, aiMessage];
      });
      setBitzyState('speaking');
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error('Error sending message to Bitzy:', error);

      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(errorMsg);

      // Show different error messages based on error type
      let userFriendlyMessage = "Sorry, I'm having trouble connecting right now. Please try again! 😕";
      if (errorMsg.includes('timeout') || errorMsg.includes('cancelled')) {
        userFriendlyMessage = "The request timed out. Please try again with a shorter message. ⏱️";
      } else if (errorMsg.includes('network') || errorMsg.includes('fetch')) {
        userFriendlyMessage = "Network connection issue. Please check your internet and try again. 🌐";
      }

      toast.error(`Failed to send message: ${errorMsg}`);

      const errorMessage: BitzyMessage = {
        id: (Date.now() + 1).toString(),
        content: userFriendlyMessage,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
      setBitzyState('confused');
    } finally {
      setIsLoading(false);
      setCurrentRequestId(null);
    }
  }, [inputValue, isLoading, currentAlertId, currentSession?.id, pageContext, isIncognitoMode, currentAlertTitle, setCurrentSession]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      if (isFullScreen) {
        setIsFullScreen(false);
      } else if (isExpanded) {
        setIsExpanded(false);
      }
    }
  }, [handleSendMessage, isFullScreen, isExpanded]);

  // Global keyboard shortcuts and modal bypass
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to open/close Bitzy
      if ((e.ctrlKey || e.metaKey) && e.key === 'k' && !isExpanded) {
        e.preventDefault();
        setIsExpanded(true);
      }
      // Escape to close when expanded
      else if (e.key === 'Escape' && isExpanded) {
        e.preventDefault();
        if (isFullScreen) {
          setIsFullScreen(false);
        } else {
          setIsExpanded(false);
        }
      }
    };

    // Simple keyboard handler for Bitzy functionality only
    document.addEventListener('keydown', handleGlobalKeyDown);

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [isExpanded, isFullScreen]);

  // Memoized placeholder text for better performance
  const placeholderText = useMemo(() => {
    if (currentAlertTitle) {
      return "Ask Bitzy about this alert...";
    } else if (pageContext.pageType === 'alerts-list') {
      return "Ask Bitzy about these alerts...";
    } else {
      return "Ask Bitzy anything about cybersecurity...";
    }
  }, [currentAlertTitle, pageContext.pageType]);

  // Memoized welcome message for better performance
  const welcomeMessage = useMemo(() => {
    if (currentAlertTitle) {
      return `I can help you analyze "${currentAlertTitle}". I have access to all AlertAI documentation and can guide you through investigation steps.`;
    } else if (pageContext.pageType === 'alerts-list') {
      return "I can see you're viewing the alerts dashboard. I can help you understand these alerts, prioritize them, or guide you through AlertAI features. I have access to all documentation!";
    } else if (pageContext.pageType === 'sources') {
      return "I can see you're managing sources. I can help you create new sources, configure JSON mapping, set up authentication, or troubleshoot webhook issues. Ask me anything!";
    } else if (pageContext.pageType === 'docs') {
      return "I can see you're browsing documentation. I have access to all AlertAI docs and can help you find specific information, explain features, or guide you through setup steps.";
    } else if (pageContext.pageType === 'dashboard') {
      return "I can help you with cybersecurity questions and guide you through the AlertAI platform. I have access to all documentation and can explain any feature.";
    } else {
      return "I can help with cybersecurity questions, AlertAI features, and analysis. I have access to all documentation and can guide you through any task.";
    }
  }, [currentAlertTitle, pageContext.pageType]);

  const toggleExpanded = useCallback(() => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setIsMinimized(false);
      setError(null); // Clear any errors when expanding
    }
  }, [isExpanded]);

  const toggleMinimized = useCallback(() => {
    setIsMinimized(!isMinimized);
  }, [isMinimized]);

  const toggleFullScreen = useCallback(() => {
    setIsFullScreen(!isFullScreen);
    if (!isFullScreen) {
      setIsExpanded(true);
      setIsMinimized(false);
    }
  }, [isFullScreen]);

  const handleStartNewChat = async () => {
    try {
      const newSession = await startNewSession(currentAlertId, isIncognitoMode);
      // Messages will be cleared by the session tracking effect
      toast.success('New chat started!');
    } catch (error) {
      console.error('Failed to start new chat:', error);
      toast.error('Failed to start new chat');
    }
  };

  const handleLoadSession = async (sessionId: number) => {
    try {
      const sessionWithMessages = await loadSession(sessionId);

      // Convert chat messages to BitzyMessage format
      const convertedMessages: BitzyMessage[] = [];
      sessionWithMessages.messages.forEach(msg => {
        // Add user message
        convertedMessages.push({
          id: `${msg.id}-user`,
          content: msg.user_message,
          isUser: true,
          timestamp: new Date(msg.created_at),
          dbMessageId: msg.id,
        });

        // Add AI response
        convertedMessages.push({
          id: `${msg.id}-ai`,
          content: msg.ai_response,
          isUser: false,
          timestamp: new Date(msg.created_at),
          dbMessageId: msg.id,
        });
      });

      // Set messages after the session tracking effect clears them
      setTimeout(() => {
        setMessages(convertedMessages);
      }, 50);

      setShowHistory(false);
    } catch (error) {
      console.error('Failed to load session:', error);
      toast.error('Failed to load chat session');
    }
  };

  const handleEndSession = async () => {
    try {
      await endCurrentSession();
      // Messages will be cleared by the session tracking effect
    } catch (error) {
      console.error('Failed to end session:', error);
    }
  };

  const handleCopyMessage = useCallback(async (messageId: string, content: string) => {
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }

      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      toast.success('Message copied to clipboard!');

      // Clear any existing timeout
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }

      // Reset the copied state after timeout
      copyTimeoutRef.current = setTimeout(() => {
        setCopiedMessageId(null);
        copyTimeoutRef.current = null;
      }, COPY_FEEDBACK_TIMEOUT);
    } catch (error) {
      console.error('Failed to copy message:', error);

      // Fallback for older browsers
      try {
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        setCopiedMessageId(messageId);
        toast.success('Message copied to clipboard!');

        copyTimeoutRef.current = setTimeout(() => {
          setCopiedMessageId(null);
          copyTimeoutRef.current = null;
        }, COPY_FEEDBACK_TIMEOUT);
      } catch (fallbackError) {
        toast.error('Failed to copy message. Please copy manually.');
      }
    }
  }, []);

  const handleCopyCode = useCallback(async (codeId: string, codeContent: string) => {
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }

      await navigator.clipboard.writeText(codeContent);
      setCopiedCodeId(codeId);
      toast.success('Code copied to clipboard!');

      // Clear any existing timeout
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }

      // Reset the copied state after timeout
      copyTimeoutRef.current = setTimeout(() => {
        setCopiedCodeId(null);
        copyTimeoutRef.current = null;
      }, COPY_FEEDBACK_TIMEOUT);
    } catch (error) {
      console.error('Failed to copy code:', error);

      // Fallback for older browsers
      try {
        const textArea = document.createElement('textarea');
        textArea.value = codeContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        setCopiedCodeId(codeId);
        toast.success('Code copied to clipboard!');

        copyTimeoutRef.current = setTimeout(() => {
          setCopiedCodeId(null);
          copyTimeoutRef.current = null;
        }, COPY_FEEDBACK_TIMEOUT);
      } catch (fallbackError) {
        toast.error('Failed to copy code. Please copy manually.');
      }
    }
  }, []);

  const handleStartEdit = useCallback((messageId: string, currentContent: string) => {
    setEditingMessageId(messageId);
    setEditingText(currentContent);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setEditingMessageId(null);
    setEditingText('');
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!editingMessageId || !editingText.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      // Find the message being edited to get its database ID
      const messageToEdit = messages.find(msg => msg.id === editingMessageId);
      if (!messageToEdit || !messageToEdit.dbMessageId) {
        throw new Error('Message not found or missing database ID');
      }

      // Find the index of the edited message in the current messages
      const editedMessageIndex = messages.findIndex(msg => msg.id === editingMessageId);
      if (editedMessageIndex === -1) {
        throw new Error('Message not found');
      }

      // Update the edited message content immediately
      const updatedUserMessage: BitzyMessage = {
        ...messages[editedMessageIndex],
        content: editingText.trim(),
      };

      // Remove all messages after the edited one immediately and show only up to the edited message
      const messagesBeforeAndIncludingEdit = messages.slice(0, editedMessageIndex);
      setMessages([...messagesBeforeAndIncludingEdit, updatedUserMessage]);

      // Clear edit state
      setEditingMessageId(null);
      setEditingText('');

      const editRequest: EditMessageRequest = {
        new_message: editingText.trim(),
        user_id: 'current_user',
        page_context: pageContext
      };

      const response = await chatService.editMessage(messageToEdit.dbMessageId, editRequest);

      // Create new AI response message
      const aiMessage: BitzyMessage = {
        id: `${response.message_id}-ai`,
        content: response.response,
        isUser: false,
        timestamp: new Date(response.created_at),
        dbMessageId: response.message_id,
      };

      // Add the AI response to the current messages (which already only contain messages up to the edited one)
      setMessages(prev => [...prev, aiMessage]);

      toast.success('Message edited and conversation regenerated');
    } catch (err) {
      console.error('Failed to edit message:', err);
      setError(err instanceof Error ? err.message : 'Failed to edit message');
      toast.error('Failed to edit message');
    } finally {
      setIsLoading(false);
    }
  }, [editingMessageId, editingText, messages, pageContext]);

  const handleEditKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancelEdit();
    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSaveEdit();
    }
  }, [handleCancelEdit, handleSaveEdit]);

  const handleQuickAction = useCallback(async (action: QuickAction) => {
    const [actionType, ...dataParts] = action.action.split(':');
    const data = dataParts.join(':'); // Rejoin in case there are multiple colons (like in URLs)

    let query = '';
    switch (actionType) {
      case 'investigate-ip':
        query = `Please investigate these IP addresses: ${data}. Check for reputation, geolocation, and any suspicious activity.`;
        break;
      case 'investigate-domain':
        query = `Please analyze these domains: ${data}. Check for reputation, DNS records, and any malicious indicators.`;
        break;
      case 'investigate-hash':
        query = `Please analyze these file hashes: ${data}. Check for malware signatures and threat intelligence.`;
        break;
      case 'run-script':
        query = `Please provide a safe way to execute the script you provided, including any necessary precautions.`;
        break;
      case 'create-playbook':
        query = `Please convert the previous investigation steps into a reusable security playbook.`;
        break;
      case 'create-ticket':
        query = `Please help me create a security incident ticket based on our investigation findings.`;
        break;
      case 'show-examples':
        query = `Please show me some example queries and commands I can use for security investigations.`;
        break;
      case 'help-sources':
        query = `I'm on the sources page. Can you help me understand how to create and manage webhook sources? What are the key steps for setting up a new source?`;
        break;
      case 'help-alerts':
        query = `I'm working with alerts. Can you guide me through the best practices for investigating security alerts? What should I look for first?`;
        break;
      case 'open-docs':
        // Handle documentation links - extract the full URL from the action
        const fullDocUrl = data || '/docs';
        // Remove the 'open-docs:' prefix if present
        const cleanUrl = fullDocUrl.replace(/^open-docs:/, '');
        window.open(cleanUrl, '_blank');
        toast.success('Opening documentation...');
        return;
      default:
        query = `Please help me with: ${action.label}`;
    }

    // Set the input value and trigger send
    setInputValue(query);

    // Auto-send the message
    setTimeout(() => {
      handleSendMessage();
    }, 100);

    toast.success(`Quick action: ${action.label}`);
  }, [setInputValue, handleSendMessage]);

  // Don't render until mounted (prevents SSR issues)
  if (!isMounted) {
    return null;
  }

  const bitzyContent = (
    <div
        className={cn("fixed bottom-4 left-4 sm:bottom-6 sm:left-6 bitzy-assistant pointer-events-auto", className)}
        role="region"
        aria-label="Bitzy AI Assistant"
        data-bitzy-assistant="true"
      >
      {/* Floating Bubble */}
      {!isExpanded && (
        <button
          onClick={toggleExpanded}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggleExpanded();
            }
          }}
          className="relative cursor-pointer group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full pointer-events-auto"
          aria-label={`Open Bitzy AI Assistant chat. Current state: ${bitzyState}. ${messages.length > 0 ? `${messages.length} unread messages` : 'No messages'}`}
          aria-expanded={false}
          aria-haspopup="dialog"
        >
          {/* Bubble with Bitzy */}
          <div className="relative">
            <div className={`w-20 h-20 rounded-full transition-all duration-300 ${getBitzyAnimation(bitzyState)} hover:shadow-lg overflow-hidden bg-background border-2 border-primary/20`}>
              <img
                src={getBitzyImageSrc(bitzyState)}
                alt={`Bitzy AI Assistant - ${bitzyState} state`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to emoji if image fails to load
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              <div
                className="absolute inset-0 flex items-center justify-center text-2xl bg-gradient-to-br from-primary/80 to-primary hidden"
                aria-hidden="true"
              >
                🤖
              </div>
            </div>

            {/* Notification badge for new messages */}
            {messages.length > 0 && (
              <div
                className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
                aria-label={`${messages.length} unread messages`}
              >
                <span className="text-xs text-white font-bold" aria-hidden="true">!</span>
              </div>
            )}

            {/* Hover tooltip */}
            <div
              className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-popover text-popover-foreground text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 shadow-lg border"
              role="tooltip"
              aria-hidden="true"
            >
              Hi! I'm Bitzy, your AI assistant
            </div>
          </div>
        </button>
      )}

      {/* Expanded Chat Interface */}
      {isExpanded && (
        <div
          className={cn(
            "bg-background/95 backdrop-blur-sm rounded-lg shadow-2xl border transition-all duration-300 flex flex-col pointer-events-auto",
            isFullScreen
              ? "fixed inset-2 sm:inset-4 w-auto h-auto"
              : isMinimized
              ? "w-[280px] sm:w-[320px] h-14"
              : "w-[350px] sm:w-[450px] h-[500px] sm:h-[600px] max-h-[90vh]"
          )}
          style={{
            isolation: 'isolate' // Create new stacking context - cannot be replaced with Tailwind
          }}
          role="dialog"
          aria-label="Bitzy AI Assistant Chat"
          aria-modal={isFullScreen}
          aria-expanded={isExpanded}

        >
          {/* Header */}
          <div className={cn(
            "flex items-center justify-between border-b bg-gradient-to-r from-muted/50 to-muted rounded-t-lg",
            isMinimized ? "px-4 py-2" : "p-4"
          )}>
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className={`${isMinimized ? 'w-8 h-8' : 'w-10 h-10'} rounded-full transition-all duration-300 ${getBitzyAnimation(bitzyState)} overflow-hidden bg-background border border-primary/20 flex-shrink-0`}>
                <img
                  src={getBitzyImageSrc(bitzyState)}
                  alt="Bitzy AI Assistant"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                />
                <div className="absolute inset-0 flex items-center justify-center text-lg bg-gradient-to-br from-primary/80 to-primary hidden">
                  🤖
                </div>
              </div>
              {!isMinimized && (
                <div className="min-w-0">
                  <h3 className="font-semibold text-foreground">Bitzy</h3>
                  <p className="text-xs text-muted-foreground">AI Security Assistant</p>
                </div>
              )}
              {isMinimized && (
                <div className="min-w-0">
                  <h3 className="font-semibold text-foreground text-sm">Bitzy</h3>
                </div>
              )}
            </div>
            
            <div className={cn(
              "flex items-center flex-shrink-0",
              isMinimized ? "gap-1" : "gap-1"
            )}>
              {/* Chat Session Controls - Hide some when minimized */}
              {!isMinimized && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleIncognitoMode}
                    className={cn("h-8 w-8 p-0", isIncognitoMode && "bg-orange-100 dark:bg-orange-900/20")}
                    title={isIncognitoMode ? "Disable Incognito Mode" : "Enable Incognito Mode"}
                  >
                    {isIncognitoMode ? <EyeOff className="h-4 w-4 text-orange-600 dark:text-orange-400" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHistory(!showHistory)}
                    className="h-8 w-8 p-0"
                    title="Chat History"
                  >
                    <History className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleStartNewChat}
                    className="h-8 w-8 p-0"
                    title="Start New Chat"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Always visible controls */}
              {!isFullScreen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleMinimized}
                  className="h-8 w-8 p-0"
                  title={isMinimized ? "Maximize" : "Minimize"}
                >
                  {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullScreen}
                className="h-8 w-8 p-0"
                title={isFullScreen ? "Exit Full Screen" : "Full Screen"}
              >
                <Expand className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsExpanded(false);
                  setIsFullScreen(false);
                }}
                className="h-8 w-8 p-0"
                title="Close"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Chat History Sidebar */}
          {showHistory && !isMinimized && (
            <div className="border-b bg-muted/30 p-4 max-h-48 overflow-y-auto">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-foreground">Chat History</h4>
                <div className="flex items-center gap-1">
                  {currentSession && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEndSession}
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive/80"
                      title="End Current Session"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {currentSession && (
                <div className="mb-3 p-2 bg-primary/10 border border-primary/20 rounded text-xs">
                  <div className="font-medium text-primary">Current: {currentSession.title}</div>
                  <div className="text-primary/80">
                    {isIncognitoMode ? "🕵️ Incognito Mode" : `${currentSession.message_count || 0} messages`}
                  </div>
                </div>
              )}

              <div className="space-y-1">
                {chatSessions.length === 0 ? (
                  <p className="text-xs text-muted-foreground text-center py-2">No chat history yet</p>
                ) : (
                  chatSessions.slice(0, 5).map((session) => (
                    <div
                      key={session.id}
                      className="flex items-center justify-between p-2 hover:bg-muted/50 rounded cursor-pointer group"
                      onClick={() => handleLoadSession(session.id)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium text-foreground truncate">
                          {session.title}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(session.created_at).toLocaleDateString()} • {session.message_count || 0} messages
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteSession(session.id);
                        }}
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 text-destructive hover:text-destructive/80"
                        title="Delete Session"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* Chat Content */}
          {!isMinimized && (
            <div className="flex flex-col flex-1 min-h-0">
              {/* Messages */}
              <div
                className={cn(
                  "flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 scroll-smooth",
                  messages.length === 0 && "flex items-center justify-center"
                )}
                role="log"
                aria-label="Chat messages"
                aria-live="polite"
                aria-atomic="false"
                style={{
                  scrollBehavior: 'smooth'
                }}
              >
                {messages.length === 0 && (
                  <div className="text-center text-muted-foreground" role="status">
                    <div className="text-4xl mb-3" aria-hidden="true">👋</div>
                    <p className="text-sm px-4 max-w-sm mx-auto">
                      Hi! I'm Bitzy, your AI security assistant.
                      <br />
                      {welcomeMessage}
                      <br />
                      Ask me anything!
                    </p>
                  </div>
                )}
                
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex",
                      message.isUser ? "justify-end" : "justify-start"
                    )}
                    role="article"
                    aria-label={`${message.isUser ? 'Your message' : 'Bitzy\'s response'} at ${message.timestamp.toLocaleTimeString()}`}
                  >
                    <div
                      className={cn(
                        "rounded-lg px-3 py-2 text-sm relative group",
                        isFullScreen ? "max-w-[85%]" : "max-w-[80%]",
                        message.isUser
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted text-muted-foreground"
                      )}
                    >
                      {message.isUser ? (
                        editingMessageId === message.id ? (
                          // Edit mode for user messages
                          <div className="w-full">
                            <Textarea
                              value={editingText}
                              onChange={(e) => setEditingText(e.target.value)}
                              onKeyDown={handleEditKeyDown}
                              className="w-full min-h-[60px] mb-2 text-sm bg-background/90 border-border text-foreground"
                              placeholder="Edit your message... (Cmd+Enter to save, Esc to cancel)"
                              autoFocus
                            />
                            <div className="flex gap-1 justify-end">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCancelEdit}
                                className="h-6 px-2 text-xs bg-background/80 hover:bg-background text-muted-foreground hover:text-foreground"
                                disabled={isLoading}
                              >
                                <XCircle className="h-3 w-3 mr-1" />
                                Cancel
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleSaveEdit}
                                className="h-6 px-2 text-xs bg-background/80 hover:bg-background text-primary hover:text-primary/80"
                                disabled={isLoading || !editingText.trim()}
                              >
                                <Save className="h-3 w-3 mr-1" />
                                Save
                              </Button>
                            </div>
                          </div>
                        ) : (
                          // Normal display mode for user messages
                          <>
                            <span className="pr-6">{message.content}</span>
                            {/* Edit button for user messages - only show for messages with database IDs */}
                            {message.dbMessageId && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleStartEdit(message.id, message.content)}
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity bg-background/80 hover:bg-background focus:ring-2 focus:ring-primary"
                                aria-label="Edit message"
                                title="Edit message"
                                disabled={isLoading}
                              >
                                <Edit3 className="h-3 w-3 text-primary" aria-hidden="true" />
                              </Button>
                            )}
                          </>
                        )
                      ) : (
                        <>
                          {/* Copy button for AI messages */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyMessage(message.id, message.content)}
                            className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity bg-background/80 hover:bg-background focus:ring-2 focus:ring-primary"
                            aria-label={copiedMessageId === message.id ? "Message copied" : "Copy message to clipboard"}
                            title={copiedMessageId === message.id ? "Message copied" : "Copy message"}
                          >
                            {copiedMessageId === message.id ? (
                              <Check className="h-3 w-3 text-green-600 dark:text-green-400" aria-hidden="true" />
                            ) : (
                              <Copy className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                            )}
                            <span className="sr-only">
                              {copiedMessageId === message.id ? "Message copied to clipboard" : "Copy message to clipboard"}
                            </span>
                          </Button>

                          <div className="prose prose-sm max-w-none text-foreground pr-8">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm, remarkBreaks]}
                            rehypePlugins={[rehypeHighlight]}
                            components={{
                              // Custom styling for paragraphs
                              p: ({ children, ...props }) => (
                                <p className="text-foreground mb-2 leading-relaxed" {...props}>
                                  {children}
                                </p>
                              ),
                              // Custom styling for headings
                              h1: ({ children, ...props }) => (
                                <h1 className="text-lg font-bold text-foreground mb-2 mt-3" {...props}>
                                  {children}
                                </h1>
                              ),
                              h2: ({ children, ...props }) => (
                                <h2 className="text-base font-bold text-foreground mb-2 mt-3" {...props}>
                                  {children}
                                </h2>
                              ),
                              h3: ({ children, ...props }) => (
                                <h3 className="text-sm font-bold text-foreground mb-1 mt-2" {...props}>
                                  {children}
                                </h3>
                              ),
                              // Custom styling for code blocks
                              code: ({ children, ...props }: any) => {
                                const inline = (props as any).inline;
                                return inline ? (
                                  <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono text-foreground" {...props}>
                                    {children}
                                  </code>
                                ) : (
                                  <code className="block bg-gray-800 dark:bg-gray-900 text-gray-100 dark:text-gray-200 p-2 rounded text-xs font-mono overflow-x-auto" {...props}>
                                    {children}
                                  </code>
                                );
                              },
                              // Custom styling for pre blocks with copy button
                              pre: ({ children, ...props }) => {
                                // Extract the code content from children - need to get the raw text
                                const extractTextFromChildren = (children: any): string => {
                                  if (typeof children === 'string') {
                                    return children;
                                  }
                                  if (Array.isArray(children)) {
                                    return children.map(extractTextFromChildren).join('');
                                  }
                                  if (React.isValidElement(children)) {
                                    return extractTextFromChildren((children.props as any).children);
                                  }
                                  return '';
                                };

                                const codeContent = extractTextFromChildren(children);
                                const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                                const lineCount = codeContent.split('\n').length;

                                // Detect language from code element className
                                const codeElement = React.Children.toArray(children).find(
                                  (child): child is React.ReactElement =>
                                    React.isValidElement(child) && child.type === 'code'
                                );
                                const className = codeElement && React.isValidElement(codeElement) ? (codeElement.props as any).className || '' : '';
                                const language = className.replace('language-', '') || 'text';

                                const [isCollapsed, setIsCollapsed] = useState(lineCount > 15);

                                return (
                                  <div className="relative group mb-3 border border-gray-200 rounded-lg overflow-hidden">
                                    {/* Code header with language and controls */}
                                    <div className="flex items-center justify-between bg-gray-800 text-gray-300 px-3 py-2 text-xs">
                                      <span className="font-mono uppercase tracking-wide">
                                        {language === 'text' ? 'Code' : language}
                                        {lineCount > 1 && <span className="ml-2 text-gray-500">({lineCount} lines)</span>}
                                      </span>
                                      <div className="flex items-center gap-2">
                                        {lineCount > 15 && (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setIsCollapsed(!isCollapsed)}
                                            className="h-6 px-2 text-xs text-gray-300 hover:text-white hover:bg-gray-700"
                                          >
                                            {isCollapsed ? 'Expand' : 'Collapse'}
                                          </Button>
                                        )}
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleCopyCode(codeId, codeContent)}
                                          className="h-6 w-6 p-0 text-gray-300 hover:text-white hover:bg-gray-700"
                                          aria-label={copiedCodeId === codeId ? "Code copied" : "Copy code to clipboard"}
                                          title={copiedCodeId === codeId ? "Code copied" : "Copy code"}
                                        >
                                          {copiedCodeId === codeId ? (
                                            <Check className="h-3 w-3 text-green-400" aria-hidden="true" />
                                          ) : (
                                            <Copy className="h-3 w-3" aria-hidden="true" />
                                          )}
                                        </Button>
                                      </div>
                                    </div>

                                    {/* Code content */}
                                    <div className={cn(
                                      "transition-all duration-200 relative",
                                      isCollapsed ? "max-h-80 overflow-hidden" : "max-h-none"
                                    )}>
                                      <pre className="bg-gray-900 text-gray-100 p-4 overflow-x-auto text-sm m-0" {...props}>
                                        {children}
                                      </pre>

                                      {/* Fade overlay for collapsed code */}
                                      {isCollapsed && lineCount > 15 && (
                                        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-gray-900 to-transparent pointer-events-none" />
                                      )}
                                    </div>
                                  </div>
                                );
                              },
                              // Custom styling for links
                              a: ({ children, ...props }) => (
                                <a className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer" {...props}>
                                  {children}
                                </a>
                              ),
                              // Custom styling for lists
                              ul: ({ children, ...props }) => (
                                <ul className="list-disc list-inside space-y-1 mb-2 ml-2" {...props}>
                                  {children}
                                </ul>
                              ),
                              ol: ({ children, ...props }) => (
                                <ol className="list-decimal list-inside space-y-1 mb-2 ml-2" {...props}>
                                  {children}
                                </ol>
                              ),
                              // Custom styling for list items
                              li: ({ children, ...props }) => (
                                <li className="text-gray-900 mb-1" {...props}>
                                  {children}
                                </li>
                              ),
                              // Custom styling for strong/bold
                              strong: ({ children, ...props }) => (
                                <strong className="font-bold text-gray-900" {...props}>
                                  {children}
                                </strong>
                              ),
                              // Custom styling for emphasis/italic
                              em: ({ children, ...props }) => (
                                <em className="italic text-gray-900" {...props}>
                                  {children}
                                </em>
                              ),
                              // Custom styling for blockquotes
                              blockquote: ({ children, ...props }) => (
                                <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-700 mb-2" {...props}>
                                  {children}
                                </blockquote>
                              ),
                            }}
                          >
                            {message.content}
                          </ReactMarkdown>
                          </div>

                          {/* Quick Actions */}
                          {message.suggestions && message.suggestions.length > 0 && (
                            <div className="mt-3 flex flex-wrap gap-2">
                              {message.suggestions.map((action) => (
                                <Button
                                  key={action.id}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleQuickAction(action)}
                                  className="h-7 px-2 text-xs bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300"
                                  title={action.description}
                                >
                                  <span className="mr-1">{action.icon}</span>
                                  {action.label}
                                </Button>
                              ))}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex justify-start" role="status" aria-live="polite">
                    <div className="bg-gray-100 rounded-lg px-3 py-2 text-sm">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" aria-hidden="true"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} aria-hidden="true"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} aria-hidden="true"></div>
                      </div>
                      <span className="sr-only">Bitzy is thinking and preparing a response...</span>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="p-4 border-t" role="form" aria-label="Chat input form">
                {/* Session Status */}
                {(currentSession || isIncognitoMode) && (
                  <div
                    className="mb-2 text-xs text-gray-500 flex items-center justify-between"
                    role="status"
                    aria-live="polite"
                  >
                    <span>
                      {isIncognitoMode
                        ? "🕵️ Incognito mode - messages won't be saved"
                        : currentSession
                          ? `💬 ${currentSession.title}`
                          : "💬 New chat session"
                      }
                    </span>
                    {currentSession && !isIncognitoMode && (
                      <span>{currentSession.message_count || 0} messages</span>
                    )}
                  </div>
                )}

                {/* Error display */}
                {error && (
                  <div
                    className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700"
                    role="alert"
                    aria-live="assertive"
                  >
                    Error: {error}
                  </div>
                )}

                <div className="flex gap-2">
                  <Textarea
                    ref={textareaRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={placeholderText}
                    className="flex-1 min-h-[40px] max-h-[100px] resize-none"
                    disabled={isLoading}
                    aria-label="Type your message to Bitzy"
                    aria-describedby="chat-input-help"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim() || isLoading}
                    size="sm"
                    className="h-10"
                    aria-label={isLoading ? "Sending message..." : "Send message"}
                  >
                    <Send className="h-4 w-4" aria-hidden="true" />
                    <span className="sr-only">
                      {isLoading ? "Sending message..." : "Send message"}
                    </span>
                  </Button>
                </div>

                <div id="chat-input-help" className="sr-only">
                  Press Enter to send message, Shift+Enter for new line, Escape to close chat
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );

  // Render in portal to ensure it's outside modal DOM hierarchy
  return createPortal(bitzyContent, document.body);
});

BitzyAssistantCore.displayName = 'BitzyAssistantCore';

// Main component with error boundary
const BitzyAssistant: React.FC<BitzyAssistantProps> = (props) => {
  const handleError = useCallback((error: Error) => {
    console.error('Bitzy component error:', error);
    toast.error('Bitzy encountered an error. Please refresh the page.');
  }, []);

  return (
    <BitzyErrorBoundary onError={handleError}>
      <BitzyAssistantCore {...props} />
    </BitzyErrorBoundary>
  );
};

export default BitzyAssistant;
