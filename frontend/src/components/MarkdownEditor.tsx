import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Bold,
  Italic,
  Strikethrough,
  Link,
  List,
  ListOrdered,
  Quote,
  Code,
  Code2,
  Image,
  Eye,
  EyeOff,
  Smile,
  Sparkles,
  Loader2
} from 'lucide-react';
import EmojiPicker from 'emoji-picker-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import 'highlight.js/styles/github.css';

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  onImagePaste?: (file: File) => Promise<string>; // Returns the URL of the uploaded image
  onSubmit?: () => void; // Called when Ctrl/Cmd + Enter is pressed
  onBeautify?: (text: string) => Promise<string>; // AI beautifier function
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  placeholder = "Write your comment...",
  disabled = false,
  className = "",
  onImagePaste,
  onSubmit,
  onBeautify
}) => {
  const [isPreview, setIsPreview] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isBeautifying, setIsBeautifying] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const currentValueRef = useRef<string>(value);

  // Keep the ref in sync with the current value
  useEffect(() => {
    currentValueRef.current = value;
  }, [value]);

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newText = value.substring(0, start) + before + textToInsert + after + value.substring(end);
    onChange(newText);

    // Set cursor position after insertion
    setTimeout(() => {
      const newCursorPos = start + before.length + textToInsert.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
      textarea.focus();
    }, 0);
  };

  const insertAtCursor = (text: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const newText = value.substring(0, start) + text + value.substring(end);
    onChange(newText);

    // Set cursor position after insertion
    setTimeout(() => {
      const newCursorPos = start + text.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
      textarea.focus();
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle Ctrl/Cmd + Enter for submission
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey) && onSubmit) {
      e.preventDefault();
      onSubmit();
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const lines = value.substring(0, start).split('\n');
      const currentLine = lines[lines.length - 1];

      // Check for bullet list continuation
      const bulletMatch = currentLine.match(/^(\s*)([-*+])\s/);
      if (bulletMatch) {
        const [, indent, bullet] = bulletMatch;
        const restOfLine = currentLine.substring(bulletMatch[0].length).trim();

        // If the line is empty (just the bullet), remove it
        if (!restOfLine) {
          e.preventDefault();
          const lineStart = start - currentLine.length;
          const newText = value.substring(0, lineStart) + value.substring(start);
          onChange(newText);
          setTimeout(() => {
            textarea.setSelectionRange(lineStart, lineStart);
            textarea.focus();
          }, 0);
          return;
        }

        // Continue the bullet list
        e.preventDefault();
        const newBullet = `\n${indent}${bullet} `;
        insertAtCursor(newBullet);
        return;
      }

      // Check for numbered list continuation
      const numberedMatch = currentLine.match(/^(\s*)(\d+)\.\s/);
      if (numberedMatch) {
        const [, indent, number] = numberedMatch;
        const restOfLine = currentLine.substring(numberedMatch[0].length).trim();

        // If the line is empty (just the number), remove it
        if (!restOfLine) {
          e.preventDefault();
          const lineStart = start - currentLine.length;
          const newText = value.substring(0, lineStart) + value.substring(start);
          onChange(newText);
          setTimeout(() => {
            textarea.setSelectionRange(lineStart, lineStart);
            textarea.focus();
          }, 0);
          return;
        }

        // Continue the numbered list
        e.preventDefault();
        const nextNumber = parseInt(number) + 1;
        const newNumber = `\n${indent}${nextNumber}. `;
        insertAtCursor(newNumber);
        return;
      }

      // Check for checkbox list continuation
      const checkboxMatch = currentLine.match(/^(\s*)([-*+])\s\[([ x])\]\s/);
      if (checkboxMatch) {
        const [, indent, bullet] = checkboxMatch;
        const restOfLine = currentLine.substring(checkboxMatch[0].length).trim();

        // If the line is empty (just the checkbox), remove it
        if (!restOfLine) {
          e.preventDefault();
          const lineStart = start - currentLine.length;
          const newText = value.substring(0, lineStart) + value.substring(start);
          onChange(newText);
          setTimeout(() => {
            textarea.setSelectionRange(lineStart, lineStart);
            textarea.focus();
          }, 0);
          return;
        }

        // Continue the checkbox list
        e.preventDefault();
        const newCheckbox = `\n${indent}${bullet} [ ] `;
        insertAtCursor(newCheckbox);
        return;
      }
    }
  };

  const handleEmojiClick = (emojiData: any) => {
    insertAtCursor(emojiData.emoji);
    setShowEmojiPicker(false);
  };

  const handlePaste = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    if (!onImagePaste) return;

    const items = Array.from(e.clipboardData.items);
    const imageItem = items.find(item => item.type.startsWith('image/'));

    if (imageItem) {
      e.preventDefault();
      const file = imageItem.getAsFile();
      if (!file) return;

      try {
        setIsUploadingImage(true);

        // Create a temporary URL for immediate preview
        const tempUrl = URL.createObjectURL(file);

        // Insert placeholder with preview while uploading
        const placeholder = `\n![Uploading: ${file.name}](${tempUrl})\n`;
        insertAtCursor(placeholder);

        // Upload the image
        const imageUrl = await onImagePaste(file);

        // Replace placeholder with actual image markdown (with proper spacing)
        const imageMarkdown = `\n![${file.name}](${imageUrl})\n`;

        // Use the ref to get the current value instead of the stale closure value
        const currentValue = currentValueRef.current;
        const newValue = currentValue.replace(placeholder, imageMarkdown);
        onChange(newValue);

        // Clean up the temporary URL
        URL.revokeObjectURL(tempUrl);

      } catch (error) {
        console.error('Failed to upload pasted image:', error);
        // Remove the placeholder on error
        const placeholderRegex = new RegExp(`!\\[Uploading: ${file.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\]\\([^)]+\\)`, 'g');
        const newValue = value.replace(placeholderRegex, '');
        onChange(newValue);
      } finally {
        setIsUploadingImage(false);
      }
    }
  };

  const handleBeautify = async () => {
    if (!onBeautify || !value.trim() || isBeautifying) return;

    try {
      setIsBeautifying(true);
      const beautifiedText = await onBeautify(value);
      onChange(beautifiedText);
    } catch (error) {
      console.error('Failed to beautify text:', error);
    } finally {
      setIsBeautifying(false);
    }
  };

  const toolbarButtons = [
    {
      icon: Bold,
      tooltip: 'Bold',
      action: () => insertText('**', '**', 'bold text'),
    },
    {
      icon: Italic,
      tooltip: 'Italic',
      action: () => insertText('*', '*', 'italic text'),
    },
    {
      icon: Strikethrough,
      tooltip: 'Strikethrough',
      action: () => insertText('~~', '~~', 'strikethrough text'),
    },
    {
      icon: Link,
      tooltip: 'Link',
      action: () => insertText('[', '](url)', 'link text'),
    },
    {
      icon: List,
      tooltip: 'Bullet List',
      action: () => insertAtCursor('\n- '),
    },
    {
      icon: ListOrdered,
      tooltip: 'Numbered List',
      action: () => insertAtCursor('\n1. '),
    },
    {
      icon: Quote,
      tooltip: 'Quote',
      action: () => insertAtCursor('\n> '),
    },
    {
      icon: Code,
      tooltip: 'Inline Code',
      action: () => insertText('`', '`', 'code'),
    },
    {
      icon: Code2,
      tooltip: 'Code Block',
      action: () => insertText('\n```\n', '\n```\n', 'code block'),
    },
    {
      icon: Image,
      tooltip: 'Image',
      action: () => insertText('![', '](image-url)', 'alt text'),
    },
    {
      icon: Smile,
      tooltip: 'Emoji',
      action: () => setShowEmojiPicker(!showEmojiPicker),
    },
  ];

  return (
    <div className={`border border-border rounded-lg ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/30">
        <div className="flex items-center space-x-1">
          {toolbarButtons.map((button, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={button.action}
              disabled={disabled}
              className="h-8 w-8 p-0 hover:bg-muted/50"
              title={button.tooltip}
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
        </div>

        <div className="flex items-center space-x-1">
          {/* AI Beautifier Button */}
          {onBeautify && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBeautify}
              disabled={disabled || isBeautifying || !value.trim()}
              className="h-8 px-3 hover:bg-muted/50"
              title="AI Beautify with Markdown"
            >
              {isBeautifying ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                  AI
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-1" />
                  AI
                </>
              )}
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPreview(!isPreview)}
            className="h-8 px-3 hover:bg-muted/50"
            title={isPreview ? 'Edit' : 'Preview'}
          >
            {isPreview ? (
              <>
                <EyeOff className="h-4 w-4 mr-1" />
                Edit
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-1" />
                Preview
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Editor/Preview Area */}
      <div className="min-h-[120px]">
        {isPreview ? (
          <div className="p-3 prose prose-sm max-w-none">
            {value.trim() ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkBreaks]}
                  rehypePlugins={[rehypeHighlight, rehypeRaw]}
                  components={{
                  // Custom styling for markdown elements
                  h1: ({ children }) => <h1 className="text-xl font-bold mb-2 text-foreground">{children}</h1>,
                  h2: ({ children }) => <h2 className="text-lg font-bold mb-2 text-foreground">{children}</h2>,
                  h3: ({ children }) => <h3 className="text-md font-bold mb-1 text-foreground">{children}</h3>,
                  p: ({ children, node }) => {
                    // Check if this paragraph only contains an image by looking at the AST node
                    const hasOnlyImage = node && node.children &&
                      node.children.length === 1 &&
                      (node.children[0] as any).tagName === 'img';

                    // If it's just an image, render as div to avoid nesting issues
                    if (hasOnlyImage) {
                      return <div className="mb-2 last:mb-0">{children}</div>;
                    }

                    return <p className="mb-2 last:mb-0">{children}</p>;
                  },
                  ul: ({ children }) => <ul className="list-disc list-inside mb-2 text-foreground">{children}</ul>,
                  ol: ({ children }) => <ol className="list-decimal list-inside mb-2 text-foreground">{children}</ol>,
                  li: ({ children }) => <li className="mb-1">{children}</li>,
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-primary/30 pl-4 italic text-muted-foreground mb-2">
                      {children}
                    </blockquote>
                  ),
                  code: ({ children, className }) => {
                    const isInline = !className;
                    return isInline ? (
                      <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono text-foreground">
                        {children}
                      </code>
                    ) : (
                      <code className={className}>{children}</code>
                    );
                  },
                  pre: ({ children }) => (
                    <pre className="bg-muted p-3 rounded overflow-x-auto mb-2 border border-border">
                      {children}
                    </pre>
                  ),
                  a: ({ children, href }) => (
                    <a href={href} className="text-primary hover:text-primary/80 hover:underline" target="_blank" rel="noopener noreferrer">
                      {children}
                    </a>
                  ),
                  img: ({ src, alt }) => {
                    return (
                      <div className="my-3">
                        <img
                          src={src}
                          alt={alt}
                          className="max-w-full h-auto max-h-[300px] rounded border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => window.open(src, '_blank')}
                          title="Click to open full size"
                        />
                        {alt && alt !== 'Uploading image...' && (
                          <div className="text-xs text-muted-foreground mt-1 italic">{alt}</div>
                        )}
                      </div>
                    );
                  },
                  table: ({ children }) => (
                    <table className="border-collapse border border-border mb-2">
                      {children}
                    </table>
                  ),
                  th: ({ children }) => (
                    <th className="border border-border px-2 py-1 bg-muted/30 font-semibold text-foreground">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="border border-border px-2 py-1 text-foreground">
                      {children}
                    </td>
                  ),
                }}
              >
                {value}
              </ReactMarkdown>
            ) : (
              <div className="text-muted-foreground italic">Nothing to preview</div>
            )}
          </div>
        ) : (
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              placeholder={placeholder}
              disabled={disabled || isUploadingImage}
              className="min-h-[120px] border-0 resize-none focus:ring-0 focus:border-0 shadow-none"
            />

            {/* Emoji Picker */}
            {showEmojiPicker && (
              <div
                ref={emojiPickerRef}
                className="absolute top-0 right-0 z-50 border border-border rounded-lg shadow-lg bg-background"
              >
                <EmojiPicker
                  onEmojiClick={handleEmojiClick}
                  width={300}
                  height={400}
                  searchDisabled={false}
                  skinTonesDisabled={false}
                  previewConfig={{
                    showPreview: false
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Help Text */}
      {!isPreview && (
        <div className="px-3 py-2 text-xs text-muted-foreground border-t border-border bg-muted/30">
          <div className="flex items-center justify-between">
            <span>
              Supports Markdown: **bold**, *italic*, `code`, [links](url), lists, quotes, emojis 😊 and more. Press Enter to continue lists automatically!
              {onImagePaste && " Paste images directly from clipboard! 📷"}
              {onSubmit && " Press Ctrl/Cmd+Enter to submit. ⌨️"}
              {isUploadingImage && " Uploading image..."}
              {isBeautifying && " AI is beautifying..."}
            </span>
            <span className="text-muted-foreground/70">
              {value.length} characters
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarkdownEditor;
