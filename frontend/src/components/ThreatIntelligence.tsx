import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, Eye, Users, Bug, Target } from 'lucide-react';

interface ThreatIndicator {
  value: string;
  threat_level: string;
  confidence: number;
  description: string;
  sources: string[];
  threat_actors: string[];
  malware_families: string[];
  tags: string[];
}

interface ThreatIntelligence {
  alert_id: number;
  indicators: {
    ip_addresses: ThreatIndicator[];
    domains: ThreatIndicator[];
    file_hashes: ThreatIndicator[];
  };
  threat_summary: {
    total_threats: number;
    high_confidence_threats: number;
    threat_actors: string[];
    malware_families: string[];
    attack_techniques: string[];
  };
}

interface ThreatIntelligenceProps {
  alertId: number;
}

const ThreatIntelligence: React.FC<ThreatIntelligenceProps> = ({ alertId }) => {
  const [threatIntel, setThreatIntel] = useState<ThreatIntelligence | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchThreatIntel = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8001/api/alerts/${alertId}/threat-intel`);
      if (!response.ok) {
        throw new Error('Failed to fetch threat intelligence');
      }
      const data = await response.json();
      setThreatIntel(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [alertId]);

  useEffect(() => {
    fetchThreatIntel();
  }, [fetchThreatIntel]);

  const getThreatLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical': return 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800';
      case 'high': return 'bg-orange-50 dark:bg-orange-950/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800';
      case 'medium': return 'bg-yellow-50 dark:bg-yellow-950/20 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800';
      case 'low': return 'bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 dark:text-green-400';
    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const renderIndicator = (indicator: ThreatIndicator, type: string) => (
    <Card key={indicator.value} className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{indicator.value}</CardTitle>
          <div className="flex gap-2">
            <Badge className={getThreatLevelColor(indicator.threat_level)}>
              {indicator.threat_level.toUpperCase()}
            </Badge>
            <Badge variant="outline" className={getConfidenceColor(indicator.confidence)}>
              {Math.round(indicator.confidence * 100)}% confidence
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-3">{indicator.description}</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Sources</h4>
            <div className="flex flex-wrap gap-1">
              {indicator.sources.map((source, idx) => (
                <Badge key={idx} variant="secondary" className="text-xs">
                  {source}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Tags</h4>
            <div className="flex flex-wrap gap-1">
              {indicator.tags.map((tag, idx) => (
                <Badge key={idx} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {indicator.threat_actors.length > 0 && (
          <div className="mt-3">
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Threat Actors</h4>
            <div className="flex flex-wrap gap-1">
              {indicator.threat_actors.map((actor, idx) => (
                <Badge key={idx} variant="destructive" className="text-xs">
                  <Users className="w-3 h-3 mr-1" />
                  {actor}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {indicator.malware_families.length > 0 && (
          <div className="mt-3">
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Malware Families</h4>
            <div className="flex flex-wrap gap-1">
              {indicator.malware_families.map((malware, idx) => (
                <Badge key={idx} variant="destructive" className="text-xs">
                  <Bug className="w-3 h-3 mr-1" />
                  {malware}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Threat Intelligence
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-8">
        <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
        <p>Failed to load threat intelligence: {error}</p>
      </div>
    );
  }

  if (!threatIntel) {
    return null;
  }

  const { indicators, threat_summary } = threatIntel;
  const hasIndicators = indicators.ip_addresses.length > 0 || 
                       indicators.domains.length > 0 || 
                       indicators.file_hashes.length > 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Threat Intelligence
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Threat Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{threat_summary.total_threats}</div>
            <div className="text-xs text-gray-500">Total Threats</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{threat_summary.high_confidence_threats}</div>
            <div className="text-xs text-gray-500">High Confidence</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{threat_summary.threat_actors.length}</div>
            <div className="text-xs text-gray-500">Threat Actors</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{threat_summary.malware_families.length}</div>
            <div className="text-xs text-gray-500">Malware Families</div>
          </div>
        </div>

        {!hasIndicators ? (
          <Alert>
            <Eye className="h-4 w-4" />
            <AlertDescription>
              No threat indicators found in this alert. The alert appears to be clean or contains no extractable IOCs.
            </AlertDescription>
          </Alert>
        ) : (
          <Tabs defaultValue="ip_addresses" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="ip_addresses" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                IP Addresses ({indicators.ip_addresses.length})
              </TabsTrigger>
              <TabsTrigger value="domains" className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                Domains ({indicators.domains.length})
              </TabsTrigger>
              <TabsTrigger value="file_hashes" className="flex items-center gap-2">
                <Bug className="w-4 h-4" />
                File Hashes ({indicators.file_hashes.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="ip_addresses" className="mt-4">
              {indicators.ip_addresses.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No malicious IP addresses detected</p>
              ) : (
                indicators.ip_addresses.map(indicator => renderIndicator(indicator, 'ip'))
              )}
            </TabsContent>
            
            <TabsContent value="domains" className="mt-4">
              {indicators.domains.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No malicious domains detected</p>
              ) : (
                indicators.domains.map(indicator => renderIndicator(indicator, 'domain'))
              )}
            </TabsContent>
            
            <TabsContent value="file_hashes" className="mt-4">
              {indicators.file_hashes.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No malicious file hashes detected</p>
              ) : (
                indicators.file_hashes.map(indicator => renderIndicator(indicator, 'hash'))
              )}
            </TabsContent>
          </Tabs>
        )}

        {/* Attack Techniques */}
        {threat_summary.attack_techniques.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-semibold text-gray-700 mb-3">MITRE ATT&CK Techniques</h3>
            <div className="flex flex-wrap gap-2">
              {threat_summary.attack_techniques.map((technique, idx) => (
                <Badge key={idx} variant="outline" className="text-xs">
                  <Target className="w-3 h-3 mr-1" />
                  {technique}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ThreatIntelligence;
