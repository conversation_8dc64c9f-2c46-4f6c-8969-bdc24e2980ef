"use client";

import { useEffect, useState } from 'react';
import { Alert } from '@/types/alert';
import { fetchAlerts } from '@/lib/api';
import { AlertsTable } from '@/components/alerts-table';
import { CreateAlertDialog } from '@/components/create-alert-dialog';
import { BulkAlertActions } from '@/components/bulk-alert-actions';
import { useBitzy } from '@/contexts/BitzyContext';
import { clientLogger } from '@/lib/client-logging';


type SortField = 'id' | 'title' | 'description' | 'severity' | 'status' | 'source' | 'created_at' | 'updated_at';
type SortDirection = 'asc' | 'desc' | null;

interface SortConfig {
  field: SortField | null;
  direction: SortDirection;
}

export default function AlertsPage() {
  const { clearCurrentAlert } = useBitzy();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: null, direction: null });
  const [selectedAlerts, setSelectedAlerts] = useState<Alert[]>([]);

  const loadAlerts = async () => {
    try {
      setLoading(true);
      const response = await fetchAlerts();
      setAlerts(response.alerts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load alerts');
    } finally {
      setLoading(false);
    }
  };

  const handleAlertUpdated = (updatedAlert: Alert) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === updatedAlert.id ? updatedAlert : alert
    ));
    // Remove from selection if it was selected
    setSelectedAlerts(prev => prev.filter(alert => alert.id !== updatedAlert.id));
  };

  const handleAlertDeleted = (deletedAlertId: number) => {
    setAlerts(prev => prev.filter(alert => alert.id !== deletedAlertId));
    setSelectedAlerts(prev => prev.filter(alert => alert.id !== deletedAlertId));
  };

  const handleBulkAlertsUpdated = (updatedAlerts: Alert[]) => {
    const updatedIds = updatedAlerts.map(alert => alert.id);
    setAlerts(prev => prev.map(alert => {
      const updated = updatedAlerts.find(u => u.id === alert.id);
      return updated || alert;
    }));
    // Clear selection after bulk update
    setSelectedAlerts([]);
  };

  const handleClearSelection = () => {
    setSelectedAlerts([]);
  };

  useEffect(() => {
    loadAlerts();
    // Clear current alert when viewing alerts list
    clearCurrentAlert();

    // Log page view
    clientLogger.logPageView('/alerts', 'Alerts Dashboard');
  }, [clearCurrentAlert]);

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">Loading alerts...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-destructive">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Alerts</h1>
        <p className="text-muted-foreground">
          Monitor and manage system alerts across your infrastructure.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              Showing {alerts.length} alerts
              {selectedAlerts.length > 0 && (
                <span className="ml-2 text-primary">
                  • {selectedAlerts.length} selected
                </span>
              )}
              {sortConfig.field && sortConfig.direction && (
                <span className="ml-2 text-primary">
                  • Sorted by {sortConfig.field.replace('_', ' ')} ({sortConfig.direction === 'asc' ? 'ascending' : 'descending'})
                </span>
              )}
            </div>
            {selectedAlerts.length > 0 && (
              <BulkAlertActions
                selectedAlerts={selectedAlerts}
                onAlertsUpdated={handleBulkAlertsUpdated}
                onClearSelection={handleClearSelection}
              />
            )}
          </div>
          <CreateAlertDialog onAlertCreated={loadAlerts} />
        </div>

        <AlertsTable
          alerts={alerts}
          onSortChange={setSortConfig}
          selectedAlerts={selectedAlerts}
          onSelectionChange={setSelectedAlerts}
          onAlertUpdated={handleAlertUpdated}
          onAlertDeleted={handleAlertDeleted}
        />
      </div>
    </div>
  );
}
