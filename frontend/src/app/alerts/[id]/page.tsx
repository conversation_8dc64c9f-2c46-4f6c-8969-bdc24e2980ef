"use client";

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Al<PERSON>, Comment, AIFinding, CommentCreate } from '@/types/alert';
import { fetchAlert, fetchAlertComments, fetchAIFindings, createComment, uploadFile } from '@/lib/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { EnhancedComments } from '@/components/enhanced-comments';
import ThreatIntelligence from '@/components/ThreatIntelligence';
import AlertCorrelation from '@/components/AlertCorrelation';

import { cn } from '@/lib/utils';
import { ArrowLeft, Upload, Clock, User, FileImage } from 'lucide-react';
import { useBitzy } from '@/contexts/BitzyContext';

function getSeverityColor(severity: string): string {
  switch (severity) {
    case 'critical':
      return 'bg-red-500 hover:bg-red-600 text-white border-red-600';
    case 'high':
      return 'bg-orange-500 hover:bg-orange-600 text-white border-orange-600';
    case 'medium':
      return 'bg-yellow-500 hover:bg-yellow-600 text-white border-yellow-600';
    case 'low':
      return 'bg-blue-500 hover:bg-blue-600 text-white border-blue-600';
    default:
      return 'bg-muted hover:bg-muted/80 text-muted-foreground border-border';
  }
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'open':
      return 'bg-red-100 text-red-800 hover:bg-red-200';
    case 'acknowledged':
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    case 'resolved':
      return 'bg-green-100 text-green-800 hover:bg-green-200';
    default:
      return 'bg-muted text-muted-foreground hover:bg-muted/80';
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString();
}



export default function AlertDetailPage() {
  const params = useParams();
  const router = useRouter();
  const alertId = parseInt(params.id as string);
  const { setCurrentAlert, clearCurrentAlert } = useBitzy();

  const [alert, setAlert] = useState<Alert | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadAlertData() {
      try {
        setLoading(true);
        const [alertData, commentsData] = await Promise.all([
          fetchAlert(alertId),
          fetchAlertComments(alertId)
        ]);

        setAlert(alertData);
        setComments(commentsData);

        // Set current alert for Bitzy
        setCurrentAlert(alertId, alertData.title);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load alert data');
      } finally {
        setLoading(false);
      }
    }

    if (alertId && !isNaN(alertId)) {
      loadAlertData();
    }
  }, [alertId]); // Removed setCurrentAlert from dependencies to prevent loops

  // Clear current alert when component unmounts
  useEffect(() => {
    return () => {
      clearCurrentAlert();
    };
  }, []); // Empty dependency array since clearCurrentAlert is now memoized

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">Loading alert details...</div>
      </div>
    );
  }

  if (error || !alert) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-red-600">Error: {error || 'Alert not found'}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/alerts">Alerts</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Alert #{alert.id}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Button 
        variant="outline" 
        onClick={() => router.back()}
        className="mb-4"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Alerts
      </Button>

      {/* Main Content Area - Full Width */}
      <div className="space-y-6">
        {/* Alert Details Card */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl">{alert.title}</CardTitle>
                <p className="text-muted-foreground mt-2">{alert.description}</p>
              </div>
              <div className="flex gap-2">
                <Badge className={cn('text-white', getSeverityColor(alert.severity))}>
                  {alert.severity.toUpperCase()}
                </Badge>
                <Badge variant="secondary" className={getStatusColor(alert.status)}>
                  {alert.status.toUpperCase()}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-muted-foreground">Source:</span>
                <p>{alert.source}</p>
              </div>
              <div>
                <span className="font-medium text-muted-foreground">Alert ID:</span>
                <p>#{alert.id}</p>
              </div>
              <div>
                <span className="font-medium text-muted-foreground">Created:</span>
                <p>{formatDate(alert.created_at)}</p>
              </div>
              <div>
                <span className="font-medium text-muted-foreground">Last Updated:</span>
                <p>{alert.updated_at ? formatDate(alert.updated_at) : 'Never'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Comments Section */}
        <EnhancedComments alertId={alertId} />

        {/* Threat Intelligence Section */}
        <ThreatIntelligence alertId={alertId} />

        {/* Alert Correlation Section */}
        <AlertCorrelation alertId={alertId} />
      </div>
    </div>
  );
}
