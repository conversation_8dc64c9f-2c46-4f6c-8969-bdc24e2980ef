import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // Await the params in Next.js 15
    const resolvedParams = await params;
    // Join the path segments to create the file path
    const filePath = resolvedParams.path.join('/');
    
    // Construct the full path to the documentation file
    const docsDir = path.join(process.cwd(), '..', 'docs');
    const fullPath = path.join(docsDir, filePath);
    
    // Security check: ensure the path is within the docs directory
    const resolvedPath = path.resolve(fullPath);
    const resolvedDocsDir = path.resolve(docsDir);
    
    if (!resolvedPath.startsWith(resolvedDocsDir)) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }
    
    // Check if file exists
    if (!fs.existsSync(resolvedPath)) {
      return NextResponse.json(
        { error: 'Documentation file not found' },
        { status: 404 }
      );
    }
    
    // Read the file content
    const content = fs.readFileSync(resolvedPath, 'utf-8');
    
    return NextResponse.json({
      content,
      path: filePath,
      lastModified: fs.statSync(resolvedPath).mtime
    });
    
  } catch (error) {
    console.error('Error serving documentation:', error);
    return NextResponse.json(
      { error: 'Failed to load documentation' },
      { status: 500 }
    );
  }
}
