import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/navbar";
import { Providers } from "@/components/providers";
import BitzyAssistant from "@/components/BitzyAssistant";
import { BitzyProvider } from "@/contexts/BitzyContext";
import { ChatSessionProvider } from "@/contexts/ChatSessionContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AlertAI",
  description: "AI-powered alert monitoring and management system",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geist<PERSON>ono.variable} antialiased`}
      >
        <Providers>
          <BitzyProvider>
            <ChatSessionProvider>
              <div className="flex h-screen bg-background">
                <Navbar />
                <main className="flex-1 overflow-auto p-8 bg-background">
                  {children}
                </main>
                {/* Global Bitzy AI Assistant */}
                <BitzyAssistant />
              </div>
            </ChatSessionProvider>
          </BitzyProvider>
        </Providers>
      </body>
    </html>
  );
}
