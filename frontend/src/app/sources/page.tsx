"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Copy, Edit, Trash2, TestTube, ExternalLink, Webhook, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { SourceCreateDialog } from '@/components/source-create-dialog';
import { SourceEditDialog } from '@/components/source-edit-dialog';
import { SourceTestDialog } from '@/components/source-test-dialog';
import { clientLogger } from '@/lib/client-logging';

interface WebhookSource {
  id: number;
  name: string;
  description?: string;
  webhook_url: string;
  webhook_secret: string;
  auth_type: 'custom_header' | 'bearer_token';
  auth_header_name?: string;
  auth_header_value?: string;
  auth_bearer_token?: string;
  json_mapping: Record<string, string>;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  last_used_at?: string;
  alert_count: number;
}

interface SourceResponse {
  source: WebhookSource;
  webhook_url: string;
}

interface SourcesListResponse {
  sources: WebhookSource[];
  total_count: number;
}

export default function SourcesPage() {
  const [sources, setSources] = useState<WebhookSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState<WebhookSource | null>(null);

  useEffect(() => {
    fetchSources();

    // Log page view
    clientLogger.logPageView('/sources', 'Sources Management');
  }, []);

  const fetchSources = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/sources');
      if (!response.ok) {
        throw new Error('Failed to fetch sources');
      }
      const data: SourcesListResponse = await response.json();
      setSources(data.sources);
    } catch (error) {
      console.error('Error fetching sources:', error);
      toast.error('Failed to load sources');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyWebhookUrl = async (webhookUrl: string) => {
    try {
      await navigator.clipboard.writeText(webhookUrl);
      toast.success('Webhook URL copied to clipboard');

      // Log the copy action
      clientLogger.logButtonClick('copy_webhook_url', {
        webhook_url_length: webhookUrl.length
      });
    } catch (error) {
      toast.error('Failed to copy webhook URL');

      // Log the error
      clientLogger.logError(new Error('Failed to copy webhook URL'), {
        action: 'copy_webhook_url'
      });
    }
  };

  const handleDeleteSource = async (sourceId: number) => {
    if (!confirm('Are you sure you want to delete this source? This action cannot be undone.')) {
      // Log cancellation
      clientLogger.logButtonClick('delete_source_cancelled', {
        source_id: sourceId
      });
      return;
    }

    try {
      const response = await fetch(`/api/sources/${sourceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete source');
      }

      toast.success('Source deleted successfully');

      // Log successful deletion
      clientLogger.logButtonClick('delete_source_success', {
        source_id: sourceId
      });

      fetchSources();
    } catch (error) {
      console.error('Error deleting source:', error);
      toast.error('Failed to delete source');

      // Log deletion error
      clientLogger.logError(error instanceof Error ? error : new Error('Failed to delete source'), {
        action: 'delete_source',
        source_id: sourceId
      });
    }
  };

  const handleEditSource = (source: WebhookSource) => {
    setSelectedSource(source);
    setEditDialogOpen(true);

    // Log edit action
    clientLogger.logButtonClick('edit_source', {
      source_id: source.id,
      source_name: source.name,
      is_active: source.is_active
    });
  };

  const handleTestSource = (source: WebhookSource) => {
    setSelectedSource(source);
    setTestDialogOpen(true);

    // Log test action
    clientLogger.logButtonClick('test_source', {
      source_id: source.id,
      source_name: source.name,
      is_active: source.is_active
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading sources...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Sources</h1>
          <p className="mt-2 text-muted-foreground">
            Manage webhook sources to receive alerts from external systems
          </p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Source
        </Button>
      </div>

      {/* Empty State */}
      {sources.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Webhook className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No sources configured</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Get started by creating your first webhook source to receive alerts from external monitoring systems.
            </p>
            <Button onClick={() => setCreateDialogOpen(true)} className="flex items-center gap-2 mx-auto">
              <Plus className="h-4 w-4" />
              Create Your First Source
            </Button>
          </CardContent>
        </Card>
      ) : (
        /* Sources Grid */
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {sources.map((source) => (
            <Card key={source.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{source.name}</CardTitle>
                    {source.description && (
                      <CardDescription className="mt-1">{source.description}</CardDescription>
                    )}
                  </div>
                  <Badge variant={source.is_active ? "default" : "secondary"}>
                    {source.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Alerts Created</p>
                    <p className="font-semibold">{source.alert_count}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Last Used</p>
                    <p className="font-semibold">
                      {source.last_used_at ? formatDate(source.last_used_at) : 'Never'}
                    </p>
                  </div>
                </div>

                {/* Webhook URL */}
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Webhook URL</p>
                  <div className="flex items-center gap-2">
                    <code className="flex-1 text-xs bg-muted px-2 py-1 rounded truncate">
                      {source.webhook_url}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyWebhookUrl(source.webhook_url)}
                      className="h-7 w-7 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Auth Type */}
                <div>
                  <p className="text-sm text-muted-foreground">Authentication</p>
                  <Badge variant="outline" className="text-xs">
                    {source.auth_type === 'custom_header' ? 'Custom Header' : 'Bearer Token'}
                  </Badge>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSource(source)}
                    className="flex items-center gap-1"
                  >
                    <TestTube className="h-3 w-3" />
                    Test
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditSource(source)}
                    className="flex items-center gap-1"
                  >
                    <Edit className="h-3 w-3" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteSource(source.id)}
                    className="flex items-center gap-1 text-destructive hover:text-destructive/80"
                  >
                    <Trash2 className="h-3 w-3" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialogs */}
      <SourceCreateDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={fetchSources}
      />

      {selectedSource && (
        <>
          <SourceEditDialog
            open={editDialogOpen}
            onOpenChange={setEditDialogOpen}
            source={selectedSource}
            onSuccess={fetchSources}
          />
          <SourceTestDialog
            open={testDialogOpen}
            onOpenChange={setTestDialogOpen}
            source={selectedSource}
          />
        </>
      )}
    </div>
  );
}
