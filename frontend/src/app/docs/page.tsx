"use client";

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Book,
  Search,
  FileText,
  Settings,
  Code,
  AlertTriangle,
  Zap,
  Shield,
  Bot,
  ChevronRight,
  Clock,
  Star
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface DocSection {
  id: string;
  title: string;
  icon: any;
  description: string;
  docs: DocItem[];
  color: string;
}

interface DocItem {
  id: string;
  title: string;
  path: string;
  description: string;
  badge?: string;
  popular?: boolean;
}

const docSections: DocSection[] = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: Zap,
    color: 'bg-blue-500',
    description: 'Learn the basics and get up and running quickly',
    docs: [
      { id: 'overview', title: 'Overview', path: 'overview.md', description: 'What is AlertAI and how it works', popular: true },
      { id: 'quick-start', title: 'Quick Start Guide', path: 'quick-start.md', description: 'Get up and running in 5 minutes', badge: 'Popular' },
      { id: 'installation', title: 'Installation', path: 'installation.md', description: 'Complete installation guide with security setup' },
    ]
  },
  {
    id: 'authentication',
    title: 'Authentication & Security',
    icon: Shield,
    color: 'bg-green-500',
    description: 'Enterprise security and user management',
    docs: [
      { id: 'auth-overview', title: 'Authentication Overview', path: 'authentication/README.md', description: 'Enterprise security features', popular: true },
      { id: 'auth-setup', title: 'Setup Guide', path: 'authentication/setup.md', description: 'Initial authentication configuration', badge: 'Essential' },
      { id: 'user-management', title: 'User Management', path: 'authentication/users.md', description: 'Managing users, roles, and permissions' },
    ]
  },
  {
    id: 'core-features',
    title: 'Core Features',
    icon: FileText,
    color: 'bg-purple-500',
    description: 'Main functionality and features',
    docs: [
      { id: 'alerts', title: 'Alert Management', path: 'alerts/README.md', description: 'Comprehensive alert lifecycle management', popular: true },
      { id: 'ai-assistant', title: 'AI Assistant (Bitzy)', path: 'bitzy/comprehensive.md', description: 'AI-powered analysis and investigation', badge: 'AI-Powered' },
      { id: 'collaboration', title: 'Comments & Collaboration', path: 'comments/README.md', description: 'Team collaboration with real-time features' },
      { id: 'files', title: 'File Management', path: 'files/README.md', description: 'Secure evidence collection and document management' },
      { id: 'sources', title: 'Sources Management', path: 'sources/README.md', description: 'Webhook sources and alert ingestion' },
    ]
  },
  {
    id: 'ai-assistant',
    title: 'AI Assistant (Bitzy)',
    icon: Bot,
    color: 'bg-orange-500',
    description: 'Master the AI-powered analysis features',
    docs: [
      { id: 'bitzy-overview', title: 'Bitzy Overview', path: 'bitzy/README.md', description: 'Introduction to the AI assistant', popular: true },
      { id: 'bitzy-comprehensive', title: 'Comprehensive Guide', path: 'bitzy/comprehensive.md', description: 'Complete AI features and usage', badge: 'Detailed' },
    ]
  },
  {
    id: 'api',
    title: 'API Reference',
    icon: Code,
    color: 'bg-indigo-500',
    description: 'Complete API documentation for developers',
    docs: [
      { id: 'api-overview', title: 'API Overview', path: 'api/README.md', description: 'Complete API reference with authentication', popular: true },
      { id: 'auth-api', title: 'Authentication API', path: 'api/authentication.md', description: 'User management and security endpoints' },
      { id: 'alerts-api', title: 'Alert Management API', path: 'api/alerts.md', description: 'Alert CRUD operations and workflows' },
      { id: 'files-api', title: 'File Management API', path: 'api/files.md', description: 'Secure file upload and management' },
      { id: 'comments-api', title: 'Comments API', path: 'api/comments.md', description: 'Collaboration and communication features' },
      { id: 'ai-api', title: 'AI Chat API', path: 'api/chat.md', description: 'AI assistant integration and analysis' },
      { id: 'webhooks', title: 'Webhooks', path: 'api/webhooks.md', description: 'External system integration guide' },
    ]
  },
  {
    id: 'administration',
    title: 'Administration',
    icon: Settings,
    color: 'bg-red-500',
    description: 'System administration and configuration',
    docs: [
      { id: 'user-admin', title: 'User Administration', path: 'authentication/users.md', description: 'Managing users, roles, and permissions' },
      { id: 'security-practices', title: 'Security Best Practices', path: 'authentication/security.md', description: 'Production security guidelines' },
      { id: 'audit-logging', title: 'Audit Logging', path: 'authentication/audit.md', description: 'Security logging and compliance' },
    ]
  },
  {
    id: 'troubleshooting',
    title: 'Troubleshooting',
    icon: AlertTriangle,
    color: 'bg-yellow-500',
    description: 'Solutions to common problems',
    docs: [
      { id: 'auth-troubleshooting', title: 'Authentication Issues', path: 'authentication/troubleshooting.md', description: 'Common authentication problems' },
      { id: 'common-issues', title: 'Common Issues', path: 'troubleshooting/common-issues.md', description: 'Frequently encountered problems' },
      { id: 'performance', title: 'Performance Issues', path: 'troubleshooting/performance.md', description: 'Optimizing system performance' },
    ]
  }
];

function DocsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedDoc, setSelectedDoc] = useState<string | null>(null);
  const [docContent, setDocContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentDocTitle, setCurrentDocTitle] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle URL-based navigation
  useEffect(() => {
    const docParam = searchParams.get('doc');
    if (docParam && mounted) {
      // Find the document by ID or path
      const foundDoc = docSections.flatMap(section => section.docs).find(doc =>
        doc.id === docParam || doc.path.includes(docParam)
      );

      if (foundDoc) {
        loadDocContent(foundDoc.path, foundDoc.title, false); // Don't update URL to prevent loop
      }
    }
  }, [searchParams, mounted]);

  const loadDocContent = async (path: string, title: string, updateUrl: boolean = true) => {
    setLoading(true);
    try {
      const content = await fetchDocContent(path);
      setDocContent(content);
      setSelectedDoc(path);
      setCurrentDocTitle(title);

      // Update URL with doc parameter
      if (updateUrl) {
        const docId = docSections.flatMap(section => section.docs).find(doc => doc.path === path)?.id;
        if (docId) {
          const newUrl = `/docs?doc=${docId}`;
          router.push(newUrl);
        }
      }
    } catch (error) {
      console.error('Error loading documentation:', error);
      setDocContent('# Error\n\nFailed to load documentation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchDocContent = async (path: string): Promise<string> => {
    try {
      const response = await fetch(`/api/docs/${path}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch documentation: ${response.status}`);
      }
      const data = await response.json();
      return data.content;
    } catch (error) {
      console.error('Error fetching documentation:', error);
      return `# Error Loading Documentation

Failed to load documentation for **${path}**.

Please check:
- The file exists in the docs directory
- The API endpoint is working correctly
- Your network connection

[← Back to Documentation Home](#)`;
    }
  };

  const filteredSections = docSections.map(section => ({
    ...section,
    docs: section.docs.filter(doc =>
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(section => section.docs.length > 0);

  const popularDocs = docSections.flatMap(section =>
    section.docs.filter(doc => doc.popular)
  );

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-80 bg-card border-r border-border flex flex-col">
        <div className="p-6 border-b border-border">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Book className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">AlertAI Docs</h1>
              <p className="text-sm text-muted-foreground">Comprehensive documentation</p>
            </div>
          </div>

          {mounted && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documentation..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {searchQuery === '' && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
                Popular Guides
              </h3>
              <div className="space-y-2">
                {popularDocs.map((doc) => (
                  <button
                    key={doc.id}
                    onClick={() => loadDocContent(doc.path, doc.title)}
                    className="w-full text-left p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-foreground group-hover:text-primary">
                          {doc.title}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {doc.description}
                        </div>
                      </div>
                      <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-6">
            {filteredSections.map((section) => (
              <div key={section.id}>
                <div className="flex items-center gap-3 mb-3">
                  <div className={`p-1.5 ${section.color} rounded-md`}>
                    <section.icon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">{section.title}</h3>
                    <p className="text-xs text-muted-foreground">{section.description}</p>
                  </div>
                </div>

                <div className="space-y-1 ml-7">
                  {section.docs.map((doc) => (
                    <button
                      key={doc.id}
                      onClick={() => loadDocContent(doc.path, doc.title)}
                      className={`w-full text-left p-3 rounded-lg transition-colors group ${
                        selectedDoc === doc.path
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-muted/50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className={`font-medium ${
                              selectedDoc === doc.path ? 'text-primary' : 'text-foreground group-hover:text-primary'
                            }`}>
                              {doc.title}
                            </span>
                            {doc.badge && (
                              <Badge variant="secondary" className="text-xs">
                                {doc.badge}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {doc.description}
                          </p>
                        </div>
                        <ChevronRight className={`h-4 w-4 ${
                          selectedDoc === doc.path ? 'text-primary' : 'text-muted-foreground group-hover:text-primary'
                        }`} />
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {selectedDoc ? (
          <>
            <div className="bg-card border-b border-border px-8 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-foreground">{currentDocTitle}</h1>
                  <p className="text-sm text-muted-foreground mt-1 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Documentation • Last updated June 2025
                  </p>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              <div className="max-w-4xl mx-auto px-8 py-8">
                {loading ? (
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4"></div>
                    <div className="h-4 bg-muted rounded animate-pulse w-full"></div>
                    <div className="h-4 bg-muted rounded animate-pulse w-5/6"></div>
                    <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
                    <div className="h-4 bg-muted rounded animate-pulse w-1/2"></div>
                  </div>
                ) : (
                  <div className="prose prose-slate dark:prose-invert max-w-none prose-headings:text-foreground prose-h1:text-3xl prose-h1:font-bold prose-h1:mb-6 prose-h2:text-2xl prose-h2:font-semibold prose-h2:mb-4 prose-h2:mt-8 prose-h3:text-xl prose-h3:font-semibold prose-h3:mb-3 prose-h3:mt-6 prose-p:text-foreground prose-p:leading-7 prose-p:mb-4 prose-ul:mb-4 prose-li:text-foreground prose-strong:text-foreground prose-code:bg-muted prose-code:text-foreground prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 dark:prose-pre:bg-gray-950 prose-pre:text-gray-100 dark:prose-pre:text-gray-200 prose-a:text-primary prose-a:no-underline hover:prose-a:underline">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {docContent}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-2xl mx-auto px-8">
              <div className="mb-8">
                <div className="p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <Book className="h-10 w-10 text-primary" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">Welcome to AlertAI Documentation</h2>
                <p className="text-lg text-muted-foreground mb-8">
                  Comprehensive guides, tutorials, and reference materials for all AlertAI features.
                  Select a topic from the sidebar to get started.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadDocContent('overview.md', 'Overview')}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Zap className="h-5 w-5 text-primary" />
                      Quick Start
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>
                      Get up and running with AlertAI in just 5 minutes
                    </CardDescription>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadDocContent('bitzy/README.md', 'AI Assistant (Bitzy)')}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Bot className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                      AI Assistant
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>
                      Learn how to use Bitzy for intelligent alert analysis
                    </CardDescription>
                  </CardContent>
                </Card>
              </div>

              <div className="text-sm text-muted-foreground">
                Use the search bar above to quickly find specific topics, or browse by category in the sidebar.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function DocsPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen bg-background">
        <div className="w-80 bg-card border-r border-border flex flex-col">
          <div className="p-6 border-b border-border">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Book className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">AlertAI Docs</h1>
                <p className="text-sm text-muted-foreground">Loading...</p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading documentation...</p>
          </div>
        </div>
      </div>
    }>
      <DocsPageContent />
    </Suspense>
  );
}