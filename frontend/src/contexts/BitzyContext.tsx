"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { ChatSession, ChatMessage, chatService } from '../services/chatService';
import { docsService, DocItem } from '../services/docsService';

interface PageContext {
  pathname: string;
  pageTitle: string;
  pageContent: string;
  pageType: 'alerts-list' | 'alert-detail' | 'dashboard' | 'sources' | 'docs' | 'other';
  contextualDocs: DocItem[];
  pageMetadata: {
    hasForm?: boolean;
    formType?: string;
    hasTable?: boolean;
    hasCards?: boolean;
    primaryAction?: string;
  };
}

interface BitzyContextType {
  currentAlertId?: number;
  currentAlertTitle?: string;
  pageContext: PageContext;
  setCurrentAlert: (alertId?: number, alertTitle?: string) => void;
  clearCurrentAlert: () => void;
  updatePageContext: (context: Partial<PageContext>) => void;
  searchDocs: (query: string) => Promise<any[]>;
  getContextualHelp: () => string[];
  getDocumentationUrls: () => Record<string, string>;
}

const BitzyContext = createContext<BitzyContextType | undefined>(undefined);

export function BitzyProvider({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const [currentAlertId, setCurrentAlertId] = useState<number | undefined>();
  const [currentAlertTitle, setCurrentAlertTitle] = useState<string | undefined>();
  const [pageContext, setPageContext] = useState<PageContext>({
    pathname: '/',
    pageTitle: 'AlertAI',
    pageContent: '',
    pageType: 'other',
    contextualDocs: [],
    pageMetadata: {}
  });
  const [lastContentCapture, setLastContentCapture] = useState<number>(0);

  // Auto-detect page type and update context when pathname changes
  useEffect(() => {
    let pageType: PageContext['pageType'] = 'other';
    let pageTitle = 'AlertAI';

    if (pathname === '/alerts') {
      pageType = 'alerts-list';
      pageTitle = 'Security Alerts Dashboard';
    } else if (pathname?.startsWith('/alerts/')) {
      pageType = 'alert-detail';
      pageTitle = currentAlertTitle ? `Alert: ${currentAlertTitle}` : 'Alert Details';
    } else if (pathname === '/' || pathname === '/dashboard') {
      pageType = 'dashboard';
      pageTitle = 'AlertAI Dashboard';
    } else if (pathname === '/sources') {
      pageType = 'sources';
      pageTitle = 'Sources Management';
    } else if (pathname === '/docs') {
      pageType = 'docs';
      pageTitle = 'Documentation';
    }

    // Get contextual documentation
    const contextualDocs = docsService.getContextualDocs(pageType, pathname);

    // Update basic page context immediately
    setPageContext(prev => {
      // Only update if something actually changed
      if (prev.pathname !== pathname || prev.pageTitle !== pageTitle || prev.pageType !== pageType) {
        return {
          ...prev,
          pathname,
          pageTitle,
          pageType,
          contextualDocs,
        };
      }
      return prev;
    });
  }, [pathname, currentAlertTitle]);

  // Separate effect for capturing page content to avoid loops
  useEffect(() => {
    const capturePageContent = () => {
      const now = Date.now();
      // Prevent capturing content too frequently (max once per 3 seconds)
      if (now - lastContentCapture < 3000) {
        return;
      }

      try {
        // Get main content area
        const mainContent = document.querySelector('main');
        if (mainContent) {
          // Extract visible text content, clean it up
          const textContent = mainContent.innerText || mainContent.textContent || '';
          // Limit content length and clean up whitespace
          const cleanContent = textContent
            .replace(/\s+/g, ' ')
            .trim()
            .substring(0, 1500); // Reduced to 1500 chars for better performance

          // Detect page metadata
          const pageMetadata = {
            hasForm: !!mainContent.querySelector('form'),
            formType: mainContent.querySelector('form')?.getAttribute('data-form-type') ||
                     (mainContent.querySelector('form input[type="submit"]')?.getAttribute('value')) ||
                     'unknown',
            hasTable: !!mainContent.querySelector('table'),
            hasCards: !!mainContent.querySelector('[class*="card"]'),
            primaryAction: mainContent.querySelector('[data-primary-action]')?.getAttribute('data-primary-action') ||
                          mainContent.querySelector('button[type="submit"]')?.textContent?.trim() ||
                          mainContent.querySelector('.btn-primary')?.textContent?.trim()
          };

          // Only update if content actually changed and is meaningful
          if (cleanContent.length > 50 && (cleanContent !== pageContext.pageContent ||
              JSON.stringify(pageMetadata) !== JSON.stringify(pageContext.pageMetadata))) {
            setPageContext(prev => ({
              ...prev,
              pageContent: cleanContent,
              pageMetadata
            }));
            setLastContentCapture(now);
          }
        }
      } catch (error) {
        console.warn('Failed to capture page content:', error);
      }
    };

    // Only capture content if we're on a meaningful page
    if (pageContext.pageType !== 'other') {
      const timer = setTimeout(capturePageContent, 2000);
      return () => clearTimeout(timer);
    }
  }, [pageContext.pageType, pageContext.pathname, lastContentCapture]);

  const setCurrentAlert = useCallback((alertId?: number, alertTitle?: string) => {
    setCurrentAlertId(alertId);
    setCurrentAlertTitle(alertTitle);
  }, []);

  const clearCurrentAlert = useCallback(() => {
    setCurrentAlertId(undefined);
    setCurrentAlertTitle(undefined);
  }, []);

  const updatePageContext = useCallback((context: Partial<PageContext>) => {
    setPageContext(prev => ({ ...prev, ...context }));
  }, []);

  const searchDocs = useCallback(async (query: string) => {
    return await docsService.searchDocs(query);
  }, []);

  const getContextualHelp = useCallback(() => {
    return docsService.generateDocSuggestions('', pageContext);
  }, [pageContext]);

  const getDocumentationUrls = useCallback(() => {
    return docsService.getCommonDocumentationUrls();
  }, []);

  return (
    <BitzyContext.Provider
      value={{
        currentAlertId,
        currentAlertTitle,
        pageContext,
        setCurrentAlert,
        clearCurrentAlert,
        updatePageContext,
        searchDocs,
        getContextualHelp,
        getDocumentationUrls,
      }}
    >
      {children}
    </BitzyContext.Provider>
  );
}

export function useBitzy() {
  const context = useContext(BitzyContext);
  if (context === undefined) {
    throw new Error('useBitzy must be used within a BitzyProvider');
  }
  return context;
}
