"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect } from 'react';
import { ChatSession, ChatMessage, chatService, ChatSessionWithMessages } from '../services/chatService';
import { toast } from 'sonner';

interface ChatSessionContextType {
  currentSession?: ChatSession;
  chatSessions: ChatSession[];
  isIncognitoMode: boolean;
  isLoading: boolean;
  startNewSession: (alertId?: number, isIncognito?: boolean, title?: string) => Promise<ChatSession>;
  loadSession: (sessionId: number) => Promise<ChatSessionWithMessages>;
  endCurrentSession: () => Promise<void>;
  deleteSession: (sessionId: number) => Promise<void>;
  toggleIncognitoMode: () => void;
  refreshSessions: () => Promise<void>;
  setCurrentSession: (session?: ChatSession) => void;
  handleNewSessionFromMessage: (sessionId: number) => Promise<void>;
}

const ChatSessionContext = createContext<ChatSessionContextType | undefined>(undefined);

export function ChatSessionProvider({ children }: { children: ReactNode }) {
  const [currentSession, setCurrentSession] = useState<ChatSession | undefined>();
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isIncognitoMode, setIsIncognitoMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load user's chat sessions on mount
  useEffect(() => {
    refreshSessions();
  }, []);

  const refreshSessions = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await chatService.getUserSessions('current_user', false);
      setChatSessions(response.sessions);
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
      toast.error('Failed to load chat history');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const startNewSession = useCallback(async (
    alertId?: number, 
    isIncognito: boolean = false, 
    title?: string
  ): Promise<ChatSession> => {
    try {
      setIsLoading(true);
      const session = await chatService.createSession({
        user_id: 'current_user',
        alert_id: alertId,
        title,
        is_incognito: isIncognito
      });

      setCurrentSession(session);

      // Only add to sessions list if not incognito
      if (!isIncognito) {
        setChatSessions(prev => [session, ...prev]);
        toast.success('New chat session started');
      } else {
        toast.success('Incognito chat started (won\'t be saved)');
      }

      return session;
    } catch (error) {
      console.error('Failed to create chat session:', error);
      toast.error('Failed to start new chat session');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadSession = useCallback(async (sessionId: number): Promise<ChatSessionWithMessages> => {
    try {
      setIsLoading(true);
      const sessionWithMessages = await chatService.getSessionWithMessages(sessionId);
      setCurrentSession(sessionWithMessages.session);
      toast.success(`Loaded chat: ${sessionWithMessages.session.title}`);
      return sessionWithMessages;
    } catch (error) {
      console.error('Failed to load chat session:', error);
      toast.error('Failed to load chat session');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const endCurrentSession = useCallback(async () => {
    if (!currentSession) return;

    try {
      setIsLoading(true);
      await chatService.endSession(currentSession.id);
      
      // Update the session in the list
      setChatSessions(prev => 
        prev.map(session => 
          session.id === currentSession.id 
            ? { ...session, is_active: false, ended_at: new Date().toISOString() }
            : session
        )
      );

      setCurrentSession(undefined);
      toast.success('Chat session ended');
    } catch (error) {
      console.error('Failed to end chat session:', error);
      toast.error('Failed to end chat session');
    } finally {
      setIsLoading(false);
    }
  }, [currentSession]);

  const deleteSession = useCallback(async (sessionId: number) => {
    try {
      setIsLoading(true);
      await chatService.deleteSession(sessionId);
      
      // Remove from sessions list
      setChatSessions(prev => prev.filter(session => session.id !== sessionId));
      
      // Clear current session if it was deleted
      if (currentSession?.id === sessionId) {
        setCurrentSession(undefined);
      }

      toast.success('Chat session deleted');
    } catch (error) {
      console.error('Failed to delete chat session:', error);
      toast.error('Failed to delete chat session');
    } finally {
      setIsLoading(false);
    }
  }, [currentSession]);

  const toggleIncognitoMode = useCallback(() => {
    setIsIncognitoMode(prev => {
      const newMode = !prev;
      toast.info(newMode ? 'Incognito mode enabled' : 'Incognito mode disabled');
      return newMode;
    });
  }, []);

  const handleNewSessionFromMessage = useCallback(async (sessionId: number) => {
    try {
      // Fetch the newly created session
      const sessionWithMessages = await chatService.getSessionWithMessages(sessionId);
      setCurrentSession(sessionWithMessages.session);

      // Add to sessions list if not incognito
      if (!sessionWithMessages.session.is_incognito) {
        setChatSessions(prev => {
          // Check if session already exists to avoid duplicates
          const exists = prev.some(s => s.id === sessionId);
          if (!exists) {
            return [sessionWithMessages.session, ...prev];
          }
          return prev;
        });
      }
    } catch (error) {
      console.error('Failed to handle new session from message:', error);
    }
  }, []);

  return (
    <ChatSessionContext.Provider
      value={{
        currentSession,
        chatSessions,
        isIncognitoMode,
        isLoading,
        startNewSession,
        loadSession,
        endCurrentSession,
        deleteSession,
        toggleIncognitoMode,
        refreshSessions,
        setCurrentSession,
        handleNewSessionFromMessage,
      }}
    >
      {children}
    </ChatSessionContext.Provider>
  );
}

export function useChatSession() {
  const context = useContext(ChatSessionContext);
  if (context === undefined) {
    throw new Error('useChatSession must be used within a ChatSessionProvider');
  }
  return context;
}
