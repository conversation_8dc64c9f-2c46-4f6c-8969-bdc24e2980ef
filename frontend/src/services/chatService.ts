// Chat session management service

export interface ChatSession {
  id: number;
  user_id: string;
  alert_id?: number;
  title: string;
  is_incognito: boolean;
  created_at: string;
  updated_at?: string;
  ended_at?: string;
  is_active: boolean;
  message_count?: number;
}

export interface ChatMessage {
  id: number;
  session_id: number;
  user_message: string;
  ai_response: string;
  message_order: number;
  created_at: string;
  response_time_ms?: number;
  context_data?: any;
}

export interface ChatSessionWithMessages {
  session: ChatSession;
  messages: ChatMessage[];
}

export interface ChatRequest {
  message: string;
  user_id?: string;
  session_id?: number;
  page_context?: any;
  is_incognito?: boolean;
}

export interface EditMessageRequest {
  new_message: string;
  user_id?: string;
  page_context?: any;
}

export interface ChatResponse {
  message: string;
  response: string;
  session_id: number;
  message_id: number;
  created_at: string;
  is_new_session?: boolean;
}

export interface ChatSessionCreateRequest {
  user_id?: string;
  alert_id?: number;
  title?: string;
  is_incognito?: boolean;
}

export interface ChatSessionListResponse {
  sessions: ChatSession[];
  total_count: number;
}

const API_BASE = '/api';

export const chatService = {
  // Session management
  async createSession(request: ChatSessionCreateRequest): Promise<ChatSession> {
    const response = await fetch(`${API_BASE}/chat-sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Failed to create chat session');
    }

    return response.json();
  },

  async getUserSessions(
    userId: string = 'current_user',
    includeIncognito: boolean = false,
    alertId?: number
  ): Promise<ChatSessionListResponse> {
    const params = new URLSearchParams({
      user_id: userId,
      include_incognito: includeIncognito.toString(),
    });

    if (alertId !== undefined) {
      params.append('alert_id', alertId.toString());
    }

    const response = await fetch(`${API_BASE}/chat-sessions?${params}`);

    if (!response.ok) {
      throw new Error('Failed to fetch chat sessions');
    }

    return response.json();
  },

  async getSessionWithMessages(sessionId: number): Promise<ChatSessionWithMessages> {
    const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}`);

    if (!response.ok) {
      throw new Error('Failed to fetch chat session');
    }

    return response.json();
  },

  async endSession(sessionId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}/end`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error('Failed to end chat session');
    }
  },

  async deleteSession(sessionId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete chat session');
    }
  },

  // Chat functionality
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const endpoint = request.session_id && request.session_id > 0 
      ? `${API_BASE}/chat` 
      : `${API_BASE}/chat`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Failed to send chat message');
    }

    return response.json();
  },

  async sendAlertMessage(alertId: number, request: ChatRequest): Promise<ChatResponse> {
    const response = await fetch(`${API_BASE}/alerts/${alertId}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Failed to send alert chat message');
    }

    return response.json();
  },

  async editMessage(messageId: number, request: EditMessageRequest): Promise<ChatResponse> {
    const response = await fetch(`${API_BASE}/chat-messages/${messageId}/edit`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Failed to edit chat message');
    }

    return response.json();
  },
};
