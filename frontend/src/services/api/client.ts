/**
 * API Client
 * 
 * Base API client with error handling, retries, and type safety.
 * Provides a consistent interface for all API calls.
 */

import { parseError, logError, AlertAIError, ErrorCodes } from '@/lib/error-handling';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api';
const DEFAULT_TIMEOUT = 30000; // 30 seconds

interface RequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

class APIClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit & RequestConfig = {}
  ): Promise<T> {
    const {
      headers = {},
      timeout = DEFAULT_TIMEOUT,
      retries = 3,
      ...fetchOptions
    } = options;

    const url = `${this.baseURL}${endpoint}`;
    const controller = new AbortController();

    // Set up timeout
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, timeout);

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        headers: {
          ...this.defaultHeaders,
          ...headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorData: any = {};
        try {
          errorData = await response.json();
        } catch {
          errorData = { detail: response.statusText };
        }

        const errorCode = 
          response.status === 404 ? ErrorCodes.NOT_FOUND :
          response.status === 401 ? ErrorCodes.UNAUTHORIZED :
          response.status === 403 ? ErrorCodes.FORBIDDEN :
          response.status >= 500 ? ErrorCodes.API_ERROR :
          ErrorCodes.VALIDATION_ERROR;

        const error = new AlertAIError(
          errorCode,
          errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
          { status: response.status, statusText: response.statusText, ...errorData },
          { endpoint, method: fetchOptions.method || 'GET' }
        );

        logError(parseError(error));
        throw error;
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        return {} as T;
      }

      return await response.json();

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof AlertAIError) {
        throw error;
      }

      if (error instanceof DOMException && error.name === 'AbortError') {
        const timeoutError = new AlertAIError(
          ErrorCodes.TIMEOUT_ERROR,
          'Request was cancelled or timed out',
          error,
          { endpoint, method: fetchOptions.method || 'GET', timeout }
        );
        logError(parseError(timeoutError));
        throw timeoutError;
      }

      const parsedError = parseError(error, { endpoint, method: fetchOptions.method || 'GET' });
      logError(parsedError);
      
      throw new AlertAIError(
        parsedError.code,
        parsedError.message,
        parsedError.details,
        parsedError.context
      );
    }
  }

  async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data instanceof FormData ? data : JSON.stringify(data),
      headers: data instanceof FormData 
        ? { ...config?.headers } // Don't set Content-Type for FormData
        : { ...config?.headers },
    });
  }

  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // Set authorization header
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  // Remove authorization header
  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  // Update base URL
  setBaseURL(baseURL: string) {
    this.baseURL = baseURL;
  }
}

// Create and export the default API client instance
export const apiClient = new APIClient(API_BASE_URL);

// Export the class for creating additional instances if needed
export { APIClient };
