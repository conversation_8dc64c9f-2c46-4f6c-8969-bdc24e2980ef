/**
 * Alerts API Service
 * 
 * Handles all API calls related to alerts with proper error handling
 * and type safety.
 */

import { Alert, AlertResponse, AlertCreateRequest, AlertUpdateRequest } from '@/types/alert';
import { apiClient } from './client';

interface GetAlertsParams {
  limit?: number;
  offset?: number;
  search?: string;
  severity?: string;
  status?: string;
  source?: string;
  assignedTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const alertsApi = {
  /**
   * Get alerts with pagination and filtering
   */
  async getAlerts(params: GetAlertsParams = {}): Promise<AlertResponse> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const url = `/alerts${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiClient.get<AlertResponse>(url);
  },

  /**
   * Get a specific alert by ID
   */
  async getAlert(alertId: number): Promise<Alert> {
    return apiClient.get<Alert>(`/alerts/${alertId}`);
  },

  /**
   * Create a new alert
   */
  async createAlert(alertData: AlertCreateRequest): Promise<Alert> {
    return apiClient.post<Alert>('/alerts', alertData);
  },

  /**
   * Update an existing alert
   */
  async updateAlert(alertId: number, updateData: AlertUpdateRequest): Promise<Alert> {
    return apiClient.put<Alert>(`/alerts/${alertId}`, updateData);
  },

  /**
   * Delete an alert (soft delete by default)
   */
  async deleteAlert(alertId: number, permanent: boolean = false): Promise<{ success: boolean; message: string }> {
    const params = permanent ? '?permanent=true' : '';
    return apiClient.delete(`/alerts/${alertId}${params}`);
  },

  /**
   * Get alert comments
   */
  async getAlertComments(alertId: number): Promise<any[]> {
    return apiClient.get(`/alerts/${alertId}/comments`);
  },

  /**
   * Create a comment for an alert
   */
  async createAlertComment(alertId: number, commentData: any): Promise<any> {
    return apiClient.post(`/alerts/${alertId}/comments`, commentData);
  },

  /**
   * Get AI findings for an alert
   */
  async getAlertAIFindings(alertId: number): Promise<any[]> {
    return apiClient.get(`/alerts/${alertId}/ai-findings`);
  },

  /**
   * Upload a file for an alert
   */
  async uploadAlertFile(alertId: number, file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post(`/alerts/${alertId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Get alert files
   */
  async getAlertFiles(alertId: number): Promise<any[]> {
    return apiClient.get(`/alerts/${alertId}/files`);
  },

  /**
   * Chat with AI about an alert
   */
  async chatWithAI(alertId: number, message: string, sessionId?: number): Promise<any> {
    return apiClient.post(`/alerts/${alertId}/chat`, {
      message,
      session_id: sessionId,
      user_id: 'current_user', // TODO: Get from auth context
    });
  },

  /**
   * Get alert change log
   */
  async getAlertChangeLog(alertId: number): Promise<any[]> {
    return apiClient.get(`/alerts/${alertId}/change-log`);
  },

  /**
   * Get threat intelligence for an alert
   */
  async getAlertThreatIntel(alertId: number): Promise<any> {
    return apiClient.get(`/alerts/${alertId}/threat-intel`);
  },

  /**
   * Get alert correlations
   */
  async getAlertCorrelations(alertId: number): Promise<any> {
    return apiClient.get(`/alerts/${alertId}/correlations`);
  },
};
