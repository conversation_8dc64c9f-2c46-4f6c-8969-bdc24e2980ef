// Documentation service for Bitzy AI assistant

export interface DocSection {
  id: string;
  title: string;
  description: string;
  docs: DocItem[];
  category: string;
}

export interface DocItem {
  id: string;
  title: string;
  path: string;
  description: string;
  content?: string;
  badge?: string;
  popular?: boolean;
  urlSlug?: string; // For user-friendly URLs
}

export interface DocSearchResult {
  item: DocItem;
  section: DocSection;
  relevanceScore: number;
  matchedContent?: string;
}

// Documentation structure for Bitzy - Only includes docs that actually exist
export const docSections: DocSection[] = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    category: 'basics',
    description: 'Learn the basics and get up and running quickly',
    docs: [
      { id: 'overview', title: 'Overview', path: 'overview.md', description: 'What is AlertAI and how it works', popular: true, urlSlug: 'overview' },
      { id: 'quick-start', title: 'Quick Start Guide', path: 'quick-start.md', description: 'Get up and running in 5 minutes', badge: 'Popular', urlSlug: 'quick-start' },
    ]
  },
  {
    id: 'core-features',
    title: 'Core Features',
    category: 'features',
    description: 'Main functionality and features',
    docs: [
      { id: 'sources', title: 'Sources Management', path: 'sources/README.md', description: 'Managing webhook sources for alert ingestion', popular: true, urlSlug: 'sources' },
      { id: 'bitzy', title: 'AI Assistant (Bitzy)', path: 'bitzy/README.md', description: 'Using AI for alert analysis', badge: 'AI-Powered', urlSlug: 'bitzy' },
    ]
  }
];

class DocsService {
  private docCache = new Map<string, string>();
  private lastCacheUpdate = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get documentation content for a specific path
   */
  async getDocContent(path: string): Promise<string | null> {
    try {
      // Check cache first
      const cacheKey = path;
      const now = Date.now();
      
      if (this.docCache.has(cacheKey) && (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
        return this.docCache.get(cacheKey) || null;
      }

      const response = await fetch(`/api/docs/${path}`);
      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      const content = data.content;

      // Cache the content
      this.docCache.set(cacheKey, content);
      this.lastCacheUpdate = now;

      return content;
    } catch (error) {
      console.error('Error fetching documentation:', error);
      return null;
    }
  }

  /**
   * Search documentation for relevant content
   */
  async searchDocs(query: string, limit: number = 5): Promise<DocSearchResult[]> {
    const results: DocSearchResult[] = [];
    const lowerQuery = query.toLowerCase();

    for (const section of docSections) {
      for (const doc of section.docs) {
        let relevanceScore = 0;
        let matchedContent = '';

        // Check title match
        if (doc.title.toLowerCase().includes(lowerQuery)) {
          relevanceScore += 10;
        }

        // Check description match
        if (doc.description.toLowerCase().includes(lowerQuery)) {
          relevanceScore += 5;
        }

        // Check content match (if available)
        try {
          const content = await this.getDocContent(doc.path);
          if (content) {
            const contentLower = content.toLowerCase();
            if (contentLower.includes(lowerQuery)) {
              relevanceScore += 3;
              
              // Extract relevant snippet
              const queryIndex = contentLower.indexOf(lowerQuery);
              if (queryIndex !== -1) {
                const start = Math.max(0, queryIndex - 100);
                const end = Math.min(content.length, queryIndex + 200);
                matchedContent = content.substring(start, end).trim();
                if (start > 0) matchedContent = '...' + matchedContent;
                if (end < content.length) matchedContent = matchedContent + '...';
              }
            }
          }
        } catch (error) {
          // Continue without content match
        }

        // Boost popular docs
        if (doc.popular) {
          relevanceScore += 2;
        }

        if (relevanceScore > 0) {
          results.push({
            item: doc,
            section,
            relevanceScore,
            matchedContent
          });
        }
      }
    }

    // Sort by relevance and return top results
    return results
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * Get documentation relevant to current page context
   */
  getContextualDocs(pageType: string, pathname: string): DocItem[] {
    const contextualDocs: DocItem[] = [];

    // Add docs based on page type
    switch (pageType) {
      case 'alerts-list':
      case 'alert-detail':
        contextualDocs.push(
          ...this.findDocsByPath(['bitzy/README.md', 'overview.md'])
        );
        break;

      case 'sources':
        contextualDocs.push(
          ...this.findDocsByPath(['sources/README.md', 'quick-start.md'])
        );
        break;

      case 'dashboard':
        contextualDocs.push(
          ...this.findDocsByPath(['overview.md', 'quick-start.md'])
        );
        break;

      case 'docs':
        // If on docs page, suggest popular docs
        contextualDocs.push(
          ...docSections.flatMap(section => section.docs.filter(doc => doc.popular))
        );
        break;

      default:
        // Default suggestions
        contextualDocs.push(
          ...this.findDocsByPath(['overview.md', 'quick-start.md', 'bitzy/README.md'])
        );
    }

    return contextualDocs.slice(0, 3); // Limit to 3 suggestions
  }

  /**
   * Find documentation items by their paths
   */
  private findDocsByPath(paths: string[]): DocItem[] {
    const docs: DocItem[] = [];
    
    for (const path of paths) {
      for (const section of docSections) {
        const doc = section.docs.find(d => d.path === path);
        if (doc) {
          docs.push(doc);
          break;
        }
      }
    }
    
    return docs;
  }

  /**
   * Get all documentation content for AI context
   */
  async getAllDocsForAI(): Promise<string> {
    const allContent: string[] = [];
    
    for (const section of docSections) {
      allContent.push(`\n## ${section.title}\n${section.description}\n`);
      
      for (const doc of section.docs) {
        try {
          const content = await this.getDocContent(doc.path);
          if (content) {
            allContent.push(`\n### ${doc.title}\nPath: ${doc.path}\nDescription: ${doc.description}\n\n${content}\n`);
          }
        } catch (error) {
          // Skip docs that can't be loaded
          continue;
        }
      }
    }
    
    return allContent.join('\n');
  }

  /**
   * Generate documentation suggestions based on user query
   */
  generateDocSuggestions(query: string, pageContext: any): string[] {
    const suggestions: string[] = [];
    const lowerQuery = query.toLowerCase();

    // Common question patterns and their doc suggestions (only existing docs)
    const patterns = [
      { keywords: ['how to', 'how do i', 'create', 'add', 'new'], docs: ['quick-start.md'] },
      { keywords: ['source', 'webhook', 'integration'], docs: ['sources/README.md'] },
      { keywords: ['alert', 'investigation', 'analyze'], docs: ['bitzy/README.md'] },
      { keywords: ['bitzy', 'ai', 'assistant'], docs: ['bitzy/README.md'] },
      { keywords: ['overview', 'what is', 'introduction'], docs: ['overview.md'] },
      { keywords: ['auth', 'authentication', 'bearer', 'token'], docs: ['sources/README.md'] },
    ];

    for (const pattern of patterns) {
      if (pattern.keywords.some(keyword => lowerQuery.includes(keyword))) {
        for (const docPath of pattern.docs) {
          const doc = this.findDocsByPath([docPath])[0];
          if (doc) {
            const docUrl = this.getDocumentationUrl(doc.id);
            suggestions.push(`📖 Check out: [${doc.title}](${docUrl}) - ${doc.description}`);
          }
        }
      }
    }

    return suggestions.slice(0, 2); // Limit to 2 suggestions
  }

  /**
   * Get the URL for a specific documentation page
   */
  getDocumentationUrl(docId: string): string {
    return `/docs?doc=${docId}`;
  }

  /**
   * Get documentation URLs for common topics (for Bitzy to reference)
   */
  getCommonDocumentationUrls(): Record<string, string> {
    return {
      'sources': this.getDocumentationUrl('sources'),
      'bitzy': this.getDocumentationUrl('bitzy'),
      'quick-start': this.getDocumentationUrl('quick-start'),
      'overview': this.getDocumentationUrl('overview'),
    };
  }
}

export const docsService = new DocsService();
