/** @type {import('next').NextConfig} */
const nextConfig = {
  // Ultra-fast development configuration
  
  // Disable optimizations that slow down development
  swcMinify: false,
  
  // Minimal webpack configuration for speed
  webpack: (config, { dev }) => {
    if (dev) {
      // Disable all optimizations in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
        minimize: false,
        minimizer: [],
        usedExports: false,
        sideEffects: false,
        concatenateModules: false,
        flagIncludedChunks: false,
        occurrenceOrder: false,
        providedExports: false,
        mangleExports: false,
        innerGraph: false,
        realContentHash: false,
      };

      // Faster module resolution
      config.resolve.symlinks = false;
      config.resolve.cacheWithContext = false;
      
      // Disable source maps completely
      config.devtool = false;
      
      // Reduce file watching overhead
      config.watchOptions = {
        ignored: /node_modules/,
        aggregateTimeout: 300,
        poll: false,
      };
      
      // Faster module building
      config.module.unsafeCache = true;
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename],
        },
      };
    }
    
    return config;
  },

  // Disable features that slow down development
  experimental: {
    // Disable optimizations
    optimizeCss: false,
    optimizePackageImports: false,
  },

  // Fast refresh settings
  onDemandEntries: {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 2,
  },

  // Disable type checking and linting
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable telemetry
  telemetry: false,
};

module.exports = nextConfig;
