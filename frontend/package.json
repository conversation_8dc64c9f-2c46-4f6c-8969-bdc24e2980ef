{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "dev:fast": "node scripts/dev-fast.js", "dev:ultra": "NEXT_CONFIG_FILE=next.config.dev.js GENERATE_SOURCEMAP=false DISABLE_ESLINT_PLUGIN=true NODE_OPTIONS='--max-old-space-size=8192' next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "analyze": "node scripts/analyze-bundle.js", "optimize": "node scripts/optimize-dev.js", "clean": "rm -rf .next node_modules/.cache", "clean:all": "rm -rf .next node_modules/.cache .swc", "reinstall": "rm -rf node_modules package-lock.json && npm install"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "lucide-react": "^0.514.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}