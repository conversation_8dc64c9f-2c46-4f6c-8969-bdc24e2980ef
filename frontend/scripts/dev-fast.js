#!/usr/bin/env node

/**
 * Ultra-fast development server with aggressive optimizations
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting ultra-fast development server...\n');

// Set aggressive performance environment variables
const env = {
  ...process.env,
  // Memory optimizations
  NODE_OPTIONS: '--max-old-space-size=8192 --max-semi-space-size=512',
  
  // Next.js optimizations
  NEXT_TELEMETRY_DISABLED: '1',
  FAST_REFRESH: 'true',
  NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK: 'true',
  NEXT_PRIVATE_SKIP_VALIDATION: 'true',
  
  // Webpack optimizations
  WEB<PERSON>CK_DISABLE_OPTIMIZATION_BAILOUT: 'true',
  NEXT_PRIVATE_DISABLE_WEBPACK_CACHE: 'false',
  NEXT_PRIVATE_WEBPACK_CACHE_TYPE: 'filesystem',
  
  // Development mode
  NODE_ENV: 'development',
  
  // Disable source maps for faster compilation
  GENERATE_SOURCEMAP: 'false',
  
  // Reduce bundle analysis
  ANALYZE: 'false',
  
  // Faster TypeScript checking
  TSC_COMPILE_ON_ERROR: 'true',
  TSC_NONPOLLING_WATCHER: 'true',
  
  // Disable ESLint during development
  DISABLE_ESLINT_PLUGIN: 'true',
  ESLINT_NO_DEV_ERRORS: 'true'
};

console.log('📋 Performance optimizations enabled:');
console.log('   • Increased memory allocation (8GB)');
console.log('   • Disabled telemetry and validation');
console.log('   • Enabled filesystem caching');
console.log('   • Disabled source maps');
console.log('   • Disabled ESLint during development');
console.log('   • Optimized TypeScript checking\n');

// Start Next.js development server
const nextProcess = spawn('npx', ['next', 'dev'], {
  env,
  stdio: 'inherit',
  cwd: process.cwd()
});

nextProcess.on('error', (error) => {
  console.error('❌ Failed to start development server:', error);
  process.exit(1);
});

nextProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ Development server exited with code ${code}`);
    process.exit(code);
  }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development server...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development server...');
  nextProcess.kill('SIGTERM');
});
