# Comments & Collaboration

Comprehensive guide to team collaboration features in AlertAI, including comments, mentions, notifications, and investigation workflows.

## Overview

AlertAI's collaboration system provides:
- **Rich commenting system** with markdown support and @mentions
- **Real-time notifications** for team coordination
- **Investigation workflows** with status tracking and assignment
- **AI-powered assistance** for comment enhancement and analysis
- **Audit trails** for compliance and accountability
- **Role-based access** for secure collaboration

## Comment System Features

### Rich Text Comments
- **Markdown support** for formatted text, links, and code blocks
- **@mentions** to notify specific team members
- **File attachments** for evidence and documentation
- **Threaded discussions** for organized conversations
- **Edit history** with version tracking and audit trails

### Real-time Collaboration
- **Live updates** when new comments are added
- **Typing indicators** to show active participants
- **Read receipts** to track comment visibility
- **Push notifications** for important updates
- **Email notifications** for offline team members

### AI Integration
- **Comment beautification** using AI to improve clarity and grammar
- **Automatic summarization** of long comment threads
- **Sentiment analysis** to identify urgent or concerning comments
- **Translation support** for international teams
- **Smart suggestions** for follow-up actions

## Adding Comments

### Basic Comments

#### Via Web Interface

1. **Navigate to Alert**: Open the alert you want to comment on
2. **Find Comments Section**: Scroll to the comments area
3. **Write Comment**: Use the text editor with markdown support
4. **Add Mentions**: Use @username to notify team members
5. **Submit**: Click "Add Comment" to post

#### Via API

```bash
# Add a basic comment
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "Initial investigation started. Checking network logs for suspicious activity.",
    "is_internal": false
  }'
```

### Advanced Comments

#### Comments with Mentions

```bash
# Comment with team member mentions
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "Found suspicious network traffic. @john.analyst please review the PCAP file. @security.lead escalation may be needed.",
    "mentions": ["john.analyst", "security.lead"],
    "priority": "high"
  }'
```

#### Comments with Attachments

```bash
# Comment with file attachment
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "content=Analysis results attached. See highlighted sections in the report." \
  -F "file=@analysis_report.pdf" \
  -F "mentions=@team.lead"
```

#### Structured Investigation Comments

```bash
# Structured comment for investigation steps
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "## Investigation Update\n\n**Status**: In Progress\n**Findings**:\n- Confirmed C2 communication\n- 5 affected systems identified\n- Persistence mechanism found\n\n**Next Steps**:\n1. Isolate affected systems\n2. Collect memory dumps\n3. Update IOC list",
    "category": "investigation_update",
    "tags": ["c2", "persistence", "containment"]
  }'
```

## Comment Management

### Viewing Comments

#### List Alert Comments

```bash
curl -X GET "http://localhost:8002/api/alerts/123/comments" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "comments": [
    {
      "id": 456,
      "content": "Initial investigation started. Checking network logs.",
      "author": {
        "id": 5,
        "name": "John Analyst",
        "email": "<EMAIL>",
        "role": "analyst"
      },
      "created_at": "2024-12-16T14:30:00Z",
      "updated_at": "2024-12-16T14:30:00Z",
      "is_internal": false,
      "mentions": [],
      "attachments": [],
      "reactions": {
        "👍": 2,
        "👀": 1
      },
      "replies_count": 3
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 50
}
```

#### Get Specific Comment

```bash
curl -X GET "http://localhost:8002/api/comments/456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Updating Comments

#### Edit Comment

```bash
curl -X PUT "http://localhost:8002/api/comments/456" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "Updated: Initial investigation completed. Found evidence of C2 communication.",
    "edit_reason": "Added investigation results"
  }'
```

#### Add Reactions

```bash
curl -X POST "http://localhost:8002/api/comments/456/reactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "reaction": "👍"
  }'
```

### Comment Threading

#### Reply to Comment

```bash
curl -X POST "http://localhost:8002/api/comments/456/replies" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "Great work! Can you also check for lateral movement indicators?",
    "mentions": ["john.analyst"]
  }'
```

#### Get Comment Thread

```bash
curl -X GET "http://localhost:8002/api/comments/456/thread" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## AI-Powered Comment Features

### Comment Beautification

#### Improve Comment Quality

```bash
# Use AI to improve comment clarity and grammar
curl -X POST "http://localhost:8002/api/comments/beautify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "found weird stuff in logs need help asap",
    "style": "professional",
    "expand": true
  }'
```

Response:
```json
{
  "original": "found weird stuff in logs need help asap",
  "beautified": "I have discovered anomalous entries in the system logs that require immediate attention and assistance from the team. The findings suggest potential security concerns that need urgent investigation.",
  "improvements": [
    "Enhanced professional tone",
    "Expanded abbreviations",
    "Improved clarity and specificity",
    "Added context for urgency"
  ]
}
```

### Automatic Summarization

#### Summarize Comment Thread

```bash
# Generate summary of long comment threads
curl -X POST "http://localhost:8002/api/alerts/123/comments/summarize" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "include_actions": true,
    "highlight_decisions": true,
    "format": "executive"
  }'
```

### Smart Suggestions

#### Get Follow-up Suggestions

```bash
# Get AI suggestions for next steps
curl -X POST "http://localhost:8002/api/comments/456/suggestions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "suggestions": [
    {
      "type": "investigation_step",
      "content": "Check for persistence mechanisms in registry and scheduled tasks",
      "priority": "high",
      "estimated_time": "30 minutes"
    },
    {
      "type": "escalation",
      "content": "Consider escalating to incident response team due to C2 communication",
      "priority": "medium",
      "stakeholders": ["incident.response", "security.manager"]
    }
  ]
}
```

## Collaboration Workflows

### Investigation Workflow

#### Standard Investigation Process

1. **Initial Assessment**
   ```bash
   # Analyst adds initial findings
   curl -X POST "http://localhost:8002/api/alerts/123/comments" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer ANALYST_TOKEN" \
     -d '{
       "content": "## Initial Assessment\n\n**Alert Type**: Network Anomaly\n**Severity**: High\n**Initial Findings**: Suspicious outbound connections detected\n\n**Immediate Actions Taken**:\n- Isolated affected system\n- Preserved logs\n- Notified team lead",
       "category": "initial_assessment",
       "mentions": ["team.lead"]
     }'
   ```

2. **Deep Dive Investigation**
   ```bash
   # Senior analyst provides detailed analysis
   curl -X POST "http://localhost:8002/api/alerts/123/comments" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer SENIOR_ANALYST_TOKEN" \
     -d '{
       "content": "## Deep Dive Analysis\n\n**Threat Actor**: Likely APT29 based on TTPs\n**Attack Vector**: Spear phishing email\n**Persistence**: Registry modifications confirmed\n**Exfiltration**: 2.3GB data staged for exfiltration\n\n**Evidence**:\n- Network PCAP attached\n- Memory dump analysis complete\n- IOCs extracted and shared\n\<EMAIL> please coordinate containment",
       "category": "detailed_analysis",
       "mentions": ["incident.response"],
       "tags": ["apt29", "exfiltration", "containment"]
     }'
   ```

3. **Resolution and Lessons Learned**
   ```bash
   # Document resolution and lessons learned
   curl -X POST "http://localhost:8002/api/alerts/123/comments" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer TEAM_LEAD_TOKEN" \
     -d '{
       "content": "## Incident Resolution\n\n**Status**: Resolved\n**Resolution Time**: 4.5 hours\n**Impact**: Contained before data exfiltration\n\n**Actions Taken**:\n- All affected systems reimaged\n- Network IOCs blocked\n- Email security rules updated\n\n**Lessons Learned**:\n- Need faster initial response\n- Email filtering requires enhancement\n- Staff training on phishing recognition\n\n**Follow-up Actions**:\n- [ ] Update detection rules\n- [ ] Schedule security awareness training\n- [ ] Review email security policies",
       "category": "resolution",
       "is_final": true
     }'
   ```

### Escalation Workflow

#### Escalation Process

```bash
# Escalate alert with detailed context
curl -X POST "http://localhost:8002/api/alerts/123/escalate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "escalation_level": "management",
    "reason": "Potential data breach - immediate executive notification required",
    "stakeholders": ["ciso", "legal.team", "pr.team"],
    "urgency": "critical",
    "comment": "Critical incident requiring immediate C-level attention. Potential regulatory notification required within 72 hours."
  }'
```

### Shift Handover

#### Handover Documentation

```bash
# Document shift handover
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "## Shift Handover - Night Shift to Day Shift\n\n**Current Status**: Investigation in progress\n**Priority Actions for Day Shift**:\n1. Complete memory dump analysis (ETA: 2 hours)\n2. Coordinate with legal team on breach notification\n3. Follow up with affected users\n\n**Key Contacts**:\n- Legal: ext. 5555\n- PR: ext. 6666\n- Executive team: Already notified\n\n**Next Update Due**: 10:00 AM\n\<EMAIL> @senior.analyst",
    "category": "shift_handover",
    "mentions": ["day.shift.lead", "senior.analyst"],
    "priority": "high"
  }'
```

## Notification Management

### Notification Settings

#### Configure Personal Notifications

```bash
# Update notification preferences
curl -X PUT "http://localhost:8002/api/users/me/notifications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "email_notifications": {
      "mentions": true,
      "high_priority_comments": true,
      "escalations": true,
      "daily_digest": false
    },
    "push_notifications": {
      "mentions": true,
      "assigned_alerts": true,
      "urgent_updates": true
    },
    "notification_schedule": {
      "start_time": "08:00",
      "end_time": "18:00",
      "timezone": "America/New_York",
      "weekend_notifications": false
    }
  }'
```

#### Team Notification Rules

```bash
# Configure team-wide notification rules
curl -X POST "http://localhost:8002/api/admin/notifications/rules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "rule_name": "Critical Alert Notifications",
    "conditions": {
      "alert_severity": ["critical", "high"],
      "comment_categories": ["escalation", "urgent"]
    },
    "actions": {
      "notify_roles": ["admin", "team_lead"],
      "send_email": true,
      "send_sms": true,
      "create_ticket": true
    }
  }'
```

## Comment Analytics

### Comment Statistics

```bash
# Get comment analytics for alerts
curl -X GET "http://localhost:8002/api/analytics/comments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -G \
  -d "period=30d" \
  -d "group_by=user,category"
```

Response:
```json
{
  "total_comments": 1250,
  "average_per_alert": 3.2,
  "response_time": {
    "average": "45 minutes",
    "median": "25 minutes"
  },
  "by_category": {
    "investigation_update": 450,
    "initial_assessment": 300,
    "resolution": 200,
    "escalation": 50
  },
  "by_user": {
    "john.analyst": 320,
    "jane.senior": 280,
    "team.lead": 150
  },
  "engagement_metrics": {
    "mentions_per_comment": 1.8,
    "reactions_per_comment": 2.1,
    "replies_per_comment": 0.7
  }
}
```

## Best Practices

### Effective Communication
1. **Use clear, descriptive language** in comments
2. **Include relevant context** and background information
3. **Tag comments appropriately** for easy categorization
4. **Mention relevant team members** for coordination
5. **Use markdown formatting** for better readability

### Investigation Documentation
1. **Document all investigation steps** for audit trails
2. **Include timestamps and evidence** for accuracy
3. **Use structured formats** for consistency
4. **Link to relevant files and resources**
5. **Summarize findings clearly** for stakeholders

### Team Collaboration
1. **Respond promptly to mentions** and urgent comments
2. **Use appropriate notification settings** to avoid spam
3. **Escalate when necessary** with clear reasoning
4. **Maintain professional tone** in all communications
5. **Follow established workflows** for consistency

## Troubleshooting

### Comment Issues

```bash
# Check comment permissions
curl -X GET "http://localhost:8002/api/alerts/123/permissions" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Verify mention notifications
curl -X GET "http://localhost:8002/api/users/me/mentions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Notification Issues

```bash
# Test notification delivery
curl -X POST "http://localhost:8002/api/notifications/test" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"type": "email", "message": "Test notification"}'

# Check notification history
curl -X GET "http://localhost:8002/api/users/me/notifications/history" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Next Steps

1. **[Alert Management](../alerts/README.md)** - Learn how comments integrate with alerts
2. **[File Management](../files/README.md)** - Attach files to comments
3. **[AI Assistant (Bitzy)](../bitzy/README.md)** - Use AI for comment enhancement
4. **[API Reference](../api/comments.md)** - Complete comments API documentation

---

*For API details, see [Comments API](../api/comments.md)*
