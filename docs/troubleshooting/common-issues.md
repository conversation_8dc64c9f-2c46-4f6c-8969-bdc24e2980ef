# Common Issues and Solutions

Comprehensive troubleshooting guide for common AlertAI issues and their solutions.

## Overview

This guide covers the most frequently encountered issues in AlertAI and provides step-by-step solutions for quick resolution.

## Installation Issues

### 1. Python Dependencies

#### "ModuleNotFoundError" Errors

**Symptoms:**
- Import errors when starting the backend
- Missing package errors during installation
- Version compatibility issues

**Solutions:**

```bash
# Ensure you're in the correct virtual environment
cd backend
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# Update pip and install dependencies
pip install --upgrade pip
pip install -r requirements.txt

# If specific package fails, install individually
pip install fastapi uvicorn sqlalchemy

# Check Python version compatibility
python --version  # Should be 3.9+
```

#### Virtual Environment Issues

```bash
# Recreate virtual environment
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 2. Node.js Dependencies

#### "npm install" Failures

**Symptoms:**
- Package installation fails
- Version conflicts
- Permission errors

**Solutions:**

```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Use specific Node.js version
nvm use 18  # If using nvm
node --version  # Should be 18+

# Fix permission issues (Linux/macOS)
sudo chown -R $(whoami) ~/.npm
```

## Service Startup Issues

### 1. Backend Won't Start

#### Port Already in Use

**Symptoms:**
- "Address already in use" error
- Cannot bind to port 8002
- Service fails to start

**Solutions:**

```bash
# Check what's using the port
lsof -i :8002
netstat -tulpn | grep :8002

# Kill process using the port
sudo kill -9 $(lsof -t -i:8002)

# Use different port
export API_PORT=8003
python start_server.py
```

#### Database Connection Errors

**Symptoms:**
- "Database connection failed"
- SQLAlchemy errors
- Cannot create tables

**Solutions:**

```bash
# Check database file permissions (SQLite)
ls -la backend/alertai.db
chmod 664 backend/alertai.db

# Test database connection
python3 -c "
from app.db.connection import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT 1'))
    print('Database OK')
"

# Recreate database
rm backend/alertai.db
python -c "
from app.db.connection import init_db
init_db()
"
```

### 2. Frontend Won't Start

#### Build Errors

**Symptoms:**
- TypeScript compilation errors
- Missing dependencies
- Build process fails

**Solutions:**

```bash
# Check Node.js and npm versions
node --version  # Should be 18+
npm --version

# Clear build cache
rm -rf .next
npm run build

# Fix TypeScript errors
npm run type-check

# Install missing dependencies
npm install
```

#### Environment Variable Issues

```bash
# Check environment file exists
ls -la frontend/.env.local

# Verify environment variables
cat frontend/.env.local

# Create missing environment file
cp frontend/.env.example frontend/.env.local
```

## Authentication Issues

### 1. Cannot Login

#### System Not Initialized

**Symptoms:**
- "System is not initialized" error
- No admin user exists
- Fresh installation issues

**Solution:**

```bash
# Initialize system with admin user
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

#### Invalid Credentials

**Symptoms:**
- Correct credentials rejected
- "Invalid email or password" error
- User exists but cannot login

**Solutions:**

```bash
# Check if user is active
curl -X GET "http://localhost:8002/api/users?search=<EMAIL>" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Reset user password
curl -X POST "http://localhost:8002/api/users/{user_id}/reset-password" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "temporary_password": "TempPassword123!",
    "force_change": true
  }'
```

### 2. Token Issues

#### Token Expired

**Symptoms:**
- Frequent logouts
- "Token has expired" errors
- API calls fail with 401

**Solutions:**

```bash
# Check token expiration setting
grep ACCESS_TOKEN_EXPIRE_MINUTES backend/.env

# Increase token lifetime (in .env)
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Get new token
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=password"
```

## API Issues

### 1. CORS Errors

#### Cross-Origin Request Blocked

**Symptoms:**
- Browser console shows CORS errors
- API calls fail from frontend
- "Access-Control-Allow-Origin" errors

**Solutions:**

```bash
# Check CORS configuration
grep FRONTEND_URLS backend/.env

# Update CORS origins (in .env)
FRONTEND_URLS=http://localhost:3001,https://yourdomain.com

# Restart backend service
```

### 2. Rate Limiting

#### Too Many Requests

**Symptoms:**
- 429 "Too Many Requests" errors
- API calls blocked temporarily
- Rate limit headers in response

**Solutions:**

```bash
# Check rate limit settings
grep RATE_LIMIT backend/.env

# Increase rate limits (in .env)
RATE_LIMIT_REQUESTS=200
RATE_LIMIT_WINDOW=60

# Wait for rate limit window to reset
# Or contact admin to reset rate limit
```

## File Upload Issues

### 1. Upload Failures

#### File Type Not Allowed

**Symptoms:**
- "File type not allowed" error
- Upload rejected by server
- Specific file types fail

**Solutions:**

```bash
# Check allowed file types
grep ALLOWED_FILE_TYPES backend/.env

# Add file type (in .env)
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/plain,application/json

# Restart backend service
```

#### File Size Too Large

**Symptoms:**
- "File too large" error
- Upload progress stops
- Large files rejected

**Solutions:**

```bash
# Check file size limit
grep MAX_FILE_SIZE backend/.env

# Increase file size limit (in .env)
MAX_FILE_SIZE=52428800  # 50MB

# Check disk space
df -h
```

### 2. Storage Issues

#### Upload Directory Permissions

**Symptoms:**
- "Permission denied" errors
- Files upload but cannot be accessed
- Storage errors in logs

**Solutions:**

```bash
# Check upload directory
ls -la backend/uploads/

# Fix permissions
sudo chown -R alertai:alertai backend/uploads/
sudo chmod -R 755 backend/uploads/

# Create upload directory if missing
mkdir -p backend/uploads
```

## AI Assistant (Bitzy) Issues

### 1. Ollama Connection

#### AI Service Not Available

**Symptoms:**
- "AI service unavailable" error
- Bitzy doesn't respond
- Connection timeout errors

**Solutions:**

```bash
# Check if Ollama is running
ollama ps

# Start Ollama service
ollama serve &

# Check Ollama connection
curl http://localhost:11434/api/tags

# Verify model is available
ollama list | grep deepseek-r1:8b

# Pull model if missing
ollama pull deepseek-r1:8b
```

### 2. Slow AI Responses

#### Performance Issues

**Symptoms:**
- Very slow AI responses
- Timeout errors
- High CPU/memory usage

**Solutions:**

```bash
# Check system resources
htop
free -h

# Use smaller model
ollama pull deepseek-r1:1.5b

# Update model in .env
OLLAMA_MODEL=deepseek-r1:1.5b

# Enable GPU acceleration (if available)
nvidia-smi  # Check GPU availability
```

## Database Issues

### 1. SQLite Issues

#### Database Locked

**Symptoms:**
- "Database is locked" errors
- Operations timeout
- Concurrent access issues

**Solutions:**

```bash
# Check for zombie processes
ps aux | grep python

# Kill hanging processes
sudo pkill -f "python.*alertai"

# Check database file
lsof backend/alertai.db

# Restart application
python start_server.py
```

#### Corruption Issues

**Symptoms:**
- "Database disk image is malformed"
- Data inconsistencies
- Query errors

**Solutions:**

```bash
# Backup current database
cp backend/alertai.db backend/alertai.db.backup

# Check database integrity
sqlite3 backend/alertai.db "PRAGMA integrity_check;"

# Repair database
sqlite3 backend/alertai.db ".recover" | sqlite3 backend/alertai_recovered.db

# Replace with recovered database
mv backend/alertai_recovered.db backend/alertai.db
```

### 2. PostgreSQL Issues

#### Connection Refused

**Symptoms:**
- "Connection refused" errors
- Cannot connect to PostgreSQL
- Database service down

**Solutions:**

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Start PostgreSQL
sudo systemctl start postgresql

# Check connection
psql -h localhost -U alertai_user -d alertai

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## Performance Issues

### 1. Slow Response Times

#### High CPU Usage

**Symptoms:**
- Slow API responses
- High server load
- Timeouts

**Solutions:**

```bash
# Check system resources
top
htop

# Check Python processes
ps aux | grep python

# Optimize database queries
# Add indexes for frequently queried fields

# Scale horizontally
# Add more worker processes in production
```

#### Memory Issues

**Symptoms:**
- Out of memory errors
- Application crashes
- Swap usage high

**Solutions:**

```bash
# Check memory usage
free -h
cat /proc/meminfo

# Restart application
sudo systemctl restart alertai

# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## Network Issues

### 1. Connectivity Problems

#### Cannot Reach API

**Symptoms:**
- Frontend cannot connect to backend
- Network timeouts
- Connection refused errors

**Solutions:**

```bash
# Check if backend is running
curl http://localhost:8002/api/health

# Check firewall rules
sudo ufw status
sudo iptables -L

# Check network connectivity
ping localhost
telnet localhost 8002

# Check DNS resolution
nslookup yourdomain.com
```

### 2. SSL/TLS Issues

#### Certificate Problems

**Symptoms:**
- SSL certificate errors
- "Insecure connection" warnings
- HTTPS not working

**Solutions:**

```bash
# Check certificate validity
openssl x509 -in /path/to/cert.pem -text -noout

# Renew Let's Encrypt certificate
sudo certbot renew

# Check certificate expiration
echo | openssl s_client -connect yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates
```

## Diagnostic Tools

### Health Check Script

```bash
#!/bin/bash
# health_check.sh

echo "🏥 AlertAI Health Check"
echo "====================="

# Check backend
echo "1. Backend Health..."
if curl -s http://localhost:8002/api/health | grep -q "healthy"; then
  echo "✅ Backend is healthy"
else
  echo "❌ Backend is not responding"
fi

# Check frontend
echo "2. Frontend Health..."
if curl -s http://localhost:3001 > /dev/null; then
  echo "✅ Frontend is accessible"
else
  echo "❌ Frontend is not accessible"
fi

# Check database
echo "3. Database Health..."
cd backend
if python3 -c "from app.db.connection import engine; engine.connect()" 2>/dev/null; then
  echo "✅ Database is connected"
else
  echo "❌ Database connection failed"
fi

# Check AI service
echo "4. AI Service Health..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
  echo "✅ AI service is available"
else
  echo "⚠️ AI service is not available (optional)"
fi

echo "====================="
```

### Log Analysis

```bash
# View recent errors
grep -i error backend/logs/application.log | tail -10

# Monitor logs in real-time
tail -f backend/logs/application.log

# Check authentication logs
grep -E "(login|auth)" backend/logs/security.log | tail -10

# Check rate limiting
grep "rate_limit" backend/logs/security.log | tail -10
```

### System Information

```bash
# System information script
echo "System Information:"
echo "OS: $(uname -a)"
echo "Python: $(python3 --version)"
echo "Node.js: $(node --version)"
echo "Memory: $(free -h | grep Mem)"
echo "Disk: $(df -h / | tail -1)"
echo "Load: $(uptime)"
```

## Getting Help

### Before Contacting Support

1. **Check logs** for error messages
2. **Run health checks** to identify issues
3. **Try basic solutions** like restarting services
4. **Check documentation** for similar issues
5. **Gather system information** for support

### Information to Include

When reporting issues, include:

- **Error messages** (exact text)
- **Steps to reproduce** the issue
- **System information** (OS, versions)
- **Log excerpts** (sanitize sensitive data)
- **Configuration details** (sanitize secrets)

### Community Resources

- **GitHub Issues**: Search existing issues
- **Documentation**: Check relevant guides
- **API Reference**: Verify API usage
- **Security Guide**: Review security settings

---

*For specific issues, see specialized troubleshooting guides:*
- *[Authentication Troubleshooting](../authentication/troubleshooting.md)*
- *[Performance Issues](./performance.md)*
- *[Security Issues](../authentication/security.md)*
