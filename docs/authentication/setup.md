# Authentication Setup Guide

This guide walks you through setting up authentication and security for AlertAI from initial installation to production deployment.

## Prerequisites

- AlertAI backend running on port 8002
- Access to the server environment
- Administrative privileges for system configuration

## Step 1: Environment Configuration

### 1.1 Create Environment File

Create a `.env` file in the backend directory:

```bash
cd backend
cp .env.example .env
```

### 1.2 Configure Authentication Settings

Edit the `.env` file with your security settings:

```bash
# CRITICAL: Change this secret key for production
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-long

# Token expiration (in minutes)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8002
DEBUG=false

# Database (use PostgreSQL for production)
DATABASE_URL=sqlite:///./alertai.db
# DATABASE_URL=postgresql://username:password@localhost:5432/alertai

# CORS Configuration
FRONTEND_URLS=http://localhost:3001,https://yourdomain.com

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Logging
LOG_LEVEL=INFO
ENABLE_AUDIT_LOGGING=true
```

### 1.3 Security Best Practices

**🔒 Critical Security Settings:**

1. **SECRET_KEY**: Must be at least 32 characters long and cryptographically secure
   ```bash
   # Generate a secure secret key
   python -c "import secrets; print(secrets.token_urlsafe(32))"
   ```

2. **DEBUG**: Always set to `false` in production
3. **CORS_ORIGINS**: Restrict to your actual domain(s)
4. **Database**: Use PostgreSQL for production environments

## Step 2: System Initialization

### 2.1 Start the Server

```bash
cd backend
python start_server.py
```

Verify the server is running:
```bash
curl http://localhost:8002/
```

Expected response:
```json
{
  "message": "AlertAI API is running",
  "version": "1.0.0",
  "docs_url": null
}
```

### 2.2 Initialize the System

Create the first admin user:

```bash
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

**Password Requirements:**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- Special characters recommended

### 2.3 Verify Initialization

Test the admin login:

```bash
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=SecureAdminPassword123!"
```

Save the returned token for subsequent requests.

## Step 3: User Management Setup

### 3.1 Create Additional Users

Register a security analyst:

```bash
curl -X POST "http://localhost:8002/api/auth/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "AnalystPassword123!",
    "full_name": "Security Analyst",
    "role": "analyst"
  }'
```

Register a viewer:

```bash
curl -X POST "http://localhost:8002/api/auth/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "ViewerPassword123!",
    "full_name": "Security Viewer",
    "role": "viewer"
  }'
```

### 3.2 Verify User Creation

List all users:

```bash
curl -X GET "http://localhost:8002/api/users" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Step 4: Security Configuration

### 4.1 Configure Rate Limiting

Adjust rate limits based on your usage patterns:

```bash
# For high-traffic environments
RATE_LIMIT_REQUESTS=500
RATE_LIMIT_WINDOW=60

# For development environments
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# For restricted environments
RATE_LIMIT_REQUESTS=50
RATE_LIMIT_WINDOW=60
```

### 4.2 Configure Audit Logging

Enable comprehensive audit logging:

```bash
# Enable audit logging
ENABLE_AUDIT_LOGGING=true

# Set log retention (days)
LOG_RETENTION_DAYS=90

# Configure log level
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
```

### 4.3 File Upload Security

Configure secure file uploads:

```bash
# Maximum file size (10MB)
MAX_FILE_SIZE=10485760

# Allowed file types
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/plain,application/json

# Upload directory
UPLOAD_DIRECTORY=/var/alertai/uploads
```

## Step 5: Testing Authentication

### 5.1 Test Authentication Flow

Create a test script to verify the complete authentication flow:

```bash
#!/bin/bash

BASE_URL="http://localhost:8002"

echo "Testing AlertAI Authentication..."

# Test 1: Login
echo "1. Testing login..."
TOKEN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=SecureAdminPassword123!")

TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

if [ "$TOKEN" != "null" ]; then
  echo "✅ Login successful"
else
  echo "❌ Login failed"
  exit 1
fi

# Test 2: Get user info
echo "2. Testing user info..."
USER_RESPONSE=$(curl -s -X GET "$BASE_URL/api/auth/me" \
  -H "Authorization: Bearer $TOKEN")

EMAIL=$(echo $USER_RESPONSE | jq -r '.email')

if [ "$EMAIL" = "<EMAIL>" ]; then
  echo "✅ User info retrieved"
else
  echo "❌ User info failed"
  exit 1
fi

# Test 3: Test protected endpoint
echo "3. Testing protected endpoint..."
ALERTS_RESPONSE=$(curl -s -X GET "$BASE_URL/api/alerts" \
  -H "Authorization: Bearer $TOKEN")

if [ $? -eq 0 ]; then
  echo "✅ Protected endpoint accessible"
else
  echo "❌ Protected endpoint failed"
  exit 1
fi

# Test 4: Test without authentication
echo "4. Testing without authentication..."
UNAUTH_RESPONSE=$(curl -s -X GET "$BASE_URL/api/alerts")

if echo $UNAUTH_RESPONSE | grep -q "detail"; then
  echo "✅ Authentication required (as expected)"
else
  echo "❌ Authentication not enforced"
  exit 1
fi

echo "🎉 All authentication tests passed!"
```

### 5.2 Test Rate Limiting

Test rate limiting functionality:

```bash
#!/bin/bash

BASE_URL="http://localhost:8002"

echo "Testing rate limiting..."

# Make rapid requests to trigger rate limiting
for i in {1..25}; do
  RESPONSE=$(curl -s -w "%{http_code}" -X GET "$BASE_URL/api/health")
  HTTP_CODE="${RESPONSE: -3}"
  
  if [ "$HTTP_CODE" = "429" ]; then
    echo "✅ Rate limiting triggered at request $i"
    break
  fi
  
  if [ $i -eq 25 ]; then
    echo "⚠️ Rate limiting not triggered (may need adjustment)"
  fi
done
```

## Step 6: Production Hardening

### 6.1 Database Migration

For production, migrate to PostgreSQL:

```bash
# Install PostgreSQL dependencies
pip install psycopg2-binary

# Update DATABASE_URL
DATABASE_URL=postgresql://alertai_user:secure_password@localhost:5432/alertai
```

### 6.2 HTTPS Configuration

Configure HTTPS for production:

```bash
# Update CORS origins for HTTPS
FRONTEND_URLS=https://yourdomain.com,https://app.yourdomain.com

# Ensure secure headers are enabled
DEBUG=false
```

### 6.3 Monitoring Setup

Configure monitoring and alerting:

```bash
# Enable detailed logging
LOG_LEVEL=INFO
ENABLE_AUDIT_LOGGING=true

# Set up log rotation
LOG_RETENTION_DAYS=90
```

## Troubleshooting

### Common Issues

1. **"System is already initialized"**
   - The system has already been set up
   - Use the existing admin credentials or reset the database

2. **"Invalid token"**
   - Token may have expired (default: 30 minutes)
   - Get a new token using the login endpoint

3. **"Rate limit exceeded"**
   - Too many requests in the time window
   - Wait for the window to reset or adjust rate limits

4. **"Authentication required"**
   - Include the `Authorization: Bearer <token>` header
   - Ensure the token is valid and not expired

### Verification Commands

```bash
# Check server status
curl http://localhost:8002/api/health

# Verify authentication is working
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=YOUR_PASSWORD"

# Test protected endpoint
curl -X GET "http://localhost:8002/api/alerts" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Next Steps

1. **[User Management](./users.md)** - Add more users and configure roles
2. **[API Authentication](./api.md)** - Integrate with external systems
3. **[Security Best Practices](./security.md)** - Secure your production deployment
4. **[Audit Logging](./audit.md)** - Monitor and analyze security events

---

*For troubleshooting authentication issues, see [Authentication Troubleshooting](./troubleshooting.md)*
