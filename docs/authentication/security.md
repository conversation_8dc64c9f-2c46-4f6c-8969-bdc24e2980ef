# Security Best Practices

Comprehensive security guidelines for deploying and maintaining AlertAI in production environments.

## Overview

AlertAI implements enterprise-grade security features designed to protect your security operations and ensure compliance with industry standards. This guide covers best practices for securing your AlertAI deployment.

## Production Security Checklist

### ✅ Essential Security Configuration

#### 1. Authentication Security
- [ ] **Change default SECRET_KEY** to a cryptographically secure value (minimum 32 characters)
- [ ] **Use strong passwords** for all user accounts (minimum 8 characters, complexity requirements)
- [ ] **Configure token expiration** appropriately for your security requirements (default: 30 minutes)
- [ ] **Enable audit logging** for all authentication events
- [ ] **Implement account lockout** policies for failed login attempts

#### 2. Network Security
- [ ] **Enable HTTPS/TLS** for all communications (TLS 1.2 minimum, TLS 1.3 recommended)
- [ ] **Configure proper CORS** origins to restrict cross-origin requests
- [ ] **Use reverse proxy** (Nginx/Apache) with security headers
- [ ] **Implement firewall rules** to restrict access to application ports
- [ ] **Enable rate limiting** to prevent abuse and DoS attacks

#### 3. Database Security
- [ ] **Use PostgreSQL** for production (not SQLite)
- [ ] **Create dedicated database user** with minimal required privileges
- [ ] **Enable database encryption** at rest and in transit
- [ ] **Configure database backups** with encryption
- [ ] **Implement database access controls** and monitoring

#### 4. File Security
- [ ] **Configure file type restrictions** to prevent malicious uploads
- [ ] **Set appropriate file size limits** to prevent storage abuse
- [ ] **Enable file scanning** for malware detection (if available)
- [ ] **Secure file storage** with proper permissions and access controls
- [ ] **Implement file retention policies** for compliance

## Environment Configuration

### Production Environment Variables

```bash
# CRITICAL: Security Settings
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-long
DEBUG=false
LOG_LEVEL=INFO

# Database Security
DATABASE_URL=postgresql://alertai_user:secure_password@localhost:5432/alertai

# Network Security
API_HOST=127.0.0.1  # Bind to localhost only, use reverse proxy
FRONTEND_URLS=https://yourdomain.com,https://app.yourdomain.com

# Authentication Security
ACCESS_TOKEN_EXPIRE_MINUTES=30
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Security
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/plain
UPLOAD_DIRECTORY=/var/alertai/uploads

# Audit and Compliance
ENABLE_AUDIT_LOGGING=true
LOG_RETENTION_DAYS=2555  # 7 years for compliance
```

### Secure Secret Key Generation

```bash
# Generate a cryptographically secure secret key
python3 -c "
import secrets
import string
alphabet = string.ascii_letters + string.digits + '!@#$%^&*'
secret_key = ''.join(secrets.choice(alphabet) for _ in range(64))
print(f'SECRET_KEY={secret_key}')
"
```

## Network Security

### HTTPS/TLS Configuration

#### SSL Certificate Setup

```bash
# Using Let's Encrypt (recommended)
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Using custom certificates
sudo mkdir -p /etc/ssl/alertai
sudo cp your-cert.pem /etc/ssl/alertai/cert.pem
sudo cp your-key.pem /etc/ssl/alertai/key.pem
sudo chmod 600 /etc/ssl/alertai/*
sudo chown root:root /etc/ssl/alertai/*
```

#### Nginx Security Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/alertai/cert.pem;
    ssl_certificate_key /etc/ssl/alertai/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # API endpoints with rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=10 nodelay;
        proxy_pass http://127.0.0.1:8002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://127.0.0.1:8002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Frontend
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

### Firewall Configuration

#### Ubuntu/Debian (UFW)

```bash
# Reset firewall rules
sudo ufw --force reset

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (change port if using non-standard)
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Deny direct access to application ports
sudo ufw deny 3001
sudo ufw deny 8002

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

#### CentOS/RHEL (firewalld)

```bash
# Configure firewall zones
sudo firewall-cmd --permanent --zone=public --add-service=http
sudo firewall-cmd --permanent --zone=public --add-service=https
sudo firewall-cmd --permanent --zone=public --add-service=ssh

# Block application ports
sudo firewall-cmd --permanent --zone=public --add-rich-rule='rule port port="3001" protocol="tcp" reject'
sudo firewall-cmd --permanent --zone=public --add-rich-rule='rule port port="8002" protocol="tcp" reject'

# Reload configuration
sudo firewall-cmd --reload

# Check status
sudo firewall-cmd --list-all
```

## Database Security

### PostgreSQL Security Configuration

#### Create Secure Database User

```sql
-- Connect as postgres superuser
sudo -u postgres psql

-- Create database and user
CREATE DATABASE alertai;
CREATE USER alertai_user WITH PASSWORD 'very_secure_password_here';

-- Grant minimal required privileges
GRANT CONNECT ON DATABASE alertai TO alertai_user;
\c alertai
GRANT USAGE ON SCHEMA public TO alertai_user;
GRANT CREATE ON SCHEMA public TO alertai_user;

-- Revoke unnecessary privileges
REVOKE ALL ON SCHEMA public FROM PUBLIC;

-- Exit
\q
```

#### PostgreSQL Configuration (postgresql.conf)

```bash
# Connection settings
listen_addresses = 'localhost'
port = 5432
max_connections = 100

# SSL settings
ssl = on
ssl_cert_file = '/etc/ssl/certs/postgresql.crt'
ssl_key_file = '/etc/ssl/private/postgresql.key'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'

# Logging
log_connections = on
log_disconnections = on
log_statement = 'mod'
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Security
password_encryption = scram-sha-256
```

#### PostgreSQL Host-Based Authentication (pg_hba.conf)

```bash
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   alertai         alertai_user                            scram-sha-256
host    alertai         alertai_user    127.0.0.1/32            scram-sha-256
host    alertai         alertai_user    ::1/128                 scram-sha-256
```

## Application Security

### File Upload Security

#### Secure Upload Configuration

```python
# File type validation
ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/plain',
    'application/json',
    'application/vnd.tcpdump.pcap'
]

# File size limits (in bytes)
MAX_FILE_SIZES = {
    'image/*': 10 * 1024 * 1024,      # 10MB for images
    'application/pdf': 25 * 1024 * 1024,  # 25MB for PDFs
    'text/*': 5 * 1024 * 1024,        # 5MB for text files
    'application/vnd.tcpdump.pcap': 100 * 1024 * 1024  # 100MB for PCAP files
}
```

#### File Storage Security

```bash
# Create secure upload directory
sudo mkdir -p /var/alertai/uploads
sudo chown alertai:alertai /var/alertai/uploads
sudo chmod 750 /var/alertai/uploads

# Set up file permissions
sudo find /var/alertai/uploads -type f -exec chmod 640 {} \;
sudo find /var/alertai/uploads -type d -exec chmod 750 {} \;
```

### Input Validation and Sanitization

#### API Input Validation

```python
# Example validation rules
VALIDATION_RULES = {
    'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'password': r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
    'alert_title': r'^[a-zA-Z0-9\s\-_.,!?()]{1,200}$',
    'ip_address': r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
}
```

## Monitoring and Logging

### Security Event Monitoring

#### Log Configuration

```bash
# Create log directories
sudo mkdir -p /var/log/alertai
sudo chown alertai:alertai /var/log/alertai
sudo chmod 750 /var/log/alertai

# Configure log rotation
sudo tee /etc/logrotate.d/alertai << EOF
/var/log/alertai/*.log {
    daily
    missingok
    rotate 365
    compress
    delaycompress
    notifempty
    create 640 alertai alertai
    postrotate
        systemctl reload alertai
    endscript
}
EOF
```

#### Security Monitoring Script

```bash
#!/bin/bash
# /usr/local/bin/alertai-security-monitor.sh

LOG_FILE="/var/log/alertai/security.log"
ALERT_THRESHOLD=10

# Monitor failed login attempts
FAILED_LOGINS=$(grep "login_failed" $LOG_FILE | grep "$(date '+%Y-%m-%d')" | wc -l)

if [ $FAILED_LOGINS -gt $ALERT_THRESHOLD ]; then
    echo "ALERT: $FAILED_LOGINS failed login attempts detected today" | \
    mail -s "AlertAI Security Alert" <EMAIL>
fi

# Monitor rate limit violations
RATE_LIMIT_VIOLATIONS=$(grep "rate_limit_exceeded" $LOG_FILE | grep "$(date '+%Y-%m-%d')" | wc -l)

if [ $RATE_LIMIT_VIOLATIONS -gt $ALERT_THRESHOLD ]; then
    echo "ALERT: $RATE_LIMIT_VIOLATIONS rate limit violations detected today" | \
    mail -s "AlertAI Rate Limit Alert" <EMAIL>
fi
```

### Intrusion Detection

#### Fail2Ban Configuration

```bash
# Install Fail2Ban
sudo apt install fail2ban  # Ubuntu/Debian
sudo yum install fail2ban   # CentOS/RHEL

# Create AlertAI jail configuration
sudo tee /etc/fail2ban/jail.d/alertai.conf << EOF
[alertai-auth]
enabled = true
port = 443,80
protocol = tcp
filter = alertai-auth
logpath = /var/log/alertai/security.log
maxretry = 5
bantime = 3600
findtime = 600

[alertai-api]
enabled = true
port = 443,80
protocol = tcp
filter = alertai-api
logpath = /var/log/alertai/application.log
maxretry = 20
bantime = 1800
findtime = 300
EOF

# Create filter for authentication failures
sudo tee /etc/fail2ban/filter.d/alertai-auth.conf << EOF
[Definition]
failregex = .*login_failed.*ip_address.*<HOST>.*
ignoreregex =
EOF

# Create filter for API abuse
sudo tee /etc/fail2ban/filter.d/alertai-api.conf << EOF
[Definition]
failregex = .*rate_limit_exceeded.*ip_address.*<HOST>.*
ignoreregex =
EOF

# Restart Fail2Ban
sudo systemctl restart fail2ban
```

## Compliance and Auditing

### Audit Log Configuration

#### Comprehensive Audit Logging

```python
# Audit events to log
AUDIT_EVENTS = [
    'user_login',
    'user_logout',
    'user_created',
    'user_updated',
    'user_deleted',
    'password_changed',
    'role_changed',
    'alert_created',
    'alert_updated',
    'alert_deleted',
    'file_uploaded',
    'file_downloaded',
    'file_deleted',
    'permission_granted',
    'permission_revoked',
    'system_configuration_changed'
]
```

#### Audit Log Retention

```bash
# Configure audit log retention
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years for compliance

# Automated cleanup script
#!/bin/bash
# /usr/local/bin/alertai-audit-cleanup.sh

find /var/log/alertai/audit/ -name "*.log" -mtime +$AUDIT_LOG_RETENTION_DAYS -delete
```

### Compliance Reporting

#### Generate Compliance Report

```bash
# Generate monthly compliance report
curl -X POST "https://api.yourdomain.com/api/audit/export" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "start_date": "2024-12-01",
    "end_date": "2024-12-31",
    "format": "csv",
    "include_events": ["user_login", "user_logout", "permission_changes"],
    "compliance_standard": "SOC2"
  }' \
  -o "compliance_report_$(date +%Y%m).csv"
```

## Incident Response

### Security Incident Procedures

#### 1. Immediate Response

```bash
# Disable compromised user account
curl -X PUT "https://api.yourdomain.com/api/users/{user_id}/status" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{"is_active": false, "reason": "Security incident"}'

# Force logout all sessions
curl -X POST "https://api.yourdomain.com/api/auth/logout-all" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Enable enhanced logging
export LOG_LEVEL=DEBUG
systemctl restart alertai
```

#### 2. Investigation

```bash
# Extract audit logs for investigation
curl -X GET "https://api.yourdomain.com/api/audit" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -G -d "user_id={compromised_user_id}" \
  -d "start_date=$(date -d '7 days ago' '+%Y-%m-%d')" \
  -d "end_date=$(date '+%Y-%m-%d')" \
  > incident_audit_logs.json

# Check for suspicious activities
grep -E "(login_failed|permission_changed|file_deleted)" incident_audit_logs.json
```

#### 3. Recovery

```bash
# Reset user passwords
curl -X POST "https://api.yourdomain.com/api/users/bulk/reset-passwords" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{"force_change": true, "notify_users": true}'

# Review and update security policies
# Update firewall rules if necessary
# Patch any identified vulnerabilities
```

## Security Testing

### Penetration Testing Checklist

- [ ] **Authentication bypass attempts**
- [ ] **SQL injection testing**
- [ ] **Cross-site scripting (XSS) testing**
- [ ] **Cross-site request forgery (CSRF) testing**
- [ ] **File upload vulnerability testing**
- [ ] **Rate limiting effectiveness**
- [ ] **Session management testing**
- [ ] **API security testing**
- [ ] **Network security assessment**
- [ ] **Database security review**

### Automated Security Scanning

```bash
# Example security scan with OWASP ZAP
docker run -t owasp/zap2docker-stable zap-baseline.py \
  -t https://yourdomain.com \
  -r security_scan_report.html

# Example dependency vulnerability scan
pip install safety
safety check --json > dependency_vulnerabilities.json
```

## Maintenance and Updates

### Regular Security Maintenance

#### Weekly Tasks
- [ ] Review audit logs for anomalies
- [ ] Check for failed login attempts
- [ ] Verify backup integrity
- [ ] Update threat intelligence feeds

#### Monthly Tasks
- [ ] Review user access and permissions
- [ ] Update security patches
- [ ] Test incident response procedures
- [ ] Generate compliance reports

#### Quarterly Tasks
- [ ] Conduct security assessment
- [ ] Review and update security policies
- [ ] Test disaster recovery procedures
- [ ] Security awareness training

### Security Update Procedures

```bash
# Update AlertAI securely
git fetch origin
git verify-commit origin/main  # Verify commit signatures
git merge origin/main

# Update dependencies
pip install --upgrade -r requirements.txt
npm audit fix

# Restart services
systemctl restart alertai
systemctl restart nginx
```

## Emergency Procedures

### Security Breach Response

#### Immediate Actions
1. **Isolate affected systems**
2. **Preserve evidence**
3. **Notify stakeholders**
4. **Activate incident response team**
5. **Document all actions**

#### Communication Plan
- **Internal**: Security team, management, legal
- **External**: Customers, partners, regulators (if required)
- **Timeline**: Within 1 hour of detection

#### Recovery Steps
1. **Assess damage and scope**
2. **Remove threat and secure systems**
3. **Restore from clean backups**
4. **Implement additional security measures**
5. **Monitor for continued threats**

---

*For additional security guidance, see [Authentication Setup](./setup.md) and [Audit Logging](./audit.md)*
