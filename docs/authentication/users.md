# User Management

This guide covers managing users, roles, and permissions in AlertAI's authentication system.

## Overview

AlertAI's user management system provides:
- **Role-based access control (RBAC)** with predefined and custom roles
- **Granular permissions** for fine-grained access control
- **User lifecycle management** from registration to deactivation
- **Audit trails** for all user-related activities
- **Self-service capabilities** for password changes and profile updates

## User Roles

### Predefined Roles

| Role | Description | Typical Users |
|------|-------------|---------------|
| **Admin** | Full system access and user management | System administrators, IT managers |
| **Analyst** | Alert management and investigation | Security analysts, SOC operators |
| **Viewer** | Read-only access to alerts and reports | Managers, compliance officers |
| **Guest** | Limited read-only access | External consultants, temporary users |

### Role Permissions

#### Admin Role
- **Full system access** including user management
- **All alert operations** (create, read, update, delete)
- **System configuration** and settings management
- **Audit log access** and compliance reporting
- **User management** (create, update, deactivate users)

#### Analyst Role
- **Alert management** (create, read, update, assign)
- **Comment and collaboration** features
- **File upload and management** for evidence
- **AI chat access** for alert analysis
- **Limited audit log access** (own actions)

#### Viewer Role
- **Read-only alert access** with filtering and search
- **View comments and files** but cannot modify
- **Dashboard and reporting** access
- **No administrative functions**

#### Guest Role
- **Limited alert viewing** (may be filtered by sensitivity)
- **No modification capabilities**
- **No access to sensitive information**
- **Temporary access** with automatic expiration

## User Management Operations

### Creating Users

#### Via API (Admin Required)

```bash
# Create a new analyst
curl -X POST "http://localhost:8002/api/auth/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "full_name": "John Doe",
    "role": "analyst",
    "department": "Security Operations",
    "phone": "******-0123"
  }'
```

#### Bulk User Creation

Create multiple users from a JSON file:

```bash
# users.json
[
  {
    "email": "<EMAIL>",
    "password": "TempPassword123!",
    "full_name": "Alice Smith",
    "role": "analyst",
    "department": "SOC"
  },
  {
    "email": "<EMAIL>",
    "password": "TempPassword123!",
    "full_name": "Bob Johnson",
    "role": "viewer",
    "department": "Management"
  }
]

# Bulk create script
#!/bin/bash
ADMIN_TOKEN="your_admin_token"
BASE_URL="http://localhost:8002"

jq -c '.[]' users.json | while read user; do
  curl -X POST "$BASE_URL/api/auth/register" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d "$user"
done
```

### Viewing Users

#### List All Users

```bash
curl -X GET "http://localhost:8002/api/users" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

Response:
```json
{
  "users": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "full_name": "System Administrator",
      "role": "admin",
      "is_active": true,
      "created_at": "2024-12-16T10:00:00Z",
      "last_login": "2024-12-16T14:30:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 50
}
```

#### Get Specific User

```bash
curl -X GET "http://localhost:8002/api/users/1" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### Filter Users

```bash
# Filter by role
curl -X GET "http://localhost:8002/api/users?role=analyst" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Filter by active status
curl -X GET "http://localhost:8002/api/users?is_active=true" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Search by name or email
curl -X GET "http://localhost:8002/api/users?search=john" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### Updating Users

#### Update User Information

```bash
curl -X PUT "http://localhost:8002/api/users/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "full_name": "John Doe Senior",
    "department": "Senior Security Operations",
    "phone": "******-0124"
  }'
```

#### Change User Role

```bash
curl -X PUT "http://localhost:8002/api/users/2/role" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "role": "admin",
    "reason": "Promoted to team lead"
  }'
```

#### Activate/Deactivate User

```bash
# Deactivate user
curl -X PUT "http://localhost:8002/api/users/2/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "is_active": false,
    "reason": "User left company"
  }'

# Reactivate user
curl -X PUT "http://localhost:8002/api/users/2/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "is_active": true,
    "reason": "User returned from leave"
  }'
```

### Password Management

#### Force Password Reset (Admin)

```bash
curl -X POST "http://localhost:8002/api/users/2/reset-password" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "temporary_password": "TempPassword123!",
    "force_change": true
  }'
```

#### User Self-Service Password Change

```bash
curl -X POST "http://localhost:8002/api/auth/change-password" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer USER_TOKEN" \
  -d '{
    "current_password": "OldPassword123!",
    "new_password": "NewPassword123!"
  }'
```

## Permission Management

### Custom Permissions

#### View User Permissions

```bash
curl -X GET "http://localhost:8002/api/users/2/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### Grant Additional Permissions

```bash
curl -X POST "http://localhost:8002/api/users/2/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "permissions": ["audit:read", "reports:export"],
    "reason": "Temporary compliance audit access"
  }'
```

#### Revoke Permissions

```bash
curl -X DELETE "http://localhost:8002/api/users/2/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "permissions": ["audit:read"],
    "reason": "Audit completed"
  }'
```

## User Activity Monitoring

### Login History

```bash
curl -X GET "http://localhost:8002/api/users/2/login-history" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### User Activity Logs

```bash
curl -X GET "http://localhost:8002/api/audit?user_id=2&limit=50" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### Active Sessions

```bash
curl -X GET "http://localhost:8002/api/users/2/sessions" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## Best Practices

### User Creation
1. **Use strong passwords** with complexity requirements
2. **Assign appropriate roles** based on job function
3. **Include contact information** for emergency access
4. **Set temporary passwords** and force change on first login
5. **Document role assignments** with business justification

### Role Assignment
1. **Follow principle of least privilege** - grant minimum necessary access
2. **Regular role reviews** - audit user roles quarterly
3. **Temporary access** - use guest roles for short-term access
4. **Role separation** - avoid combining conflicting roles

### Account Lifecycle
1. **Prompt deactivation** when users leave
2. **Regular access reviews** to identify unused accounts
3. **Password rotation** policies for shared accounts
4. **Audit trail maintenance** for compliance requirements

### Security Monitoring
1. **Monitor failed login attempts** for potential attacks
2. **Track privilege escalations** and role changes
3. **Review unusual access patterns** for insider threats
4. **Maintain audit logs** for compliance and investigation

## Troubleshooting

### Common Issues

1. **User cannot login**
   - Check if account is active
   - Verify password hasn't expired
   - Check for account lockout

2. **Permission denied errors**
   - Verify user role and permissions
   - Check if specific permissions are required
   - Ensure user account is active

3. **Cannot create users**
   - Verify admin privileges
   - Check for duplicate email addresses
   - Validate password complexity requirements

### Diagnostic Commands

```bash
# Check user status
curl -X GET "http://localhost:8002/api/users/EMAIL" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Verify permissions
curl -X GET "http://localhost:8002/api/users/ID/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Check recent activity
curl -X GET "http://localhost:8002/api/audit?user_id=ID&limit=10" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## Next Steps

1. **[API Authentication](./api.md)** - Integrate user management with external systems
2. **[Security Best Practices](./security.md)** - Secure your user management
3. **[Audit Logging](./audit.md)** - Monitor user activities
4. **[Troubleshooting](./troubleshooting.md)** - Resolve user management issues

---

*For API reference, see [User Management API](../api/users.md)*
