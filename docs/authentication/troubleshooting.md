# Authentication Troubleshooting

Common authentication issues and their solutions for AlertAI's enterprise security system.

## Overview

This guide covers troubleshooting for AlertAI's authentication system including JWT tokens, user management, permissions, and security features.

## Common Issues

### 1. Login Problems

#### "Invalid credentials" Error

**Symptoms:**
- User cannot login with correct credentials
- API returns 401 Unauthorized
- Login form shows "Invalid email or password"

**Possible Causes:**
- Incorrect email or password
- Account is deactivated
- Password has expired
- Database connection issues

**Solutions:**

```bash
# Check if user exists and is active
curl -X GET "http://localhost:8002/api/users?search=<EMAIL>" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Check user status
curl -X GET "http://localhost:8002/api/users/{user_id}" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Reset user password (admin only)
curl -X POST "http://localhost:8002/api/users/{user_id}/reset-password" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKE<PERSON>" \
  -d '{
    "temporary_password": "TempPassword123!",
    "force_change": true
  }'

# Reactivate user account
curl -X PUT "http://localhost:8002/api/users/{user_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "is_active": true,
    "reason": "Account reactivated"
  }'
```

#### "System is not initialized" Error

**Symptoms:**
- Cannot login to fresh installation
- API returns "System not initialized"
- No admin user exists

**Solution:**

```bash
# Initialize the system with admin user
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

### 2. Token Issues

#### "Token has expired" Error

**Symptoms:**
- API calls return 401 with "Token has expired"
- User gets logged out unexpectedly
- Frontend shows authentication errors

**Solutions:**

```bash
# Get new token
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=UserPassword123"

# Check token expiration settings
grep ACCESS_TOKEN_EXPIRE_MINUTES backend/.env

# Extend token expiration (in .env file)
ACCESS_TOKEN_EXPIRE_MINUTES=60  # Increase from 30 to 60 minutes
```

#### "Invalid token" Error

**Symptoms:**
- API returns "Invalid token"
- Token appears valid but is rejected
- Authentication works intermittently

**Possible Causes:**
- SECRET_KEY has changed
- Token was issued by different instance
- Token format is corrupted

**Solutions:**

```bash
# Check SECRET_KEY consistency
grep SECRET_KEY backend/.env

# Verify token format
echo "YOUR_TOKEN" | cut -d'.' -f1 | base64 -d

# Force logout all sessions and get new token
curl -X POST "http://localhost:8002/api/auth/logout-all" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3. Permission Issues

#### "Insufficient permissions" Error

**Symptoms:**
- User can login but cannot access certain features
- API returns 403 Forbidden
- UI elements are hidden or disabled

**Solutions:**

```bash
# Check user permissions
curl -X GET "http://localhost:8002/api/users/{user_id}/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Check user role
curl -X GET "http://localhost:8002/api/users/{user_id}" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Grant additional permissions
curl -X POST "http://localhost:8002/api/users/{user_id}/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "permissions": ["alerts:write", "comments:write"],
    "reason": "User needs alert management access"
  }'

# Change user role
curl -X PUT "http://localhost:8002/api/users/{user_id}/role" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "role": "analyst",
    "reason": "Promoted to analyst role"
  }'
```

### 4. Rate Limiting Issues

#### "Rate limit exceeded" Error

**Symptoms:**
- API returns 429 Too Many Requests
- User cannot make API calls temporarily
- Automated scripts fail with rate limit errors

**Solutions:**

```bash
# Check current rate limit settings
grep RATE_LIMIT backend/.env

# Adjust rate limits (in .env file)
RATE_LIMIT_REQUESTS=200  # Increase from 100
RATE_LIMIT_WINDOW=60     # Keep 60 seconds

# Check rate limit status for user
curl -X GET "http://localhost:8002/api/users/me/rate-limit" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Reset rate limit for user (admin only)
curl -X POST "http://localhost:8002/api/admin/rate-limit/reset" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{"user_id": 5}'
```

### 5. Database Issues

#### "Database connection failed" Error

**Symptoms:**
- Cannot login or register users
- API returns database connection errors
- User data is not persisting

**Solutions:**

```bash
# Check database connection
python3 -c "
from app.db.connection import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('Database connection OK')
except Exception as e:
    print(f'Database error: {e}')
"

# Check database URL
grep DATABASE_URL backend/.env

# Test PostgreSQL connection (if using PostgreSQL)
psql "postgresql://alertai_user:password@localhost:5432/alertai" -c "SELECT version();"

# Check SQLite file permissions (if using SQLite)
ls -la backend/alertai.db
```

#### "Table does not exist" Error

**Symptoms:**
- Database connection works but tables are missing
- API returns table/column not found errors
- Fresh installation issues

**Solutions:**

```bash
# Run database migrations
cd backend
python -m alembic upgrade head

# Initialize database tables
python -c "
from app.db.connection import init_db
init_db()
print('Database initialized')
"

# Check existing tables
python -c "
from app.db.connection import engine
from sqlalchemy import inspect
inspector = inspect(engine)
tables = inspector.get_table_names()
print('Tables:', tables)
"
```

## Diagnostic Commands

### System Health Check

```bash
#!/bin/bash
# comprehensive_auth_check.sh

echo "🔍 AlertAI Authentication System Diagnostics"
echo "============================================"

# Check server status
echo "1. Server Health Check..."
HEALTH=$(curl -s http://localhost:8002/api/health)
if echo $HEALTH | grep -q "healthy"; then
  echo "✅ Server is healthy"
else
  echo "❌ Server health check failed"
  echo "Response: $HEALTH"
fi

# Check database connection
echo "2. Database Connection..."
cd backend
python3 -c "
from app.db.connection import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        result = conn.execute(text('SELECT COUNT(*) FROM users'))
        count = result.scalar()
        print(f'✅ Database connected - {count} users found')
except Exception as e:
    print(f'❌ Database error: {e}')
"

# Check authentication configuration
echo "3. Authentication Configuration..."
if grep -q "SECRET_KEY=" backend/.env; then
  echo "✅ SECRET_KEY is configured"
else
  echo "❌ SECRET_KEY not found in .env"
fi

if grep -q "ACCESS_TOKEN_EXPIRE_MINUTES=" backend/.env; then
  EXPIRE_TIME=$(grep "ACCESS_TOKEN_EXPIRE_MINUTES=" backend/.env | cut -d'=' -f2)
  echo "✅ Token expiration: $EXPIRE_TIME minutes"
else
  echo "⚠️ Using default token expiration"
fi

# Test authentication flow
echo "4. Authentication Flow Test..."
# This would require valid credentials
echo "Manual test required - use admin credentials"

echo "============================================"
echo "Diagnostic complete"
```

### User Account Diagnostics

```bash
#!/bin/bash
# user_diagnostics.sh

USER_EMAIL="$1"
ADMIN_TOKEN="$2"

if [ -z "$USER_EMAIL" ] || [ -z "$ADMIN_TOKEN" ]; then
  echo "Usage: $0 <user_email> <admin_token>"
  exit 1
fi

echo "🔍 User Account Diagnostics for: $USER_EMAIL"
echo "============================================"

# Get user information
echo "1. User Information..."
USER_INFO=$(curl -s -X GET "http://localhost:8002/api/users?search=$USER_EMAIL" \
  -H "Authorization: Bearer $ADMIN_TOKEN")

if echo $USER_INFO | grep -q "\"email\":\"$USER_EMAIL\""; then
  echo "✅ User found in system"
  
  # Extract user ID
  USER_ID=$(echo $USER_INFO | jq -r '.users[0].id')
  IS_ACTIVE=$(echo $USER_INFO | jq -r '.users[0].is_active')
  ROLE=$(echo $USER_INFO | jq -r '.users[0].role')
  
  echo "   User ID: $USER_ID"
  echo "   Active: $IS_ACTIVE"
  echo "   Role: $ROLE"
  
  # Check permissions
  echo "2. User Permissions..."
  PERMISSIONS=$(curl -s -X GET "http://localhost:8002/api/users/$USER_ID/permissions" \
    -H "Authorization: Bearer $ADMIN_TOKEN")
  
  echo "   Permissions: $(echo $PERMISSIONS | jq -r '.permissions | join(", ")')"
  
  # Check recent activity
  echo "3. Recent Activity..."
  ACTIVITY=$(curl -s -X GET "http://localhost:8002/api/audit?user_id=$USER_ID&limit=5" \
    -H "Authorization: Bearer $ADMIN_TOKEN")
  
  echo "   Recent actions: $(echo $ACTIVITY | jq -r '.events | length') events"
  
else
  echo "❌ User not found in system"
fi

echo "============================================"
```

### Token Validation

```bash
#!/bin/bash
# token_validation.sh

TOKEN="$1"

if [ -z "$TOKEN" ]; then
  echo "Usage: $0 <jwt_token>"
  exit 1
fi

echo "🔍 JWT Token Validation"
echo "======================"

# Decode token header
echo "1. Token Header:"
HEADER=$(echo $TOKEN | cut -d'.' -f1)
echo $HEADER | base64 -d 2>/dev/null | jq . || echo "Invalid header format"

# Decode token payload
echo "2. Token Payload:"
PAYLOAD=$(echo $TOKEN | cut -d'.' -f2)
echo $PAYLOAD | base64 -d 2>/dev/null | jq . || echo "Invalid payload format"

# Test token with API
echo "3. Token Validation Test:"
RESPONSE=$(curl -s -X GET "http://localhost:8002/api/auth/me" \
  -H "Authorization: Bearer $TOKEN")

if echo $RESPONSE | grep -q "email"; then
  echo "✅ Token is valid"
  echo "   User: $(echo $RESPONSE | jq -r '.email')"
  echo "   Role: $(echo $RESPONSE | jq -r '.role')"
else
  echo "❌ Token validation failed"
  echo "   Response: $RESPONSE"
fi

echo "======================"
```

## Configuration Issues

### Environment Variables

Common environment variable issues:

```bash
# Check all authentication-related environment variables
echo "Authentication Configuration:"
echo "SECRET_KEY: $(grep SECRET_KEY backend/.env | cut -d'=' -f2 | sed 's/./*/g')"
echo "ACCESS_TOKEN_EXPIRE_MINUTES: $(grep ACCESS_TOKEN_EXPIRE_MINUTES backend/.env | cut -d'=' -f2)"
echo "DATABASE_URL: $(grep DATABASE_URL backend/.env | cut -d'=' -f2 | sed 's/password:[^@]*/password:***/')"
echo "DEBUG: $(grep DEBUG backend/.env | cut -d'=' -f2)"
echo "RATE_LIMIT_REQUESTS: $(grep RATE_LIMIT_REQUESTS backend/.env | cut -d'=' -f2)"
echo "RATE_LIMIT_WINDOW: $(grep RATE_LIMIT_WINDOW backend/.env | cut -d'=' -f2)"
```

### CORS Issues

```bash
# Check CORS configuration
echo "CORS Configuration:"
echo "FRONTEND_URLS: $(grep FRONTEND_URLS backend/.env | cut -d'=' -f2)"

# Test CORS headers
curl -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: X-Requested-With" \
  -X OPTIONS \
  http://localhost:8002/api/auth/token \
  -v
```

## Performance Issues

### Slow Authentication

```bash
# Check database query performance
python3 -c "
import time
from app.db.connection import get_db
from app.models.user import User

start_time = time.time()
db = next(get_db())
user = db.query(User).filter(User.email == '<EMAIL>').first()
end_time = time.time()

print(f'User query took: {(end_time - start_time) * 1000:.2f}ms')
"

# Check password hashing performance
python3 -c "
import time
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')

start_time = time.time()
hashed = pwd_context.hash('test_password')
end_time = time.time()

print(f'Password hashing took: {(end_time - start_time) * 1000:.2f}ms')

start_time = time.time()
verified = pwd_context.verify('test_password', hashed)
end_time = time.time()

print(f'Password verification took: {(end_time - start_time) * 1000:.2f}ms')
"
```

## Security Alerts

### Failed Login Monitoring

```bash
# Check for failed login attempts
grep "login_failed" backend/logs/security.log | tail -10

# Count failed attempts by IP
grep "login_failed" backend/logs/security.log | \
  grep "$(date '+%Y-%m-%d')" | \
  jq -r '.event_data.ip_address' | \
  sort | uniq -c | sort -nr
```

### Suspicious Activity Detection

```bash
# Check for rate limit violations
grep "rate_limit_exceeded" backend/logs/security.log | tail -10

# Check for privilege escalation attempts
grep "permission_denied" backend/logs/security.log | \
  grep "$(date '+%Y-%m-%d')" | \
  jq -r '.event_data.attempted_action' | \
  sort | uniq -c
```

## Recovery Procedures

### Reset Admin Password

```bash
# Reset admin password via database (emergency only)
python3 -c "
from app.db.connection import get_db
from app.models.user import User
from app.core.auth import AuthService

db = next(get_db())
admin_user = db.query(User).filter(User.email == '<EMAIL>').first()

if admin_user:
    admin_user.hashed_password = AuthService.get_password_hash('NewAdminPassword123!')
    db.commit()
    print('Admin password reset successfully')
else:
    print('Admin user not found')
"
```

### Clear All Sessions

```bash
# Force logout all users (admin only)
curl -X POST "http://localhost:8002/api/auth/logout-all" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### Database Recovery

```bash
# Backup current database
cp backend/alertai.db backend/alertai.db.backup

# Reset database (WARNING: This will delete all data)
rm backend/alertai.db
python -c "
from app.db.connection import init_db
init_db()
print('Database reset complete')
"

# Re-initialize system
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "NewAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

## Getting Help

### Log Analysis

```bash
# View recent authentication logs
tail -f backend/logs/security.log | grep -E "(login|auth|permission)"

# View application logs
tail -f backend/logs/application.log

# View error logs
grep -i error backend/logs/application.log | tail -10
```

### Support Information

When contacting support, include:

1. **Error messages** - Exact error text and codes
2. **Log excerpts** - Relevant log entries (sanitize sensitive data)
3. **Configuration** - Environment variables (sanitize secrets)
4. **Steps to reproduce** - Detailed reproduction steps
5. **System information** - OS, Python version, database type

### Community Resources

- **GitHub Issues**: Check existing issues and solutions
- **Documentation**: Review authentication setup guide
- **API Reference**: Verify API usage patterns
- **Security Guide**: Follow security best practices

---

*For additional help, see [Authentication Setup](./setup.md) and [Security Best Practices](./security.md)*
