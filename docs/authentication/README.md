# Authentication & Security

AlertAI includes enterprise-grade authentication and security features designed to protect your security operations and ensure compliance with industry standards.

## Overview

The authentication system provides:
- **JWT-based authentication** with secure token management
- **Role-based access control (RBAC)** with granular permissions
- **Multi-user support** with user registration and management
- **Comprehensive audit logging** for security compliance
- **API rate limiting** to prevent abuse
- **Input validation and sanitization** for security

## Quick Start

### 1. System Initialization

First-time setup requires initializing the system with an admin user:

```bash
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123",
    "full_name": "System Administrator"
  }'
```

### 2. User Login

Login to get an access token:

```bash
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=SecureAdminPassword123"
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 3. Using the Token

Include the token in API requests:

```bash
curl -X GET "http://localhost:8002/api/alerts" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## User Roles & Permissions

### Default Roles

| Role | Description | Use Case |
|------|-------------|----------|
| **Admin** | System administrator with full access | System management, user administration |
| **Analyst** | Security analyst with alert management | Day-to-day security operations |
| **Viewer** | Read-only access to alerts and reports | Management oversight, reporting |
| **Guest** | Limited read-only access | External consultants, temporary access |

### Permission Matrix

| Resource | Admin | Analyst | Viewer | Guest |
|----------|-------|---------|--------|-------|
| **Users** | Full | - | - | - |
| **Alerts** | Full | Read/Write | Read | Read (limited) |
| **Comments** | Full | Read/Write | Read | - |
| **Files** | Full | Read/Write | Read | - |
| **AI Chat** | Full | Read/Write | - | - |
| **Audit Logs** | Full | Read | - | - |
| **System Config** | Full | - | - | - |

### Granular Permissions

Permissions follow the format: `resource:action`

#### Alert Permissions
- `alerts:read` - View alerts and their details
- `alerts:write` - Create and update alerts
- `alerts:delete` - Delete alerts (soft delete)
- `alerts:assign` - Assign alerts to analysts

#### User Management Permissions
- `users:read` - View user information
- `users:write` - Create and update users
- `users:delete` - Deactivate users
- `users:admin` - Full user administration

#### System Permissions
- `system:read` - View system configuration
- `system:write` - Modify system settings
- `system:admin` - Full system administration
- `audit:read` - View audit logs

## Security Features

### Authentication Security
- **JWT tokens** with HS256 algorithm
- **Configurable token expiration** (default: 30 minutes)
- **Secure password hashing** with bcrypt
- **Password strength requirements** (minimum 8 characters)
- **Session management** with token invalidation

### API Security
- **Rate limiting** with configurable limits per endpoint
- **Input validation** for all API endpoints
- **SQL injection prevention** with parameterized queries
- **XSS protection** with output encoding
- **CORS protection** with environment-based origins

### Audit & Compliance
- **Comprehensive audit logging** for all security events
- **Real-time security monitoring** with automated alerting
- **Compliance reporting** for SOC 2, GDPR, HIPAA
- **Data retention policies** with automated cleanup
- **Security metrics** and performance monitoring

## Documentation Sections

- **[Setup Guide](./setup.md)** - Initial system setup and configuration
- **[User Management](./users.md)** - Managing users, roles, and permissions
- **[API Authentication](./api.md)** - Using authentication with the API
- **[Security Best Practices](./security.md)** - Production security guidelines
- **[Audit Logging](./audit.md)** - Understanding and managing audit logs
- **[Troubleshooting](./troubleshooting.md)** - Common authentication issues

## Quick Reference

### Common API Endpoints

```bash
# System initialization (one-time)
POST /api/auth/init-system

# User authentication
POST /api/auth/token          # Login
GET /api/auth/me             # Get current user
POST /api/auth/logout        # Logout
POST /api/auth/register      # Register new user

# User management (admin only)
GET /api/users               # List users
POST /api/users              # Create user
PUT /api/users/{id}          # Update user
DELETE /api/users/{id}       # Deactivate user

# Audit logs (admin/analyst)
GET /api/audit               # Get audit logs
GET /api/audit/stats         # Get audit statistics
```

### Environment Variables

```bash
# Authentication settings
SECRET_KEY=your-super-secure-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Security
DEBUG=false
CORS_ORIGINS=https://yourdomain.com
```

## Next Steps

1. **[Setup Guide](./setup.md)** - Configure authentication for your environment
2. **[User Management](./users.md)** - Add users and configure roles
3. **[API Authentication](./api.md)** - Integrate with external systems
4. **[Security Best Practices](./security.md)** - Secure your deployment

---

*For detailed API documentation, see the [API Reference](../api/rest-api.md)*
