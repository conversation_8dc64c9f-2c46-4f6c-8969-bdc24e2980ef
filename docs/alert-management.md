# Alert Management System

## Overview

AlertAI now includes a comprehensive alert management system that allows security analysts to fully manage alerts throughout their lifecycle. This includes editing alert details, changing status, bulk operations, and advanced security features.

## Features

### 🔧 **Alert Editing**
- **Edit Alert Details**: Modify title, description, severity, status, and source
- **Advanced Fields**: Set priority, due dates, escalation levels, and investigation notes
- **Tag Management**: Add and remove tags for better organization
- **MITRE ATT&CK Mapping**: Tag alerts with relevant MITRE techniques
- **Analyst Assignment**: Assign alerts to specific security analysts

### 🗑️ **Alert Deletion**
- **Soft Delete**: Move alerts to trash (can be restored later)
- **Hard Delete**: Permanently remove alerts and all associated data
- **Confirmation Required**: Type confirmation text to prevent accidental deletion
- **Cascade Deletion**: Automatically handles related comments, files, and chat history

### 📋 **Bulk Operations**
- **Multi-Select**: Select multiple alerts using checkboxes
- **Bulk Status Changes**: Mark multiple alerts as open, acknowledged, or resolved
- **Bulk Severity Updates**: Change severity levels across multiple alerts
- **Bulk Priority Setting**: Set priority levels for multiple alerts
- **Bulk Escalation**: Apply escalation levels to multiple alerts

### 📊 **Comprehensive Change Log**
- **Complete Audit Trail**: Every action on an alert is logged with timestamps
- **Activity Types**: Alert creation, field updates, comments, file uploads, deletions
- **User Attribution**: Tracks who made each change with IP address and user agent
- **Field-Level Tracking**: Shows exactly what changed from old value to new value
- **Rich Descriptions**: Human-readable descriptions of all changes
- **Timeline View**: Chronological display of all alert activities

### 🔍 **Enhanced Alert Fields**

#### **Core Fields**
- **Title**: Brief description of the alert
- **Description**: Detailed alert information
- **Severity**: Low, Medium, High, Critical
- **Status**: Open, Acknowledged, Resolved
- **Source**: Origin system or service

#### **Management Fields**
- **Assigned Analyst**: Security analyst responsible for the alert
- **Priority**: 1-5 scale (1=Very Low, 5=Critical)
- **Due Date**: Target resolution date
- **Escalation Level**: 0-3 scale (0=None, 3=Level 3)
- **Tags**: Custom labels for organization
- **Investigation Notes**: Internal analyst notes
- **MITRE Techniques**: ATT&CK framework technique IDs
- **SLA Deadline**: Service level agreement deadline

## User Interface

### **Alerts Table**
- **Selection Column**: Checkboxes for multi-select operations
- **Action Buttons**: Edit and delete buttons for each alert
- **Bulk Actions Bar**: Appears when alerts are selected
- **Visual Indicators**: Color-coded severity and status badges

### **Alert Detail Page**
- **Tabbed Interface**: Comments, Resources, and Change Log tabs
- **Change Log Tab**: Complete audit trail with timeline view
- **Activity Icons**: Visual indicators for different change types
- **User Attribution**: Shows who made each change and when
- **Field Changes**: Before/after values for all modifications

### **Edit Alert Dialog**
- **Large Modal**: Comprehensive form with all alert fields
- **Tabbed Interface**: Organized sections for different field groups
- **Real-time Validation**: Immediate feedback on form inputs
- **Change Detection**: Only sends modified fields to the API

### **Delete Alert Dialog**
- **Confirmation Required**: Must type "DELETE-{ID}" to confirm
- **Delete Type Selection**: Choose between soft delete and hard delete
- **Impact Warning**: Shows what data will be affected
- **Preview Information**: Displays alert details before deletion

### **Bulk Actions Dialog**
- **Selected Alerts Preview**: Shows which alerts are selected
- **Action Categories**: Grouped by status, severity, priority, and escalation
- **Preview Changes**: Shows what will be applied before execution
- **Progress Feedback**: Real-time updates during bulk operations

### **Change Log Interface**
- **Timeline View**: Chronological display of all activities
- **Activity Types**: Created, Updated, Comment Added, File Uploaded, Deleted
- **Change Details**: Field-level changes with old and new values
- **User Information**: Who made the change and when
- **Rich Descriptions**: Human-readable summaries of all changes
- **Visual Indicators**: Color-coded badges and icons for different activity types

## API Endpoints

### **Update Alert**
```http
PUT /api/alerts/{alert_id}
Content-Type: application/json

{
  "title": "Updated Alert Title",
  "status": "acknowledged",
  "priority": 4,
  "tags": ["malware", "endpoint"],
  "due_date": "2025-06-20T23:59:59Z",
  "investigation_notes": "Initial analysis completed"
}
```

### **Delete Alert**
```http
DELETE /api/alerts/{alert_id}?hard_delete=false
```

### **Get Change Logs**
```http
GET /api/alerts/{alert_id}/change-logs
```

### **Response Format**
```json
{
  "alert": {
    "id": 1,
    "title": "Updated Alert Title",
    "status": "acknowledged",
    "priority": 4,
    "tags": ["malware", "endpoint"],
    "assigned_analyst_id": 1,
    "assigned_analyst_name": "Sarah Chen",
    "escalation_level": 1,
    "investigation_notes": "Initial analysis completed",
    "mitre_techniques": ["T1566.001", "T1204.002"],
    "is_deleted": false,
    "updated_at": "2025-06-15T19:36:12.607716"
  },
  "message": "Alert #1 updated successfully"
}
```

### **Change Log Response**
```json
{
  "change_logs": [
    {
      "id": 6,
      "alert_id": 35,
      "changed_by": "Current User",
      "changed_at": "2025-06-15T17:05:05",
      "change_type": "file_uploaded",
      "field_name": null,
      "old_value": null,
      "new_value": null,
      "description": "File uploaded by Current User: test-file.txt (43 bytes)",
      "ip_address": null,
      "user_agent": null
    },
    {
      "id": 5,
      "alert_id": 35,
      "changed_by": "Security Analyst",
      "changed_at": "2025-06-15T17:04:24",
      "change_type": "comment_added",
      "field_name": null,
      "old_value": null,
      "new_value": null,
      "description": "Comment added by Security Analyst: This alert has been investigated...",
      "ip_address": null,
      "user_agent": null
    },
    {
      "id": 2,
      "alert_id": 35,
      "changed_by": "API User",
      "changed_at": "2025-06-15T16:55:20",
      "change_type": "updated",
      "field_name": "title",
      "old_value": "Test Change Log Alert",
      "new_value": "Updated Test Change Log Alert",
      "description": "Title changed from 'Test Change Log Alert' to 'Updated Test Change Log Alert'",
      "ip_address": null,
      "user_agent": null
    }
  ],
  "total_count": 3
}
```

## Database Schema

### **New Alert Fields**
```sql
ALTER TABLE alerts ADD COLUMN assigned_analyst_id INTEGER;
ALTER TABLE alerts ADD COLUMN assigned_analyst_name TEXT;
ALTER TABLE alerts ADD COLUMN tags TEXT; -- JSON array
ALTER TABLE alerts ADD COLUMN due_date DATETIME;
ALTER TABLE alerts ADD COLUMN priority INTEGER;
ALTER TABLE alerts ADD COLUMN investigation_notes TEXT;
ALTER TABLE alerts ADD COLUMN mitre_techniques TEXT; -- JSON array
ALTER TABLE alerts ADD COLUMN escalation_level INTEGER DEFAULT 0;
ALTER TABLE alerts ADD COLUMN sla_deadline DATETIME;
ALTER TABLE alerts ADD COLUMN is_deleted BOOLEAN DEFAULT 0;
```

### **Change Log Table**
```sql
CREATE TABLE alert_change_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alert_id INTEGER NOT NULL,
    changed_by TEXT NOT NULL,
    changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    change_type TEXT NOT NULL,
    field_name TEXT,
    old_value TEXT,
    new_value TEXT,
    description TEXT,
    ip_address TEXT,
    user_agent TEXT,
    FOREIGN KEY (alert_id) REFERENCES alerts (id)
);
```

### **Change Types**
- **created**: Alert was initially created
- **updated**: Alert field was modified
- **comment_added**: Comment was added to the alert
- **file_uploaded**: File or image was uploaded to the alert
- **soft_deleted**: Alert was moved to trash (can be restored)
- **hard_deleted**: Alert was permanently deleted

## Security Features

### **Soft Delete Protection**
- Alerts are marked as deleted rather than removed
- Preserves audit trail and investigation history
- Can be restored if needed
- Excluded from normal queries

### **Change Tracking**
- All updates include timestamp
- Maintains updated_at field
- Preserves original creation date
- Audit trail for compliance

### **Validation**
- Required field validation
- Data type checking
- Business rule enforcement
- SQL injection prevention

## Usage Examples

### **Quick Status Update**
1. Navigate to alerts list
2. Click edit button on desired alert
3. Change status to "Acknowledged"
4. Click "Update Alert"

### **Bulk Priority Setting**
1. Select multiple alerts using checkboxes
2. Click "Bulk Actions" button
3. Choose "Set Priority: High"
4. Confirm the action

### **Alert Investigation**
1. Open alert details
2. Click edit button
3. Add investigation notes
4. Set MITRE techniques
5. Assign to analyst
6. Set due date

## Best Practices

### **Alert Lifecycle Management**
1. **Triage**: Set initial priority and assign analyst
2. **Investigation**: Add notes and MITRE mappings
3. **Escalation**: Increase escalation level if needed
4. **Resolution**: Update status and add final notes
5. **Closure**: Mark as resolved with summary

### **Tag Organization**
- Use consistent naming conventions
- Create tags for: threat types, affected systems, investigation status
- Examples: "malware", "phishing", "endpoint", "network", "in-progress"

### **Priority Guidelines**
- **1 (Very Low)**: Informational alerts, low-risk events
- **2 (Low)**: Minor security events, routine monitoring
- **3 (Medium)**: Standard security incidents, default priority
- **4 (High)**: Significant threats, potential breaches
- **5 (Critical)**: Active attacks, confirmed breaches, system compromise

## Bitzy AI Assistant Integration

### **Change Log Awareness**
- **Full Context**: Bitzy can access and analyze the complete change history of any alert
- **Timeline Analysis**: AI can identify patterns in alert modifications and user activities
- **Investigation Assistance**: Bitzy can summarize what actions have been taken on an alert
- **Compliance Reporting**: AI can generate audit reports based on change log data

### **Smart Suggestions**
- **Next Actions**: Based on change history, Bitzy suggests appropriate next steps
- **Pattern Recognition**: AI identifies similar alerts and their resolution patterns
- **Escalation Recommendations**: Suggests when to escalate based on activity timeline
- **Documentation Guidance**: Recommends adding investigation notes or comments

### **Bitzy Commands for Change Logs**
- **"Show me the change history for this alert"** - Displays formatted timeline
- **"Who last modified this alert?"** - Shows recent changes and users
- **"What actions have been taken on this alert?"** - Summarizes all activities
- **"Generate an audit report for this alert"** - Creates compliance documentation
- **"Find similar alerts with this change pattern"** - Pattern-based search

### **System-Wide Logging Integration**
- **Complete Activity Tracking**: All alert activities are logged in the centralized system logging infrastructure
- **Performance Monitoring**: Response times and system metrics are tracked for all alert operations
- **Security Audit Trail**: Every alert action includes user attribution, IP addresses, and session tracking
- **Bitzy Log Analysis**: AI assistant can analyze system logs to identify patterns and troubleshoot issues
- **Compliance Reporting**: Comprehensive audit trails for regulatory compliance and security investigations

### **Enhanced Bitzy Capabilities with System Logs**
- **"Show me system activity for the last hour"** - Real-time system monitoring
- **"Who has been most active today?"** - User activity analysis
- **"Are there any error patterns I should know about?"** - Proactive error detection
- **"Generate a security audit report"** - Comprehensive security analysis
- **"What's the system performance like?"** - Performance metrics and health checks

## Migration

The system includes an automatic database migration that:
- Adds new columns to existing alerts table
- Sets default values for existing alerts
- Preserves all existing data
- Maintains backward compatibility

Run the migration with:
```bash
cd backend
python3 migrate_database.py
```

## Troubleshooting

### **Common Issues**
- **Missing columns error**: Run the database migration
- **Permission denied**: Check file permissions on database
- **API 405 errors**: Ensure backend is running updated code
- **Frontend compilation errors**: Install missing dependencies with `npm install`

### **Validation Errors**
- **Title required**: Alert title cannot be empty
- **Invalid severity**: Must be one of: low, medium, high, critical
- **Invalid status**: Must be one of: open, acknowledged, resolved
- **Invalid priority**: Must be integer between 1-5
