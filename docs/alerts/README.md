# Alert Management

Comprehensive guide to managing security alerts in AlertAI, from creation and analysis to resolution and reporting.

## Overview

AlertAI's alert management system provides:
- **Centralized alert collection** from multiple sources
- **Intelligent prioritization** with AI-powered analysis
- **Collaborative investigation** with team assignment
- **Rich metadata and context** for thorough analysis
- **Audit trails** for compliance and reporting
- **Integration capabilities** with external systems

## Alert Lifecycle

### 1. Alert Creation
Alerts can be created through:
- **Webhook ingestion** from external security tools
- **Manual creation** by security analysts
- **API integration** from custom applications
- **Bulk import** from CSV or JSON files

### 2. Initial Processing
When an alert is created:
- **Automatic classification** based on content and source
- **Priority assignment** using AI-powered analysis
- **IOC extraction** for threat intelligence correlation
- **Duplicate detection** to prevent alert fatigue
- **Routing** to appropriate analysts based on rules

### 3. Investigation
During investigation:
- **Analyst assignment** with role-based access
- **Collaborative comments** with @mentions and notifications
- **File attachments** for evidence collection
- **AI-powered analysis** with <PERSON><PERSON> assistant
- **Status tracking** through investigation phases

### 4. Resolution
Alert resolution includes:
- **Final status assignment** (resolved, false positive, etc.)
- **Resolution notes** with detailed findings
- **Lessons learned** documentation
- **Metrics collection** for performance analysis
- **Audit trail completion** for compliance

## Alert Structure

### Core Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `title` | string | Brief alert description | Yes |
| `description` | text | Detailed alert information | Yes |
| `severity` | enum | Critical, High, Medium, Low | Yes |
| `status` | enum | Open, In Progress, Resolved, False Positive | Yes |
| `source` | string | Alert source system | Yes |
| `created_at` | datetime | Alert creation timestamp | Auto |
| `updated_at` | datetime | Last modification timestamp | Auto |

### Extended Fields

| Field | Type | Description |
|-------|------|-------------|
| `assigned_to` | integer | Analyst user ID |
| `priority` | integer | Priority score (1-100) |
| `tags` | array | Classification tags |
| `external_id` | string | Source system identifier |
| `deadline` | datetime | SLA deadline |
| `resolution_time` | integer | Time to resolution (minutes) |

### Metadata Fields

| Field | Type | Description |
|-------|------|-------------|
| `source_ip` | string | Source IP address |
| `destination_ip` | string | Destination IP address |
| `user_agent` | string | User agent string |
| `file_hash` | string | File hash (MD5, SHA1, SHA256) |
| `domain` | string | Associated domain |
| `url` | string | Associated URL |
| `process_name` | string | Process name |
| `command_line` | string | Command line arguments |

## Alert Management Operations

### Creating Alerts

#### Manual Alert Creation

```bash
curl -X POST "http://localhost:8002/api/alerts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Suspicious Network Activity",
    "description": "Unusual outbound connections detected from workstation",
    "severity": "high",
    "source": "Network Monitoring",
    "metadata": {
      "source_ip": "*************",
      "destination_ip": "************",
      "port": 443,
      "protocol": "HTTPS"
    },
    "tags": ["network", "outbound", "suspicious"]
  }'
```

#### Webhook Alert Creation

Configure webhook sources to automatically create alerts:

```json
{
  "webhook_url": "http://localhost:8002/api/webhooks/alerts",
  "authentication": {
    "type": "bearer",
    "token": "webhook_token_here"
  },
  "mapping": {
    "title": "$.alert.name",
    "description": "$.alert.description",
    "severity": "$.alert.severity",
    "source": "$.source.name",
    "metadata.source_ip": "$.network.src_ip",
    "metadata.destination_ip": "$.network.dst_ip"
  }
}
```

### Retrieving Alerts

#### List Alerts with Filtering

```bash
# Get all alerts
curl -X GET "http://localhost:8002/api/alerts" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Filter by severity
curl -X GET "http://localhost:8002/api/alerts?severity=high" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Filter by status and assigned analyst
curl -X GET "http://localhost:8002/api/alerts?status=open&assigned_to=5" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Search alerts
curl -X GET "http://localhost:8002/api/alerts?search=malware" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Pagination
curl -X GET "http://localhost:8002/api/alerts?page=2&per_page=25" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Get Specific Alert

```bash
curl -X GET "http://localhost:8002/api/alerts/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "id": 123,
  "title": "Suspicious Network Activity",
  "description": "Unusual outbound connections detected",
  "severity": "high",
  "status": "open",
  "priority": 85,
  "source": "Network Monitoring",
  "assigned_to": {
    "id": 5,
    "name": "John Analyst",
    "email": "<EMAIL>"
  },
  "metadata": {
    "source_ip": "*************",
    "destination_ip": "************"
  },
  "tags": ["network", "outbound", "suspicious"],
  "created_at": "2024-12-16T10:30:00Z",
  "updated_at": "2024-12-16T11:15:00Z",
  "comments_count": 3,
  "files_count": 1
}
```

### Updating Alerts

#### Update Alert Status

```bash
curl -X PUT "http://localhost:8002/api/alerts/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "status": "in_progress",
    "notes": "Investigation started - checking network logs"
  }'
```

#### Assign Alert to Analyst

```bash
curl -X POST "http://localhost:8002/api/alerts/123/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "assigned_to": 5,
    "reason": "Network security specialist"
  }'
```

#### Update Alert Priority

```bash
curl -X PUT "http://localhost:8002/api/alerts/123/priority" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "priority": 95,
    "reason": "Critical infrastructure affected"
  }'
```

### Alert Analysis

#### AI-Powered Analysis

Request AI analysis of an alert:

```bash
curl -X POST "http://localhost:8002/api/alerts/123/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Get AI findings:

```bash
curl -X GET "http://localhost:8002/api/alerts/123/ai-findings" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "analysis": {
    "threat_level": "high",
    "confidence": 0.87,
    "mitre_techniques": ["T1071.001", "T1041"],
    "iocs": [
      {
        "type": "ip",
        "value": "************",
        "threat_type": "c2_server"
      }
    ],
    "recommendations": [
      "Block outbound connections to ************",
      "Investigate other systems for similar activity",
      "Check for persistence mechanisms"
    ]
  },
  "generated_at": "2024-12-16T11:20:00Z"
}
```

#### Chat with AI

Interactive chat about the alert:

```bash
curl -X POST "http://localhost:8002/api/alerts/123/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "message": "What are the potential attack vectors for this alert?"
  }'
```

## Alert Collaboration

### Comments

#### Add Comment

```bash
curl -X POST "http://localhost:8002/api/alerts/123/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "Checked network logs - confirmed suspicious activity @john.analyst",
    "is_internal": false
  }'
```

#### Get Comments

```bash
curl -X GET "http://localhost:8002/api/alerts/123/comments" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### File Attachments

#### Upload Evidence File

```bash
curl -X POST "http://localhost:8002/api/alerts/123/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@network_capture.pcap" \
  -F "description=Network capture during incident"
```

#### Get Alert Files

```bash
curl -X GET "http://localhost:8002/api/alerts/123/files" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Alert Filtering and Search

### Advanced Filtering

```bash
# Multiple filters
curl -X GET "http://localhost:8002/api/alerts?severity=high,critical&status=open&created_after=2024-12-01" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Date range filtering
curl -X GET "http://localhost:8002/api/alerts?created_after=2024-12-01&created_before=2024-12-16" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Tag filtering
curl -X GET "http://localhost:8002/api/alerts?tags=malware,network" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Source filtering
curl -X GET "http://localhost:8002/api/alerts?source=SIEM,IDS" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Search Capabilities

```bash
# Full-text search
curl -X GET "http://localhost:8002/api/alerts?search=malware+detection" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Metadata search
curl -X GET "http://localhost:8002/api/alerts?search=ip:*************" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Advanced search syntax
curl -X GET "http://localhost:8002/api/alerts?search=severity:high+AND+status:open" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Alert Metrics and Reporting

### Alert Statistics

```bash
curl -X GET "http://localhost:8002/api/alerts/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "total_alerts": 1250,
  "open_alerts": 45,
  "in_progress": 23,
  "resolved_today": 12,
  "average_resolution_time": 145,
  "by_severity": {
    "critical": 5,
    "high": 15,
    "medium": 20,
    "low": 5
  },
  "by_source": {
    "SIEM": 25,
    "IDS": 15,
    "EDR": 5
  }
}
```

### Performance Metrics

```bash
curl -X GET "http://localhost:8002/api/alerts/metrics?period=7d" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Best Practices

### Alert Creation
1. **Use descriptive titles** that clearly indicate the threat
2. **Include relevant metadata** for context and correlation
3. **Set appropriate severity** based on impact and urgency
4. **Add meaningful tags** for categorization and filtering
5. **Include source information** for traceability

### Investigation Workflow
1. **Assign alerts promptly** to appropriate analysts
2. **Document investigation steps** in comments
3. **Attach relevant evidence** files and screenshots
4. **Use AI analysis** to accelerate investigation
5. **Update status regularly** to track progress

### Resolution Process
1. **Provide detailed resolution notes** for future reference
2. **Document lessons learned** for process improvement
3. **Update threat intelligence** with new IOCs
4. **Close related alerts** if applicable
5. **Generate reports** for stakeholders

## Next Steps

1. **[AI Assistant (Bitzy)](../bitzy/README.md)** - Use AI for alert analysis
2. **[Comments & Collaboration](../comments/README.md)** - Team collaboration features
3. **[File Management](../files/README.md)** - Evidence collection and management
4. **[Sources Management](../sources/README.md)** - Configure alert sources

---

*For API details, see [Alert Management API](../api/alerts.md)*
