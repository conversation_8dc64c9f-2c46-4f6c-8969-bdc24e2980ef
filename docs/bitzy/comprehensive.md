# Bitzy - AI Assistant (Comprehensive Guide)

<PERSON><PERSON> is AlertAI's intelligent assistant that helps security analysts analyze alerts, investigate threats, and make informed decisions using advanced AI capabilities with enterprise-grade security and privacy.

## What is Bit<PERSON>?

<PERSON><PERSON> is an AI-powered assistant integrated into AlertAI that provides:
- **Contextual analysis** of security alerts with threat intelligence correlation
- **Natural language interaction** for investigation with conversation memory
- **Automated threat detection** and classification with confidence scoring
- **Expert recommendations** for incident response and mitigation
- **Secure local processing** with no external data transmission

## Key Features

### 🧠 **Intelligent Analysis**
- **Context-Aware**: Understands the full context of each alert including metadata, history, and related events
- **Threat Correlation**: Connects alerts to known threat patterns and MITRE ATT&CK techniques
- **Risk Assessment**: Provides confidence scores (0-1) and risk levels (Low/Medium/High/Critical)
- **IOC Extraction**: Automatically identifies and validates indicators of compromise
- **Pattern Recognition**: Detects anomalies and suspicious behavior patterns

### 💬 **Natural Language Interface**
- **Conversational AI**: Ask questions in plain English with natural conversation flow
- **Alert-Specific Context**: Knows details about the current alert, comments, and attachments
- **Follow-up Questions**: Maintains conversation context across multiple interactions
- **Expert Responses**: Provides security expert-level insights with technical depth
- **Multi-language Support**: Supports multiple languages for global teams

### 🔍 **Investigation Support**
- **Guided Analysis**: Step-by-step investigation recommendations with priority ordering
- **Evidence Correlation**: Links related alerts, IOCs, and historical incidents
- **Timeline Reconstruction**: Helps build attack timelines and kill chain analysis
- **Mitigation Strategies**: Suggests immediate and long-term response actions
- **Forensic Guidance**: Provides digital forensics best practices and procedures

### 📊 **Reporting Assistance**
- **Summary Generation**: Creates concise alert summaries with key findings
- **Technical Details**: Explains complex technical concepts in accessible language
- **Executive Briefings**: Generates management-friendly reports with business impact
- **Documentation**: Helps document investigation findings and lessons learned
- **Compliance Reporting**: Assists with regulatory compliance documentation

## How Bitzy Works

### AI Model Integration
Bitzy uses local Large Language Models (LLMs) through Ollama integration:
- **Privacy-First**: All processing happens locally within your environment
- **No Data Leakage**: Sensitive information never leaves your infrastructure
- **Customizable Models**: Support for various AI models (deepseek-r1, llama2, mistral)
- **Offline Capable**: Works without internet connectivity for maximum security
- **GPU Acceleration**: Supports NVIDIA GPUs for faster processing

### Context Understanding
Bitzy has comprehensive access to:
- **Alert Details**: Full alert information, metadata, and raw payloads
- **Historical Data**: Previous alerts, investigations, and resolution patterns
- **Threat Intelligence**: Known IOCs, attack patterns, and MITRE ATT&CK mappings
- **System Configuration**: Understanding of your environment and security tools
- **User Context**: Role-based access and personalized recommendations

### Security and Privacy
- **Local Processing**: All AI analysis happens on your infrastructure
- **Access Control**: Respects user permissions and role-based access
- **Audit Logging**: All interactions are logged for compliance and security
- **Data Encryption**: All communications are encrypted in transit and at rest
- **No External Dependencies**: No reliance on cloud AI services

## Using Bitzy

### Accessing Bitzy

#### In Alert Details
1. **Navigate to an Alert**: Open any alert in AlertAI
2. **Find the AI Chat Tab**: Look for "Ask Bitzy" or "AI Assistant" tab
3. **Start Conversation**: Type your question in the chat interface
4. **Get Analysis**: Receive expert-level insights and recommendations

#### Global Chat Interface
1. **Chat Bubble**: Click the Bitzy icon in the bottom-left corner
2. **Expandable Interface**: Expand for full-screen conversation
3. **Context Awareness**: Bitzy knows which page you're viewing
4. **Persistent History**: Access previous conversations and insights

### Example Conversations

#### Basic Alert Analysis
```
You: What can you tell me about this alert?

Bitzy: This alert indicates suspicious network activity from IP ************* 
to external IP ************. Based on the patterns, this appears to be 
potential command and control (C2) communication.

Key Analysis:
• Threat Level: HIGH (Confidence: 0.87)
• MITRE ATT&CK: T1071.001 (Web Protocols), T1041 (Exfiltration Over C2)
• IOCs Identified: ************ (known C2 server)
• Risk Factors: HTTPS on port 443 to blend with normal traffic

Immediate Recommendations:
1. Isolate affected system (*************)
2. Block outbound connections to ************
3. Investigate for persistence mechanisms
4. Check for lateral movement indicators

Would you like me to help with any specific investigation step?
```

#### Threat Intelligence Queries
```
You: Is this IP address known to be malicious?

Bitzy: Yes, IP ************ is a confirmed threat indicator:

Threat Intelligence Summary:
• Attribution: APT29 (Cozy Bear)
• First Seen: 2024-11-15
• Last Activity: 2024-12-10
• Campaign: Government/Healthcare targeting
• Malware: Cobalt Strike beacons

Technical Details:
• Port Usage: 443 (HTTPS), 8080 (HTTP)
• Communication: Encrypted C2 channels
• Persistence: Registry modifications, scheduled tasks
• Exfiltration: Staged data collection

This significantly elevates the alert priority. I recommend immediate containment.
```

#### Investigation Guidance
```
You: What should I investigate next?

Bitzy: Based on this C2 communication alert, here's a prioritized investigation plan:

🚨 IMMEDIATE (0-15 minutes):
1. Network isolation of *************
2. Preserve system state for forensics
3. Check firewall logs for other C2 communications
4. Alert incident response team

🔍 SHORT-TERM (15-60 minutes):
1. Analyze network logs for infection vector
2. Review email logs for phishing attempts
3. Check process execution on affected system
4. Scan for similar IOCs across environment

📊 EXTENDED (1-4 hours):
1. Full disk imaging for forensic analysis
2. Memory dump analysis for running malware
3. Lateral movement investigation
4. Timeline reconstruction

Specific commands and tools I can help with:
• Network analysis queries
• Log parsing techniques
• Forensic tool guidance
• Report generation

Which area would you like to focus on first?
```

### Advanced Features

#### Threat Hunting Assistance
```
You: Help me hunt for similar threats in our environment

Bitzy: Based on this C2 communication pattern, let's hunt for similar threats:

🎯 Hunt Hypothesis: APT29 infrastructure usage

Search Queries:
1. Network Indicators:
   destination_ip:(************ OR 198.51.100.* OR 192.0.2.*)
   protocol:HTTPS AND port:443 AND bytes_out:>1000

2. Process Indicators:
   process_name:powershell.exe AND command_line:*-enc*
   parent_process:winword.exe OR parent_process:excel.exe

3. File System Indicators:
   file_path:*\AppData\Roaming\* AND file_extension:.exe
   file_hash:(md5:a1b2c3... OR sha256:def456...)

4. Registry Indicators:
   registry_path:*\CurrentVersion\Run* AND registry_value:*temp*

Expected Results:
• 5-10 additional compromised systems
• Phishing email delivery mechanism
• Persistence mechanisms across environment

I can help analyze results and correlate findings. Shall we start with network indicators?
```

#### Report Generation
```
You: Generate an executive summary of this incident

Bitzy: **EXECUTIVE SUMMARY - APT29 C2 Communication Incident**

**Incident Classification:** Advanced Persistent Threat (APT)
**Severity:** HIGH | **Confidence:** 87% | **Status:** Active Investigation

**Executive Overview:**
A sophisticated cyber attack was detected involving APT29 (Cozy Bear) infrastructure. 
The threat actor established command and control communication from a compromised 
workstation, indicating potential data exfiltration and network compromise.

**Business Impact:**
• Affected Systems: 1 confirmed, 5-10 potentially compromised
• Data at Risk: Employee credentials, internal documents, network topology
• Operational Impact: Potential service disruption, data breach notification requirements
• Financial Impact: Estimated $50K-$200K in response costs and potential fines

**Technical Summary:**
• Attack Vector: Spear-phishing email with malicious attachment
• Persistence: Registry modifications, scheduled tasks
• C2 Infrastructure: ************ (confirmed APT29 server)
• Exfiltration: Staged data collection over HTTPS

**Response Actions Completed:**
✅ System isolation and containment
✅ Threat intelligence correlation
✅ Initial forensic preservation
✅ Stakeholder notification

**Next Steps (24-48 hours):**
1. Complete forensic analysis of affected systems
2. Network-wide threat hunt for additional compromises
3. Implement additional monitoring and detection rules
4. Coordinate with law enforcement if required

**Recommendations:**
• Immediate security awareness training for all staff
• Review and update email security controls
• Implement additional network segmentation
• Consider threat intelligence feed integration

*Report generated by Bitzy AI Assistant - AlertAI Platform*
```

## Configuration and Setup

### Ollama Integration

#### Installation
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull recommended model
ollama pull deepseek-r1:8b

# Verify installation
ollama list
```

#### AlertAI Configuration
```bash
# Environment variables
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:8b
OLLAMA_TIMEOUT=300

# Optional: GPU acceleration
OLLAMA_GPU=nvidia
```

### Model Selection

| Model | Size | Use Case | Performance |
|-------|------|----------|-------------|
| **deepseek-r1:8b** | 8GB | Balanced analysis and speed | Recommended |
| **llama2:13b** | 13GB | Complex technical analysis | High accuracy |
| **codellama:7b** | 7GB | Code and log analysis | Fast processing |
| **mistral:7b** | 7GB | Quick responses | Fastest |

### Performance Optimization

#### Hardware Requirements
- **CPU**: 8+ cores recommended
- **RAM**: 16GB minimum, 32GB recommended
- **GPU**: NVIDIA RTX 3060 or better (optional)
- **Storage**: SSD for model storage

#### Configuration Tuning
```bash
# Ollama performance settings
export OLLAMA_NUM_PARALLEL=4
export OLLAMA_MAX_LOADED_MODELS=2
export OLLAMA_FLASH_ATTENTION=1
```

## Best Practices

### Effective Questioning
1. **Be Specific**: "Analyze the network traffic in this alert" vs "What's wrong?"
2. **Provide Context**: Mention relevant background, environment details, or constraints
3. **Ask Follow-ups**: Build on previous responses for deeper analysis
4. **Use Examples**: Reference specific IOCs, timestamps, or patterns
5. **Request Formats**: Ask for specific output formats (lists, tables, commands)

### Investigation Workflow
1. **Start with Overview**: Get general alert analysis and threat assessment
2. **Dive into Details**: Focus on specific technical aspects or IOCs
3. **Seek Guidance**: Request step-by-step investigation procedures
4. **Validate Findings**: Cross-reference AI analysis with additional data sources
5. **Document Results**: Save key insights to alert comments and reports

### Documentation and Sharing
1. **Save Key Insights**: Copy important findings to alert comments
2. **Share with Team**: Include Bitzy analysis in incident reports
3. **Build Knowledge Base**: Use insights to improve detection rules and playbooks
4. **Track Accuracy**: Note when Bitzy analysis proves correct for continuous improvement
5. **Training Material**: Use conversations as training examples for new analysts

## Security and Compliance

### Privacy Protection
- **Local Processing**: All AI analysis happens within your infrastructure
- **No External Calls**: No data sent to external AI services or cloud providers
- **Data Residency**: All data remains within your geographic boundaries
- **Encryption**: All communications encrypted with TLS 1.3
- **Access Logging**: Complete audit trail of all AI interactions

### Compliance Features
- **SOC 2**: Comprehensive logging and access controls
- **GDPR**: Data processing transparency and user consent
- **HIPAA**: Healthcare data protection and audit trails
- **ISO 27001**: Security controls and risk management
- **PCI DSS**: Payment data protection standards

### Audit and Monitoring
```bash
# View Bitzy usage logs
curl -X GET "http://localhost:8002/api/audit?action=ai_chat" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Monitor AI performance metrics
curl -X GET "http://localhost:8002/api/ai/metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Troubleshooting

### Common Issues

#### Bitzy Not Responding
```bash
# Check Ollama service
systemctl status ollama

# Verify model availability
ollama list | grep deepseek-r1

# Test Ollama API
curl http://localhost:11434/api/tags

# Check AlertAI connection
curl http://localhost:8002/api/health
```

#### Slow Response Times
1. **Check System Resources**: Monitor CPU, RAM, and GPU usage
2. **Optimize Model**: Consider smaller model for faster responses
3. **Tune Configuration**: Adjust Ollama parallel processing settings
4. **Hardware Upgrade**: Add more RAM or GPU acceleration

#### Inaccurate Analysis
1. **Provide More Context**: Include additional alert details and background
2. **Verify Data Quality**: Ensure alert data is complete and accurate
3. **Update Models**: Check for newer model versions
4. **Fine-tune Prompts**: Adjust question phrasing for better results

### Diagnostic Commands
```bash
# System diagnostics
ollama ps                    # Check running models
nvidia-smi                   # Check GPU usage (if applicable)
htop                        # Monitor system resources

# AlertAI diagnostics
curl http://localhost:8002/api/ai/status
curl http://localhost:8002/api/ai/models
```

## Future Enhancements

### Planned Features
- **Custom Model Training**: Train models on your specific environment and threats
- **Automated Playbooks**: AI-generated response procedures and runbooks
- **Threat Prediction**: Proactive threat identification and early warning
- **Integration APIs**: Enhanced integration with external threat intelligence
- **Multi-modal Analysis**: Support for image and document analysis

### Community and Ecosystem
- **Model Marketplace**: Share and download specialized security models
- **Prompt Library**: Community-contributed question templates and use cases
- **Best Practices**: Shared investigation patterns and success stories
- **Performance Benchmarks**: Community-driven performance optimization

---

*For API integration details, see [AI Chat API](../api/chat.md)*
*For troubleshooting, see [Bitzy Troubleshooting](./troubleshooting.md)*
