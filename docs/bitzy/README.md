# AI Assistant (<PERSON><PERSON>)

<PERSON><PERSON> is AlertAI's intelligent assistant that helps you analyze alerts, understand threats, and make informed decisions about security incidents.

## What is <PERSON><PERSON>?

<PERSON><PERSON> is an AI-powered assistant that:
- **Analyzes alerts** with contextual understanding
- **Answers questions** about specific alerts and threats
- **Provides recommendations** for investigation and response
- **Correlates information** across multiple alerts and sources
- **Explains complex security concepts** in plain language

## How to Use Bitzy

### Accessing Bitzy

Bitzy appears as a floating chat bubble in the bottom-left corner of your screen:

1. **Chat Bubble**: Click the Bitzy icon to open the chat interface
2. **Expandable Interface**: The chat can be expanded for better readability
3. **Context Awareness**: <PERSON><PERSON> knows which page you're viewing and can provide relevant help

### Starting a Conversation

#### First-Time Users
When you first open Bitzy, you'll see a welcome message with:
- Introduction to <PERSON><PERSON>'s capabilities
- Suggested questions to get started
- Quick action buttons for common tasks

#### Returning Users
For returning users, <PERSON><PERSON> will:
- Show your recent chat history
- Provide context-specific suggestions
- Remember your preferences and previous conversations

### Types of Questions You Can Ask

#### Alert-Specific Questions
When viewing an alert, ask <PERSON><PERSON>:
- "What does this alert mean?"
- "How serious is this threat?"
- "What should I investigate first?"
- "Are there any related alerts?"
- "What are the potential attack vectors?"

#### General Security Questions
- "Explain this IP address reputation"
- "What is a SQL injection attack?"
- "How do I investigate a phishing alert?"
- "What are indicators of compromise?"

#### System and Process Questions
- "How do I create a new source?"
- "What's the best way to configure JSON mapping?"
- "How can I improve alert quality?"
- "What are AlertAI best practices?"

## Bitzy Features

### Context Awareness
Bitzy understands:
- **Current Page**: Knows if you're viewing alerts, sources, or documentation
- **Selected Alert**: Has full context of the alert you're investigating
- **User History**: Remembers your previous questions and preferences
- **System State**: Aware of your sources, alert volumes, and configurations

### Smart Suggestions
Bitzy provides:
- **Quick Actions**: Buttons for common tasks
- **Follow-up Questions**: Suggested next steps based on your inquiry
- **Related Topics**: Links to relevant documentation or alerts
- **Best Practices**: Recommendations based on security expertise

### Response Features
- **Markdown Support**: Rich formatting for better readability
- **Code Blocks**: Formatted examples and configurations
- **Copy to Clipboard**: Easy copying of scripts, commands, or configurations
- **Full-Screen View**: Expandable interface for detailed responses

## Chat Management

### Chat History
- **Persistent Storage**: Conversations are saved per user
- **Multiple Chats**: Start new conversations for different topics
- **Search History**: Find previous conversations and answers
- **Export Options**: Save important conversations for reference

### Privacy Options
- **Incognito Mode**: Chat without saving to history
- **Clear History**: Remove old conversations
- **Data Control**: Manage what information Bitzy can access

### Message Editing
- **Edit Messages**: Modify your questions after sending
- **Restart Conversations**: Remove messages after a certain point
- **Context Reset**: Start fresh while keeping history above

## Best Practices for Using Bitzy

### Asking Effective Questions

#### Be Specific
❌ "This alert looks weird"
✅ "This alert shows unusual network traffic from IP ************* to external domains. What should I investigate?"

#### Provide Context
❌ "Is this bad?"
✅ "I'm seeing multiple failed login attempts from different IPs. Is this a coordinated attack?"

#### Ask Follow-up Questions
- Build on previous answers
- Ask for clarification when needed
- Request specific examples or steps

### Getting the Most from Bitzy

#### Use for Learning
- Ask Bitzy to explain security concepts
- Request examples of attack patterns
- Learn about threat intelligence sources

#### Leverage for Investigation
- Get analysis of suspicious indicators
- Understand attack timelines
- Correlate events across alerts

#### Seek Recommendations
- Ask for next steps in investigations
- Get suggestions for preventive measures
- Request best practices for specific scenarios

## Advanced Features

### Alert Correlation
Bitzy can help identify:
- **Related Alerts**: Alerts from the same source or timeframe
- **Attack Patterns**: Sequences of events that indicate campaigns
- **False Positives**: Alerts that may not require action
- **Escalation Triggers**: When to involve additional teams

### Threat Intelligence Integration
Bitzy provides insights on:
- **IOC Analysis**: Reputation and context for IPs, domains, hashes
- **Threat Actor Attribution**: Known groups and their tactics
- **MITRE ATT&CK Mapping**: Techniques and procedures used
- **Campaign Tracking**: Ongoing threat campaigns and trends

### Response Guidance
Get help with:
- **Incident Response**: Step-by-step investigation procedures
- **Containment**: How to limit threat spread
- **Evidence Collection**: What data to preserve
- **Communication**: How to report findings

## Troubleshooting Bitzy

### Common Issues

**Bitzy not responding**
- Check your internet connection
- Refresh the page
- Try asking a simpler question

**Responses seem generic**
- Provide more specific context
- Include relevant alert details
- Ask follow-up questions for clarification

**Missing context about alerts**
- Ensure you're viewing the alert when asking
- Refresh the alert page
- Try asking about specific alert fields

### Getting Better Responses

#### Provide Context
Include relevant details like:
- Alert timestamps and sources
- Specific indicators (IPs, domains, hashes)
- Your role and investigation goals
- Previous actions taken

#### Use Clear Language
- Ask one question at a time
- Use specific security terminology when appropriate
- Break complex questions into smaller parts

#### Iterate and Refine
- Build on Bitzy's responses
- Ask for clarification when needed
- Request examples or additional details

## Privacy and Security

### Data Handling
- **Local Processing**: AI analysis can be done locally
- **No External Sharing**: Your data stays within your AlertAI instance
- **Audit Trails**: All interactions are logged for security
- **Access Controls**: Bitzy respects user permissions

### Best Practices
- Don't share sensitive credentials with Bitzy
- Use incognito mode for highly sensitive investigations
- Review responses for accuracy before taking action
- Maintain human oversight of AI recommendations

## Examples

### Investigating a Suspicious Alert
**You**: "I have an alert about unusual network traffic. The source IP is ************ and it's connecting to multiple domains. What should I check?"

**Bitzy**: "This pattern suggests potential data exfiltration or C2 communication. Here's what to investigate:

1. **IP Reputation**: Check threat intelligence for ************
2. **Domain Analysis**: Review the domains being contacted
3. **Traffic Volume**: Look at data transfer amounts
4. **Timeline**: Check when this activity started
5. **Internal Systems**: Identify which internal systems are involved

Would you like me to help analyze any specific aspect?"

### Understanding an Attack Technique
**You**: "What is a living-off-the-land attack?"

**Bitzy**: "Living-off-the-land (LotL) attacks use legitimate tools and processes already present on a system to carry out malicious activities. This makes detection difficult because the tools appear normal.

**Common Examples**:
- PowerShell for command execution
- WMI for persistence
- Scheduled tasks for persistence
- Built-in network tools for reconnaissance

**Detection Tips**:
- Monitor for unusual command-line arguments
- Look for legitimate tools in unexpected contexts
- Check for process injection techniques
- Monitor for suspicious network connections from system tools

Would you like specific detection rules or examples?"

---

*Next: [User Guides](../user-guides/dashboard.md)*
