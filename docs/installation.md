# AlertAI Installation Guide

Complete installation guide for AlertAI with enterprise security features, authentication, and AI capabilities.

## System Requirements

### Minimum Requirements
- **Operating System**: Linux (Ubuntu 20.04+), macOS 10.15+, Windows 10+
- **Python**: 3.9 or higher
- **Node.js**: 18.0 or higher
- **Memory**: 4GB RAM minimum
- **Storage**: 10GB free space
- **Network**: Internet access for initial setup

### Recommended Requirements
- **Operating System**: Ubuntu 22.04 LTS or CentOS 8+
- **Python**: 3.11+
- **Node.js**: 20.0+
- **Memory**: 16GB RAM (for AI features)
- **Storage**: 50GB SSD
- **CPU**: 8+ cores
- **GPU**: NVIDIA RTX 3060+ (optional, for AI acceleration)

### Production Requirements
- **Operating System**: Linux (Ubuntu 22.04 LTS recommended)
- **Database**: PostgreSQL 12+ (SQLite for development only)
- **Memory**: 32GB RAM
- **Storage**: 100GB+ SSD with backup
- **Network**: Load balancer, SSL certificates
- **Monitoring**: Log aggregation and monitoring system

## Pre-Installation Setup

### 1. System Updates

#### Ubuntu/Debian
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y git curl wget build-essential python3-dev python3-pip nodejs npm
```

#### CentOS/RHEL
```bash
sudo yum update -y
sudo yum groupinstall -y "Development Tools"
sudo yum install -y git curl wget python3-devel python3-pip nodejs npm
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install git python@3.11 node
```

### 2. Database Setup (Production)

#### PostgreSQL Installation
```bash
# Ubuntu/Debian
sudo apt install -y postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install -y postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl enable postgresql
sudo systemctl start postgresql
```

#### Database Configuration
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE alertai;
CREATE USER alertai_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE alertai TO alertai_user;
\q
```

### 3. AI Service Setup (Optional)

#### Install Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve &

# Pull AI model
ollama pull deepseek-r1:8b
```

## Installation Methods

### Method 1: Quick Install (Recommended)

#### 1. Clone Repository
```bash
git clone https://github.com/your-org/alertai.git
cd alertai
```

#### 2. Run Installation Script
```bash
# Make script executable
chmod +x install.sh

# Run installation
./install.sh
```

The installation script will:
- Install all dependencies
- Set up virtual environments
- Configure databases
- Initialize the system
- Start all services

#### 3. Access AlertAI
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8002
- **API Docs**: http://localhost:8002/docs (development mode)

### Method 2: Manual Installation

#### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

#### 2. Configure Environment

Edit `.env` file with your settings:

```bash
# CRITICAL: Change these for production
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-long
DEBUG=false

# Database configuration
DATABASE_URL=postgresql://alertai_user:secure_password@localhost:5432/alertai
# DATABASE_URL=sqlite:///./alertai.db  # Development only

# API Configuration
API_HOST=0.0.0.0
API_PORT=8002

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
FRONTEND_URLS=http://localhost:3001,https://yourdomain.com

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# AI Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:8b

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/plain

# Logging
LOG_LEVEL=INFO
ENABLE_AUDIT_LOGGING=true
```

#### 3. Initialize Database

```bash
# Run database migrations
python -m alembic upgrade head

# Initialize the system (creates admin user)
python -c "
from app.db.connection import init_db
init_db()
print('Database initialized successfully')
"
```

#### 4. Start Backend

```bash
# Development
python start_server.py

# Production
uvicorn app.main:app --host 0.0.0.0 --port 8002 --workers 4
```

#### 5. Frontend Setup

```bash
# Navigate to frontend directory
cd ../frontend

# Install dependencies
npm install

# Create environment file
cp .env.example .env.local
```

Edit `.env.local`:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8002

# Feature flags
NEXT_PUBLIC_AUTH_ENABLED=true
NEXT_PUBLIC_ENABLE_CHAT=true
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=true

# Optional: Analytics
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
```

#### 6. Start Frontend

```bash
# Development
npm run dev

# Production build
npm run build
npm start
```

### Method 3: Docker Installation

#### 1. Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: alertai
      POSTGRES_USER: alertai_user
      POSTGRES_PASSWORD: secure_password_here
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=************************************************************/alertai
      - SECRET_KEY=your-super-secure-secret-key-here
      - DEBUG=false
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    ports:
      - "8002:8002"
    depends_on:
      - postgres
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8002
    ports:
      - "3001:3000"
    depends_on:
      - backend
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

volumes:
  postgres_data:
  ollama_data:
```

#### 2. Start Services

```bash
# Build and start all services
docker-compose up -d

# Pull AI model
docker-compose exec ollama ollama pull deepseek-r1:8b

# Check service status
docker-compose ps
```

## Post-Installation Configuration

### 1. System Initialization

Initialize the system with an admin user:

```bash
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

### 2. Verify Installation

Run the verification script:

```bash
#!/bin/bash
echo "🧪 Verifying AlertAI Installation..."

# Test backend health
echo "1. Testing backend health..."
HEALTH=$(curl -s http://localhost:8002/api/health)
if echo $HEALTH | grep -q "healthy"; then
  echo "✅ Backend is healthy"
else
  echo "❌ Backend health check failed"
  exit 1
fi

# Test authentication
echo "2. Testing authentication..."
TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=SecureAdminPassword123!")

if echo $TOKEN_RESPONSE | grep -q "access_token"; then
  echo "✅ Authentication working"
else
  echo "❌ Authentication failed"
  exit 1
fi

# Test frontend
echo "3. Testing frontend..."
FRONTEND=$(curl -s http://localhost:3001)
if [ $? -eq 0 ]; then
  echo "✅ Frontend accessible"
else
  echo "❌ Frontend not accessible"
  exit 1
fi

# Test AI service (if enabled)
echo "4. Testing AI service..."
AI_STATUS=$(curl -s http://localhost:11434/api/tags)
if [ $? -eq 0 ]; then
  echo "✅ AI service accessible"
else
  echo "⚠️ AI service not accessible (optional)"
fi

echo "🎉 AlertAI installation verified successfully!"
```

### 3. Security Hardening

#### SSL/TLS Configuration
```bash
# Generate SSL certificate (Let's Encrypt)
sudo certbot --nginx -d yourdomain.com

# Or use your own certificates
sudo mkdir -p /etc/ssl/alertai
sudo cp your-cert.pem /etc/ssl/alertai/
sudo cp your-key.pem /etc/ssl/alertai/
sudo chmod 600 /etc/ssl/alertai/*
```

#### Firewall Configuration
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Block direct access to application ports
sudo ufw deny 3001
sudo ufw deny 8002
```

### 4. Monitoring Setup

#### Log Configuration
```bash
# Create log directories
sudo mkdir -p /var/log/alertai
sudo chown alertai:alertai /var/log/alertai

# Configure log rotation
sudo tee /etc/logrotate.d/alertai << EOF
/var/log/alertai/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 alertai alertai
}
EOF
```

#### Health Monitoring
```bash
# Create health check script
cat > /usr/local/bin/alertai-health.sh << 'EOF'
#!/bin/bash
curl -f http://localhost:8002/api/health || exit 1
curl -f http://localhost:3001/ || exit 1
EOF

chmod +x /usr/local/bin/alertai-health.sh

# Add to crontab for monitoring
echo "*/5 * * * * /usr/local/bin/alertai-health.sh" | crontab -
```

## Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check Python version
python3 --version

# Check dependencies
pip list | grep -E "(fastapi|uvicorn|sqlalchemy)"

# Check database connection
python -c "from app.db.connection import engine; print('DB OK')"

# Check logs
tail -f logs/application.log
```

#### Frontend Build Fails
```bash
# Check Node.js version
node --version
npm --version

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for port conflicts
lsof -i :3001
```

#### Authentication Issues
```bash
# Verify secret key is set
grep SECRET_KEY .env

# Check database tables
python -c "
from app.db.connection import engine
from sqlalchemy import inspect
inspector = inspect(engine)
print('Tables:', inspector.get_table_names())
"

# Reset admin user
python -c "
from app.core.auth import AuthService
from app.db.connection import get_db
# Reset admin user code here
"
```

#### AI Service Issues
```bash
# Check Ollama status
ollama ps

# Check model availability
ollama list

# Test model
ollama run deepseek-r1:8b "Hello, test message"

# Check AlertAI AI connection
curl http://localhost:8002/api/ai/status
```

### Getting Help

1. **Check Logs**: Review application and system logs
2. **Verify Configuration**: Ensure all environment variables are set
3. **Test Components**: Test each service individually
4. **Documentation**: Refer to specific component documentation
5. **Community**: Check GitHub issues and discussions

## Next Steps

After successful installation:

1. **[Authentication Setup](./authentication/setup.md)** - Configure users and security
2. **[Quick Start Guide](./quick-start.md)** - Get started with basic features
3. **[Sources Management](./sources/README.md)** - Configure alert sources
4. **[Security Best Practices](./authentication/security.md)** - Secure your deployment

---

*For production deployment, see [Deployment Guide](../DEPLOYMENT_GUIDE.md)*
