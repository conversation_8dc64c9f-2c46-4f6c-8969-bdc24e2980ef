# AlertAI Tools Documentation

Comprehensive guide to AI tools available in AlertAI for enhanced threat intelligence and analysis.

## Overview

AlertAI includes a powerful tools system that extends the AI assistant (<PERSON><PERSON>) with external threat intelligence capabilities. These tools automatically enhance alert analysis by providing real-time threat intelligence from various sources.

## Available Tools

### 🦠 VirusTotal Integration

**Purpose**: Automatic IOC (Indicators of Compromise) analysis using VirusTotal's comprehensive threat intelligence database.

**Capabilities**:
- Automatic detection of IOCs in alert data
- Multi-engine malware detection results
- Threat classification and risk assessment
- Historical analysis and community insights
- Support for multiple IOC types

**Supported IOC Types**:
- **IP Addresses**: IPv4 addresses for reputation checking
- **Domains**: Domain names and subdomains
- **URLs**: Complete URLs for malicious site detection
- **File Hashes**: MD5, SHA1, and SHA256 hashes

**How It Works**:
1. **Automatic Detection**: When <PERSON><PERSON> analyzes an alert, it automatically scans for IOCs
2. **Intelligent Filtering**: Filters out common false positives (private IPs, legitimate domains)
3. **Batch Processing**: Efficiently processes multiple IOCs with rate limiting
4. **Contextual Results**: Provides threat intelligence in the context of the alert

## Configuration

### VirusTotal Setup

#### 1. Get API Key

1. **Sign up** at [VirusTotal](https://www.virustotal.com/)
2. **Navigate** to your profile settings
3. **Generate** an API key
4. **Copy** the API key for configuration

#### 2. Configure AlertAI

Add your VirusTotal API key to the backend environment:

```bash
# In backend/.env
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
VIRUSTOTAL_RATE_LIMIT_DELAY=15.0
```

#### 3. Restart Services

```bash
# Restart the backend to load new configuration
cd backend
python start_server.py
```

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `VIRUSTOTAL_API_KEY` | VirusTotal API key | None | No* |
| `VIRUSTOTAL_RATE_LIMIT_DELAY` | Delay between API calls (seconds) | 15.0 | No |

*\* If not provided, tools will use mock data for demonstration*

## Usage

### Automatic Integration

The VirusTotal tool works automatically with Bitzy:

1. **Create or view an alert** containing IOCs
2. **Start a chat** with Bitzy about the alert
3. **Bitzy automatically detects** IOCs in the alert data
4. **VirusTotal analysis** is performed automatically
5. **Results are included** in Bitzy's response

### Example Alert Data

Alerts containing these types of data will trigger automatic VirusTotal analysis:

```json
{
  "title": "Suspicious Network Activity",
  "description": "Outbound connection to ************ detected",
  "metadata": {
    "source_ip": "*************",
    "destination_ip": "************",
    "domain": "suspicious-domain.com",
    "file_hash": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234"
  }
}
```

### Sample AI Response

When IOCs are detected, Bitzy's response will include:

```
🔍 **Alert Analysis**

This alert shows suspicious outbound network activity from an internal host to an external IP address. Let me check the threat intelligence for the IOCs found.

🔍 **VirusTotal Analysis Results:**
- Total IOCs checked: 2
- Malicious IOCs: 1
- Suspicious IOCs: 0
- Clean IOCs: 1

**Recommendations:**
- CRITICAL: Malicious IOCs detected. Immediate containment recommended.
- Block outbound connections to ************
- Investigate other systems for similar activity

**Next Steps:**
1. Isolate the affected host (*************)
2. Check for persistence mechanisms
3. Review network logs for similar connections
```

## Tool Architecture

### Design Principles

The AlertAI tools system follows LangChain best practices:

1. **Standardized Interface**: All tools implement the `BaseTool` interface
2. **Error Handling**: Comprehensive error handling with graceful degradation
3. **Rate Limiting**: Built-in rate limiting to respect API limits
4. **Caching**: Intelligent caching to avoid duplicate requests
5. **Logging**: Detailed logging for debugging and monitoring

### Tool Structure

```
backend/app/tools/
├── __init__.py          # Package initialization
├── base.py              # Base classes and interfaces
├── virustotal.py        # VirusTotal tool implementation
└── manager.py           # Tool management and coordination
```

### Base Classes

#### `BaseTool`
- Extends LangChain's `BaseTool`
- Provides standardized error handling
- Implements logging and monitoring
- Ensures consistent result formatting

#### `ThreatIntelTool`
- Specialized base class for threat intelligence tools
- IOC extraction and classification
- Common threat intelligence patterns
- False positive filtering

#### `ToolManager`
- Centralized tool management
- Configuration handling
- Tool discovery and initialization
- Health monitoring

## API Reference

### VirusTotal Tool

#### Class: `VirusTotalTool`

```python
from app.tools.virustotal import VirusTotalTool

# Initialize tool
vt_tool = VirusTotalTool(
    api_key="your-api-key",
    rate_limit_delay=15.0
)

# Search IOCs
result = vt_tool._run("*************, suspicious-domain.com")
```

#### Methods

##### `_run(query: str) -> str`
Execute VirusTotal lookup for IOCs.

**Parameters**:
- `query`: IOCs to search (single IOC or text containing IOCs)

**Returns**: JSON string with analysis results

##### `_extract_and_classify_iocs(query: str) -> Dict[str, List[str]]`
Extract and classify IOCs from input text.

**Returns**: Dictionary with IOC types as keys and lists of IOCs as values

### Tool Manager

#### Class: `ToolManager`

```python
from app.tools.manager import get_tool_manager

# Get global tool manager
manager = get_tool_manager()

# Get available tools
tools = manager.get_all_tools()

# Get tool status
status = manager.get_tool_status()
```

#### Methods

##### `get_tool(tool_name: str) -> Optional[BaseTool]`
Get a specific tool by name.

##### `get_all_tools() -> List[BaseTool]`
Get all available tools.

##### `get_tool_status() -> Dict[str, Dict[str, Any]]`
Get status information for all tools.

##### `test_tool(tool_name: str, test_input: str) -> Dict[str, Any]`
Test a tool with sample input.

## Testing

### Test Script

Run the comprehensive test suite:

```bash
cd backend
python test_virustotal_tool.py
```

### Test Coverage

The test suite covers:
- ✅ Basic tool functionality
- ✅ Multiple IOC processing
- ✅ Tool manager integration
- ✅ IOC extraction accuracy
- ✅ Ollama service integration
- ✅ Error handling
- ✅ Rate limiting
- ✅ Mock data fallback

### Manual Testing

Test the integration manually:

1. **Create a test alert** with IOCs:
   ```bash
   curl -X POST "http://localhost:8002/api/alerts" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "title": "Test Alert",
       "description": "Suspicious activity from *******",
       "severity": "medium"
     }'
   ```

2. **Chat with Bitzy** about the alert
3. **Verify** VirusTotal results are included

## Troubleshooting

### Common Issues

#### "VirusTotal API key not configured"
- **Cause**: API key not set in environment
- **Solution**: Add `VIRUSTOTAL_API_KEY` to `.env` file
- **Workaround**: Tool will use mock data for demonstration

#### "Rate limit exceeded"
- **Cause**: Too many API requests
- **Solution**: Increase `VIRUSTOTAL_RATE_LIMIT_DELAY`
- **Default**: 15 seconds between requests

#### "No IOCs found"
- **Cause**: Alert data doesn't contain recognizable IOCs
- **Solution**: Ensure alert contains IP addresses, domains, URLs, or hashes
- **Check**: IOC extraction patterns in tool logs

#### "Tool execution failed"
- **Cause**: Network issues or API errors
- **Solution**: Check network connectivity and API status
- **Fallback**: Tool will return error message instead of crashing

### Debug Mode

Enable detailed logging:

```bash
# In backend/.env
LOG_LEVEL=DEBUG
```

View tool execution logs:

```bash
tail -f backend/logs/application.log | grep -i virustotal
```

### Health Check

Check tool status via API:

```bash
curl -X GET "http://localhost:8002/api/tools/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Performance Considerations

### Rate Limiting

- **VirusTotal Free**: 4 requests per minute
- **VirusTotal Premium**: Higher limits available
- **AlertAI Default**: 15-second delay between requests
- **Recommendation**: Adjust based on your API tier

### Caching

- **IOC Results**: Cached for 1 hour by default
- **Duplicate Prevention**: Same IOCs won't be re-queried
- **Cache Invalidation**: Automatic cleanup of old entries

### Optimization Tips

1. **Batch Processing**: Multiple IOCs processed in single request when possible
2. **Smart Filtering**: Common false positives filtered out automatically
3. **Prioritization**: Critical IOCs processed first
4. **Fallback**: Graceful degradation when tools unavailable

## Security Considerations

### API Key Security

- **Environment Variables**: Store API keys in environment, not code
- **Access Control**: Limit API key permissions in VirusTotal
- **Rotation**: Regularly rotate API keys
- **Monitoring**: Monitor API usage for anomalies

### Data Privacy

- **IOC Sharing**: IOCs are shared with VirusTotal for analysis
- **Logging**: Tool logs may contain IOC data
- **Retention**: Configure appropriate log retention policies
- **Compliance**: Ensure compliance with data protection regulations

## Future Enhancements

### Planned Features

- **Additional Sources**: Integration with more threat intelligence providers
- **Custom Rules**: User-defined IOC extraction patterns
- **Bulk Analysis**: Batch processing for large alert volumes
- **Historical Analysis**: Trend analysis and IOC tracking
- **Integration APIs**: Direct API access to tool results

### Contributing

To add new tools:

1. **Extend** `BaseTool` or `ThreatIntelTool`
2. **Implement** required methods
3. **Add** to `ToolManager`
4. **Create** tests and documentation
5. **Submit** pull request

---

*For additional help, see [API Documentation](../api/README.md) and [Troubleshooting Guide](../troubleshooting/common-issues.md)*
