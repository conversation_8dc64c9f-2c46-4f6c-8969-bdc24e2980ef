# Sources Management

Sources are external systems that send alerts to AlertAI via webhooks. They are the entry points for all alert data in your AlertAI system.

## What is a Source?

A **Source** is a configured webhook endpoint that receives alerts from external monitoring systems, security tools, or applications. Each source acts as a bridge between your external systems and AlertAI, standardizing incoming alert data into a consistent format.

### Key Components of a Source

1. **Webhook URL** - Unique endpoint for receiving alerts
2. **Authentication** - Security configuration (custom headers or bearer tokens)
3. **JSON Mapping** - Rules for transforming incoming data into standard alert fields
4. **Processing Settings** - Configuration for how alerts are handled

## Creating a New Source

### Step 1: Navigate to Sources
1. Open AlertAI in your browser
2. Click on **"Sources"** in the left navigation menu
3. Click the **"Add Source"** button in the top-right corner

### Step 2: Basic Information
Fill in the basic details for your source:

- **Source Name** (required): A descriptive name for your source
  - Example: "Production Monitoring", "Security Scanner", "Application Logs"
- **Description** (optional): Additional details about what this source monitors
  - Example: "Alerts from our production Kubernetes cluster monitoring"

### Step 3: Authentication Configuration
Choose how external systems will authenticate with your webhook:

#### Option A: Custom Header
- **Authentication Type**: Select "Custom Header"
- **Header Name**: The HTTP header name (e.g., "X-API-Key", "Authorization")
- **Header Value**: The secret value that must be included in requests

#### Option B: Bearer Token
- **Authentication Type**: Select "Bearer Token"
- **Bearer Token**: The token value that will be sent in the Authorization header

### Step 4: JSON Schema Mapping
Configure how incoming JSON fields map to AlertAI's standard alert fields:

#### Required Fields
- **Title Field**: JSON path to the alert title (e.g., "alert.title", "message")
- **Description Field**: JSON path to the alert description (e.g., "alert.description", "details")
- **Severity Field**: JSON path to the severity level (e.g., "alert.severity", "priority")

#### Optional Fields
- **Source Field**: JSON path to identify the alert source (e.g., "system.name", "host")

#### Custom Field Mappings
Add additional field mappings for any extra data you want to capture:
- Click **"Add Custom Field Mapping"**
- Enter the **Alert field name** (how it will appear in AlertAI)
- Enter the **JSON path** (where to find it in the incoming data)

### Step 5: Save and Test
1. Click **"Create Source"** to save your configuration
2. Your new source will appear in the sources list with a unique webhook URL
3. Use the **"Test"** button to verify your configuration works correctly

## JSON Path Examples

Understanding JSON paths is crucial for proper field mapping:

### Simple Fields
```json
{
  "title": "Database Connection Failed",
  "severity": "high"
}
```
- Title path: `title`
- Severity path: `severity`

### Nested Fields
```json
{
  "alert": {
    "title": "Database Connection Failed",
    "details": {
      "severity": "high",
      "host": "db-server-01"
    }
  }
}
```
- Title path: `alert.title`
- Severity path: `alert.details.severity`
- Host path: `alert.details.host`

### Array Fields
```json
{
  "alerts": [
    {
      "title": "High CPU Usage",
      "severity": "medium"
    }
  ]
}
```
- Title path: `alerts.0.title` (for first alert)
- Severity path: `alerts.0.severity`

## Testing Your Source

### Using the Test Dialog
1. Click the **"Test"** button next to your source
2. Enter a sample JSON payload that matches your expected format
3. Click **"Test Source"** to see how AlertAI will process the data
4. Review the results to ensure fields are mapped correctly

### Sample Test Payload
```json
{
  "alert": {
    "title": "High Memory Usage",
    "message": "Server memory usage exceeded 90%",
    "severity": "warning"
  },
  "system": {
    "name": "web-server-01",
    "environment": "production"
  },
  "timestamp": "2025-06-15T10:30:00Z"
}
```

## Managing Existing Sources

### Editing a Source
1. Find your source in the sources list
2. Click the **"Edit"** button
3. Modify any settings as needed
4. Click **"Save Changes"**

### Viewing Source Details
Each source displays:
- **Status**: Active/Inactive indicator
- **Authentication Type**: How the source is secured
- **Alerts Created**: Number of alerts received
- **Last Used**: When the source last received an alert
- **Webhook URL**: The endpoint for receiving alerts

### Deactivating a Source
1. Edit the source
2. Toggle the **"Active"** switch to off
3. Save changes

Deactivated sources will reject incoming webhooks until reactivated.

## Best Practices

### Naming Conventions
- Use descriptive, consistent names
- Include environment information (e.g., "Prod-Kubernetes", "Dev-AppLogs")
- Avoid special characters that might cause issues

### Security
- Use strong, unique authentication values
- Rotate authentication credentials regularly
- Monitor source usage for suspicious activity

### JSON Mapping
- Test mappings thoroughly with real data
- Use consistent field names across sources when possible
- Document any custom field mappings for your team

### Monitoring
- Regularly check source health and activity
- Monitor alert volumes for anomalies
- Review and update mappings as source systems evolve

## Troubleshooting

### Common Issues

**Source not receiving alerts**
- Verify the webhook URL is correct
- Check authentication configuration
- Ensure the external system is sending requests

**Alerts missing data**
- Review JSON mapping configuration
- Test with sample payloads
- Check for typos in field paths

**Authentication failures**
- Verify header names and values
- Check for extra spaces or special characters
- Ensure external system is sending correct credentials

### Getting Help
- Use the test functionality to debug issues
- Check the [Troubleshooting Guide](../troubleshooting/common-issues.md)
- Ask Bitzy (AI assistant) for help with specific configurations

---

*Next: [Alert Management](../alerts/README.md)*
