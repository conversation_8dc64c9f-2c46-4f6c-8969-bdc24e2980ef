# Quick Start Guide

Get AlertAI up and running in 5 minutes with enterprise security features, authentication, and AI capabilities.

## Prerequisites

Before you begin, ensure you have:
- Python 3.9 or higher installed
- Node.js 18 or higher installed
- Git installed
- At least 8GB of available RAM (16GB recommended for AI features)
- Internet connection for downloading dependencies

## Step 1: Installation

### Quick Install

```bash
# Clone the repository
git clone https://github.com/your-org/alertai.git
cd alertai

# Run the installation script
chmod +x install.sh
./install.sh
```

### Manual Setup (Alternative)

```bash
# Backend setup
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Frontend setup
cd ../frontend
npm install
```

## Step 2: Configure Environment

### Backend Configuration

```bash
cd backend
cp .env.example .env
```

Edit `.env` with essential settings:

```bash
# CRITICAL: Change this secret key
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-long

# API Configuration
API_PORT=8002
DEBUG=false

# Database (SQLite for quick start)
DATABASE_URL=sqlite:///./alertai.db

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
FRONTEND_URLS=http://localhost:3001
```

### Frontend Configuration

```bash
cd frontend
cp .env.example .env.local
```

Edit `.env.local`:

```bash
NEXT_PUBLIC_API_URL=http://localhost:8002
NEXT_PUBLIC_AUTH_ENABLED=true
```

## Step 3: Start Services

```bash
# Start backend (in one terminal)
cd backend
python start_server.py

# Start frontend (in another terminal)
cd frontend
npm run dev
```

Services will be available at:
- **Backend API**: http://localhost:8002
- **Frontend**: http://localhost:3001

## Step 4: Initialize System

### Create Admin User

```bash
curl -X POST "http://localhost:8002/api/auth/init-system" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecureAdminPassword123!",
    "full_name": "System Administrator"
  }'
```

### Login to Get Token

```bash
curl -X POST "http://localhost:8002/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=SecureAdminPassword123!"
```

Save the returned `access_token` for API calls.

## Step 5: Access AlertAI

1. **Open your web browser**
2. **Navigate to http://localhost:3001**
3. **Login with admin credentials**:
   - Email: `<EMAIL>`
   - Password: `SecureAdminPassword123!`
4. **You should see the AlertAI dashboard**

## Step 6: Create Your First Alert

### Manual Alert Creation

Create a test alert using the API:

```bash
# Replace YOUR_TOKEN with your actual access token
curl -X POST "http://localhost:8002/api/alerts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Test Security Alert",
    "description": "Suspicious network activity detected from workstation",
    "severity": "high",
    "source": "Network Monitoring",
    "metadata": {
      "source_ip": "*************",
      "destination_ip": "************",
      "port": 443,
      "protocol": "HTTPS"
    },
    "tags": ["network", "suspicious", "outbound"]
  }'
```

### Verify Alert Creation

1. **Navigate to Alerts**: Click "Alerts" in the navigation
2. **Find Your Alert**: You should see your test alert
3. **Click on Alert**: View the full alert details

## Step 7: Explore Alert Features

### View Alert Details

1. **Basic Information**: Title, description, severity, and timestamps
2. **Metadata**: Technical details like IP addresses and ports
3. **Tags**: Classification labels for filtering and organization
4. **Audit Trail**: Complete history of changes and actions

### Add Comments

1. **Find Comments Section**: Scroll to the comments area
2. **Add Investigation Notes**:
   ```
   Initial investigation started. Checking network logs for suspicious activity.
   Destination IP ************ appears to be external C2 server.
   ```
3. **Save Comment**: Your comment is logged with timestamp

### Upload Evidence

1. **Find File Upload Section**: Look for "Attachments" area
2. **Upload Files**: Drag and drop evidence files
3. **Add Descriptions**: Describe what each file contains
4. **Secure Storage**: Files are validated and securely stored

## Step 5: Explore Your Alert

Click on your alert to see the detailed view:

1. **Alert Details**
   - View the basic information (title, description, severity)
   - See the timestamp and source information
   - Check the raw payload data

2. **AI Analysis**
   - Scroll down to see the threat intelligence section
   - Try asking Bitzy (the AI assistant) about the alert

3. **Take Action**
   - Add comments to document your investigation
   - Change the alert status as needed
   - Use the AI assistant for analysis and recommendations

## Step 6: Set Up Real Sources

Now that you understand the basics, set up sources for your actual monitoring systems:

1. **Identify Your Systems**
   - List all systems that should send alerts to AlertAI
   - Examples: monitoring tools, security scanners, application logs

2. **Configure Each Source**
   - Create a source for each system
   - Use descriptive names and strong authentication
   - Map JSON fields based on each system's alert format

3. **Test Thoroughly**
   - Use the test functionality for each source
   - Send real test alerts to verify everything works
   - Monitor the alerts page for incoming data

## Next Steps

Congratulations! You now have AlertAI up and running. Here's what to explore next:

### Learn More About Features
- **[Sources Management](./sources/README.md)** - Deep dive into source configuration
- **[Alert Management](./alerts/README.md)** - Advanced alert handling techniques
- **[AI Assistant](./bitzy/README.md)** - Get the most out of Bitzy

### Advanced Configuration
- Set up multiple sources for different systems
- Configure custom field mappings for complex data
- Explore threat intelligence features
- Set up user management and permissions

### Integration Examples
- **Monitoring Tools**: Prometheus, Grafana, Nagios
- **Security Tools**: SIEM systems, vulnerability scanners
- **Application Logs**: Custom applications, microservices
- **Cloud Services**: AWS CloudWatch, Azure Monitor

## Troubleshooting

### Common Issues

**Source not receiving alerts**
- Check the webhook URL is correct
- Verify authentication headers match
- Ensure your external system can reach AlertAI

**Alerts missing data**
- Review JSON field mappings
- Test with sample payloads
- Check for typos in field paths

**Can't see alerts**
- Refresh the alerts page
- Check if alerts are being filtered
- Verify the source is active

### Getting Help

- Use the **search** in this documentation
- Ask **Bitzy** (AI assistant) for help with specific issues
- Check the **[Troubleshooting Guide](./troubleshooting/common-issues.md)**

## Summary

You've successfully:
✅ Created your first source
✅ Configured authentication and field mapping
✅ Tested the source with sample data
✅ Sent a real alert to AlertAI
✅ Explored the alert details and AI features

AlertAI is now ready to receive and help you manage alerts from your systems!

---

*Next: [Sources Management Deep Dive](./sources/README.md)*
