# System-Wide Logging Infrastructure

## Overview

AlertAI features a comprehensive system-wide logging infrastructure that captures ALL user interactions and system events in a standardized JSON schema. This provides complete visibility into system activities for security, compliance, debugging, and user behavior analysis.

## Features

### 🔍 **Comprehensive Event Tracking**
- **Alert Management**: Creation, updates, deletion, status changes, assignments
- **Bitzy AI Interactions**: All chat messages, responses, file uploads to AI, AI suggestions used
- **Source Management**: Webhook source creation, updates, deletion, configuration changes
- **User Actions**: Login/logout, page navigation, search queries, filter usage
- **File Operations**: Uploads, downloads, deletions
- **Comment System**: Comment creation, editing, deletion
- **System Events**: API calls, background tasks, errors, performance metrics

### 📊 **Structured Logging Schema**
Every log entry follows a standardized JSON schema with consistent fields:
- `timestamp` (ISO 8601 format)
- `event_type` (e.g., "alert_created", "bitzy_interaction", "user_login")
- `user_id` and `username` (who performed the action)
- `ip_address` (client IP)
- `user_agent` (browser/client info)
- `session_id` (user session identifier)
- `resource_type` and `resource_id` (what was affected)
- `action` (specific action taken)
- `details` (additional context as JSON object)
- `success` (boolean indicating if action succeeded)
- `error_message` (if action failed)
- `response_time_ms` (performance metric)
- `log_level` (INFO, WARN, ERROR, DEBUG)
- `checksum` (SHA-256 hash for tamper detection)

### 🔒 **Security & Compliance**
- **Tamper-Proof Logs**: SHA-256 checksums for integrity verification
- **Complete Audit Trail**: Every action is logged with full context
- **User Attribution**: Tracks who performed each action
- **IP Address Logging**: Records client IP addresses
- **Session Tracking**: Links actions to user sessions
- **Error Tracking**: Captures and logs all system errors

### ⚡ **Performance Monitoring**
- **Response Time Tracking**: Measures API response times
- **Request Size Monitoring**: Tracks payload sizes
- **Error Rate Analysis**: Calculates error rates over time
- **Resource Usage**: Monitors system resource consumption

## Log Viewer Interface

### **System Logs Page** (`/logs`)
- **Real-time log streaming** with auto-refresh capability
- **Advanced filtering** by date range, event type, user, IP address, resource type
- **Search functionality** across all log fields with regex support
- **Export capabilities** to JSON, CSV, and formatted text files
- **Log level filtering** (INFO, WARN, ERROR, DEBUG)
- **Pagination** for large log volumes
- **Visual indicators** for different event types and severity levels

### **Analytics Dashboard**
- **System Statistics**: Total logs, daily activity, error rates
- **Top Event Types**: Most frequent system activities
- **User Activity**: Most active users and IP addresses
- **Performance Metrics**: Average response times and system health
- **Trend Analysis**: Activity patterns over time

## Event Types

### **Alert Management** (`alert_management`)
- **create**: New alert created
- **update**: Alert fields modified
- **delete**: Alert deleted (soft or hard)
- **view**: Alert viewed by user
- **assign**: Alert assigned to analyst
- **escalate**: Alert escalation level changed

### **Bitzy AI Interactions** (`bitzy_interaction`)
- **chat_message**: User sent message to Bitzy
- **ai_response**: Bitzy responded to user
- **file_upload**: File uploaded to AI for analysis
- **suggestion_used**: User accepted AI suggestion
- **context_request**: AI requested page context

### **Source Management** (`source_management`)
- **create**: New webhook source created
- **update**: Source configuration updated
- **delete**: Source deleted
- **webhook_received**: Webhook payload received
- **alert_generated**: Alert created from webhook

### **User Actions** (`user_action`)
- **login**: User logged into system
- **logout**: User logged out
- **page_view**: User navigated to page
- **search**: User performed search
- **filter**: User applied filters
- **export**: User exported data

### **File Operations** (`file_operation`)
- **upload**: File uploaded to alert
- **download**: File downloaded
- **delete**: File deleted
- **view**: File viewed or accessed

### **Comment Management** (`comment_management`)
- **create**: Comment added to alert
- **update**: Comment edited
- **delete**: Comment deleted
- **mention**: User mentioned in comment

## API Endpoints

### **Get System Logs**
```http
GET /api/system-logs
```

**Query Parameters:**
- `start_date`: Filter logs from date (ISO 8601)
- `end_date`: Filter logs to date (ISO 8601)
- `event_types`: Comma-separated list of event types
- `user_ids`: Comma-separated list of user IDs
- `usernames`: Comma-separated list of usernames
- `ip_addresses`: Comma-separated list of IP addresses
- `resource_types`: Comma-separated list of resource types
- `resource_ids`: Comma-separated list of resource IDs
- `actions`: Comma-separated list of actions
- `success`: Filter by success status (true/false)
- `log_levels`: Comma-separated list of log levels
- `search_query`: Search across all text fields
- `limit`: Number of logs to return (max 1000)
- `offset`: Pagination offset

### **Get Log Statistics**
```http
GET /api/system-logs/stats
```

### **Export Logs**
```http
GET /api/system-logs/export?format=json
GET /api/system-logs/export?format=csv
```

### **Get Filter Options**
```http
GET /api/system-logs/filter-options
```

## Database Schema

### **System Logs Table**
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    event_type TEXT NOT NULL,
    user_id TEXT,
    username TEXT,
    ip_address TEXT,
    user_agent TEXT,
    session_id TEXT,
    resource_type TEXT,
    resource_id TEXT,
    action TEXT NOT NULL,
    details TEXT,
    success BOOLEAN DEFAULT 1,
    error_message TEXT,
    response_time_ms INTEGER,
    request_size_bytes INTEGER,
    response_size_bytes INTEGER,
    log_level TEXT DEFAULT 'INFO',
    checksum TEXT
);
```

### **Optimized Indexes**
```sql
CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp);
CREATE INDEX idx_system_logs_event_type ON system_logs(event_type);
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_username ON system_logs(username);
CREATE INDEX idx_system_logs_ip_address ON system_logs(ip_address);
CREATE INDEX idx_system_logs_session_id ON system_logs(session_id);
CREATE INDEX idx_system_logs_resource_type ON system_logs(resource_type);
CREATE INDEX idx_system_logs_resource_id ON system_logs(resource_id);
CREATE INDEX idx_system_logs_action ON system_logs(action);
CREATE INDEX idx_system_logs_success ON system_logs(success);
CREATE INDEX idx_system_logs_log_level ON system_logs(log_level);
```

## Integration with Existing Features

### **Alert Change Log Integration**
The existing alert change log system now feeds into the centralized logging infrastructure, providing:
- **Unified Audit Trail**: All alert changes appear in both systems
- **Enhanced Context**: System logs include performance metrics and user context
- **Cross-Reference**: Link between alert-specific and system-wide logs

### **Bitzy AI Assistant Integration**
- **Full Context Awareness**: Bitzy can access and analyze complete system logs
- **Pattern Recognition**: AI identifies suspicious activities and system issues
- **Troubleshooting**: Bitzy helps diagnose problems using log data
- **Compliance Reporting**: AI generates audit reports from log data

### **Performance Monitoring**
- **Response Time Tracking**: All API calls include response time metrics
- **Error Rate Monitoring**: Automatic calculation of system error rates
- **Resource Usage**: Tracks request/response sizes and system load
- **Health Checks**: Continuous monitoring of system performance

## Security Features

### **Tamper Detection**
- **Checksums**: SHA-256 hashes prevent log tampering
- **Integrity Verification**: Automatic checksum validation
- **Audit Trail**: Any tampering attempts are logged

### **Access Control**
- **Role-Based Access**: Different log access levels for different users
- **IP Tracking**: All access attempts logged with IP addresses
- **Session Management**: Links all actions to user sessions

### **Data Protection**
- **Sensitive Data Filtering**: Automatic removal of sensitive information
- **Retention Policies**: Configurable log retention periods
- **Secure Storage**: Encrypted storage for sensitive log data

## Best Practices

### **Log Analysis**
1. **Regular Monitoring**: Check system logs daily for anomalies
2. **Error Investigation**: Investigate all ERROR level logs immediately
3. **Performance Tracking**: Monitor response times and error rates
4. **User Behavior**: Analyze user patterns for security insights

### **Compliance**
1. **Audit Preparation**: Use logs for compliance audits
2. **Incident Response**: Leverage logs for security incident investigation
3. **Change Management**: Track all system changes through logs
4. **Documentation**: Maintain log analysis documentation

### **Troubleshooting**
1. **Error Correlation**: Use timestamps to correlate related errors
2. **User Journey**: Track user actions leading to issues
3. **Performance Issues**: Identify slow operations through response times
4. **System Health**: Monitor overall system health through log patterns

## Migration and Setup

The system logging infrastructure is automatically set up during database migration:

```bash
cd backend
python3 migrate_database.py
```

This creates:
- System logs table with optimized indexes
- Integration with existing change log system
- Performance monitoring capabilities
- Security features and tamper detection

## Troubleshooting

### **Common Issues**
- **Missing logs**: Check if logging service is properly integrated
- **Performance impact**: Ensure indexes are created and optimized
- **Storage growth**: Implement log rotation and archival policies
- **Access issues**: Verify user permissions for log access

### **Log Levels**
- **DEBUG**: Detailed debugging information
- **INFO**: General system information
- **WARN**: Warning conditions that should be monitored
- **ERROR**: Error conditions that require immediate attention
