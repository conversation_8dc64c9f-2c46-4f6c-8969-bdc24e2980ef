# AlertAI Documentation

Welcome to the comprehensive documentation for AlertAI - an enterprise-grade AI-powered security alert monitoring and management platform with advanced authentication, authorization, and security features.

## 📚 Table of Contents

### Getting Started
- [Overview](./overview.md) - What is AlertAI and how it works
- [Quick Start Guide](./quick-start.md) - Get up and running in 5 minutes
- [Installation](./installation.md) - Detailed setup instructions
- [Authentication Setup](./authentication/setup.md) - Setting up users and security

### Core Features
- [Authentication & Security](./authentication/README.md) - Enterprise user management and security features
- [Alert Management](./alerts/README.md) - Comprehensive alert lifecycle management
- [AI Assistant (Bitzy)](./bitzy/README.md) - AI-powered analysis and investigation assistance
- [Comments & Collaboration](./comments/README.md) - Team collaboration with real-time features
- [File Management](./files/README.md) - Secure evidence collection and document management
- [Sources Management](./sources/README.md) - Webhook sources and alert ingestion

### User Guides
- [Dashboard Overview](./user-guides/dashboard.md) - Understanding the main interface
- [Alert Analysis](./user-guides/alert-analysis.md) - How to analyze and investigate alerts
- [Threat Intelligence](./user-guides/threat-intelligence.md) - Understanding threat indicators

### Administration
- [User Management](./admin/users.md) - Managing users and permissions
- [System Configuration](./admin/configuration.md) - System settings and preferences
- [Database Management](./admin/database.md) - Database operations and maintenance

### API Reference
- [REST API](./api/README.md) - Complete API reference with authentication
- [Authentication API](./api/authentication.md) - User management and security endpoints
- [Alert Management API](./api/alerts.md) - Alert CRUD operations and workflows
- [File Management API](./api/files.md) - Secure file upload and management
- [Comments API](./api/comments.md) - Collaboration and communication features
- [AI Chat API](./api/chat.md) - AI assistant integration and analysis
- [Audit API](./api/audit.md) - Security logging and compliance features
- [Webhook API](./api/webhooks.md) - External system integration guide

### Integrations
- [Third-Party Integrations](./integrations/README.md) - Available integrations
- [Custom Integrations](./integrations/custom.md) - Building custom integrations
- [Webhook Sources](./integrations/webhook-sources.md) - Setting up webhook sources

### Troubleshooting
- [Common Issues](./troubleshooting/common-issues.md) - Frequently encountered problems
- [Error Codes](./troubleshooting/error-codes.md) - Understanding error messages
- [Performance Tuning](./troubleshooting/performance.md) - Optimizing system performance

### Development
- [Architecture](./development/architecture.md) - System architecture overview
- [Contributing](./development/contributing.md) - How to contribute to AlertAI
- [API Development](./development/api-development.md) - Extending the API

## 🚀 Quick Links

- **New to AlertAI?** Start with the [Overview](./overview.md) and [Quick Start Guide](./quick-start.md)
- **Setting up authentication?** See [Authentication Setup](./authentication/setup.md)
- **Managing alerts?** Check out [Alert Management](./alerts/README.md)
- **Team collaboration?** See [Comments & Collaboration](./comments/README.md)
- **File management?** Visit [File Management](./files/README.md)
- **AI assistant help?** See [Bitzy Documentation](./bitzy/README.md)
- **API integration?** Visit the [API Reference](./api/README.md)
- **Security best practices?** Check [Security Guide](./authentication/security.md)
- **Having issues?** Check [Troubleshooting](./troubleshooting/common-issues.md)

## 📖 Documentation Structure

Each section contains:
- **Overview** - High-level explanation of the feature
- **Step-by-step guides** - Detailed instructions with screenshots
- **Best practices** - Recommended approaches and tips
- **Troubleshooting** - Common issues and solutions
- **Examples** - Real-world use cases and configurations

## 🔄 Keep Updated

This documentation is continuously updated to reflect the latest features and improvements in AlertAI. For the most current information, always refer to this documentation.

## 💬 Need Help?

If you can't find what you're looking for in this documentation:
1. Check the [Troubleshooting](./troubleshooting/common-issues.md) section
2. Use the AI assistant (Bitzy) within AlertAI for contextual help
3. Contact your system administrator

---

*Last updated: June 2025*
