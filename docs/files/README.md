# File Management

Comprehensive guide to managing files and attachments in AlertAI for evidence collection, documentation, and collaboration.

## Overview

AlertAI's file management system provides:
- **Secure file uploads** with type and size validation
- **Evidence collection** for security investigations
- **Document sharing** for team collaboration
- **Access control** with role-based permissions
- **Audit trails** for compliance and tracking
- **Integration capabilities** with external storage systems

## File Security Features

### Upload Security
- **File type validation** with configurable allowed types
- **File size limits** to prevent abuse and storage issues
- **Malware scanning** integration (configurable)
- **Path traversal protection** to prevent directory attacks
- **Content validation** to ensure file integrity

### Access Control
- **Role-based permissions** for file access
- **Owner-based access** for uploaded files
- **Admin override** capabilities for compliance
- **Audit logging** for all file operations
- **Secure download** with authentication required

### Storage Security
- **Encrypted storage** for sensitive files
- **Secure file paths** with UUID-based naming
- **Backup integration** for data protection
- **Retention policies** for compliance requirements
- **Secure deletion** with overwrite capabilities

## File Operations

### Uploading Files

#### Via Web Interface

1. **Navigate to Alert**: Open the alert you want to attach files to
2. **Find Upload Section**: Look for "Files" or "Attachments" tab
3. **Select Files**: Click "Upload" or drag and drop files
4. **Add Description**: Provide context for each file
5. **Upload**: Files are validated and securely stored

#### Via API

```bash
# Upload file to alert
curl -X POST "http://localhost:8002/api/alerts/123/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@evidence.pcap" \
  -F "description=Network capture during incident" \
  -F "category=evidence"
```

#### Bulk Upload

```bash
# Upload multiple files
for file in *.log; do
  curl -X POST "http://localhost:8002/api/alerts/123/upload" \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -F "file=@$file" \
    -F "description=Log file: $file" \
    -F "category=logs"
done
```

### File Types and Categories

#### Supported File Types

| Category | File Types | Max Size | Use Case |
|----------|------------|----------|----------|
| **Images** | JPG, PNG, GIF, BMP | 10MB | Screenshots, diagrams |
| **Documents** | PDF, DOC, DOCX, TXT | 25MB | Reports, documentation |
| **Logs** | LOG, TXT, JSON, XML | 50MB | System logs, traces |
| **Evidence** | PCAP, HAR, ZIP | 100MB | Network captures, forensics |
| **Scripts** | PY, SH, PS1, BAT | 1MB | Analysis scripts, tools |

#### File Categories

- **Evidence**: Forensic evidence and artifacts
- **Documentation**: Reports, procedures, and guides
- **Screenshots**: Visual evidence and demonstrations
- **Logs**: System and application logs
- **Tools**: Scripts and utilities for analysis
- **Reports**: Generated reports and summaries

### Viewing and Managing Files

#### List Alert Files

```bash
curl -X GET "http://localhost:8002/api/alerts/123/files" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response:
```json
{
  "files": [
    {
      "id": 456,
      "filename": "network_capture.pcap",
      "original_name": "network_capture.pcap",
      "size": 2048576,
      "content_type": "application/vnd.tcpdump.pcap",
      "category": "evidence",
      "description": "Network capture during incident",
      "uploaded_by": {
        "id": 5,
        "name": "John Analyst",
        "email": "<EMAIL>"
      },
      "uploaded_at": "2024-12-16T14:30:00Z",
      "download_count": 3,
      "last_accessed": "2024-12-16T15:45:00Z"
    }
  ],
  "total": 1,
  "total_size": 2048576
}
```

#### Get File Metadata

```bash
curl -X GET "http://localhost:8002/api/files/456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Download File

```bash
curl -X GET "http://localhost:8002/api/files/456/download" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o "downloaded_file.pcap"
```

### File Analysis and Processing

#### Automatic Analysis

AlertAI can automatically analyze certain file types:

- **Log files**: Extract timestamps, error patterns, and anomalies
- **Network captures**: Identify protocols, connections, and suspicious traffic
- **Images**: Extract metadata and perform OCR for text content
- **Documents**: Index content for search and extract key information

#### AI-Powered Analysis

```bash
# Request AI analysis of uploaded file
curl -X POST "http://localhost:8002/api/files/456/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "analysis_type": "security",
    "focus_areas": ["network_traffic", "malware_indicators"]
  }'
```

#### File Correlation

```bash
# Find related files across alerts
curl -X GET "http://localhost:8002/api/files/search" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -G -d "hash=sha256:abc123..." \
  -d "filename=suspicious.exe" \
  -d "content_type=application/x-executable"
```

## File Organization

### Folder Structure

Files are organized by:
- **Alert ID**: Primary organization by alert
- **Category**: Secondary organization by file type
- **Date**: Tertiary organization by upload date
- **User**: Files can be filtered by uploader

### Tagging and Metadata

#### File Tags

```bash
# Add tags to file
curl -X POST "http://localhost:8002/api/files/456/tags" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "tags": ["malware", "network", "c2_communication"]
  }'
```

#### Custom Metadata

```bash
# Add custom metadata
curl -X PUT "http://localhost:8002/api/files/456/metadata" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "metadata": {
      "source_system": "firewall_logs",
      "collection_method": "automated",
      "retention_period": "7_years",
      "classification": "confidential"
    }
  }'
```

### Search and Filtering

#### Advanced File Search

```bash
# Search files with multiple criteria
curl -X GET "http://localhost:8002/api/files/search" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -G \
  -d "category=evidence" \
  -d "uploaded_after=2024-12-01" \
  -d "size_min=1000000" \
  -d "tags=malware,network" \
  -d "content_type=application/vnd.tcpdump.pcap"
```

#### Full-Text Search

```bash
# Search file content (for text files)
curl -X GET "http://localhost:8002/api/files/search" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -G -d "content=suspicious+activity" \
  -d "file_types=text,log"
```

## File Sharing and Collaboration

### Sharing Files

#### Generate Secure Share Link

```bash
# Create temporary share link
curl -X POST "http://localhost:8002/api/files/456/share" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "expires_in": 3600,
    "password_protected": true,
    "download_limit": 5
  }'
```

#### Share with Team Members

```bash
# Share file with specific users
curl -X POST "http://localhost:8002/api/files/456/share" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "users": [7, 8, 9],
    "permissions": ["read", "download"],
    "message": "Please review this evidence file"
  }'
```

### File Comments

#### Add File Comments

```bash
# Comment on a file
curl -X POST "http://localhost:8002/api/files/456/comments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "content": "This PCAP shows clear C2 communication patterns. See packets 1250-1300.",
    "mentions": ["@john.analyst", "@security.team"]
  }'
```

#### File Annotations

```bash
# Add annotations to specific parts of file
curl -X POST "http://localhost:8002/api/files/456/annotations" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "type": "highlight",
    "position": {"line": 1250, "column": 10},
    "content": "Suspicious outbound connection",
    "category": "threat_indicator"
  }'
```

## File Administration

### Storage Management

#### Storage Statistics

```bash
# Get storage usage statistics
curl -X GET "http://localhost:8002/api/admin/storage/stats" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

Response:
```json
{
  "total_files": 1250,
  "total_size": 5368709120,
  "by_category": {
    "evidence": {"count": 450, "size": 3221225472},
    "documentation": {"count": 300, "size": 1073741824},
    "screenshots": {"count": 500, "size": 1073741824}
  },
  "by_age": {
    "last_7_days": {"count": 50, "size": 536870912},
    "last_30_days": {"count": 200, "size": 2147483648},
    "older": {"count": 1000, "size": 2684354560}
  }
}
```

#### Cleanup Old Files

```bash
# Clean up files older than retention period
curl -X POST "http://localhost:8002/api/admin/storage/cleanup" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "older_than_days": 365,
    "categories": ["logs", "temporary"],
    "dry_run": true
  }'
```

### File Permissions

#### Set File Permissions

```bash
# Update file permissions
curl -X PUT "http://localhost:8002/api/files/456/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "read": ["analyst", "admin"],
    "write": ["admin"],
    "delete": ["admin"],
    "share": ["analyst", "admin"]
  }'
```

#### Bulk Permission Updates

```bash
# Update permissions for multiple files
curl -X PUT "http://localhost:8002/api/files/bulk/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "file_ids": [456, 457, 458],
    "permissions": {
      "read": ["all_users"],
      "download": ["analyst", "admin"]
    }
  }'
```

## Configuration

### File Upload Settings

```bash
# Environment variables for file management
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/plain,application/vnd.tcpdump.pcap
UPLOAD_DIRECTORY=/var/alertai/uploads
ENABLE_FILE_SCANNING=true
FILE_RETENTION_DAYS=2555  # 7 years
```

### Storage Backends

#### Local Storage (Default)

```bash
STORAGE_BACKEND=local
STORAGE_PATH=/var/alertai/uploads
STORAGE_PERMISSIONS=0640
```

#### S3-Compatible Storage

```bash
STORAGE_BACKEND=s3
S3_BUCKET=alertai-files
S3_REGION=us-east-1
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key
S3_ENDPOINT=https://s3.amazonaws.com
```

#### Network Storage

```bash
STORAGE_BACKEND=nfs
NFS_MOUNT=/mnt/alertai-storage
NFS_OPTIONS=rw,sync,hard
```

## Best Practices

### File Organization
1. **Use descriptive filenames** that indicate content and purpose
2. **Add meaningful descriptions** for context and searchability
3. **Categorize files appropriately** for better organization
4. **Tag files consistently** for easy filtering and correlation
5. **Include metadata** for compliance and tracking

### Security Practices
1. **Validate all uploads** before processing or storage
2. **Scan files for malware** in high-security environments
3. **Encrypt sensitive files** at rest and in transit
4. **Implement retention policies** for compliance requirements
5. **Monitor file access** for security and audit purposes

### Performance Optimization
1. **Compress large files** before upload when possible
2. **Use appropriate file formats** for the content type
3. **Implement file deduplication** to save storage space
4. **Archive old files** to cheaper storage tiers
5. **Monitor storage usage** and plan for growth

## Troubleshooting

### Upload Issues

```bash
# Check file upload limits
curl -X GET "http://localhost:8002/api/files/limits" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Verify file type is allowed
curl -X GET "http://localhost:8002/api/files/allowed-types" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Storage Issues

```bash
# Check storage health
curl -X GET "http://localhost:8002/api/admin/storage/health" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Test storage backend
curl -X POST "http://localhost:8002/api/admin/storage/test" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### Permission Issues

```bash
# Check file permissions
curl -X GET "http://localhost:8002/api/files/456/permissions" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Verify user access
curl -X GET "http://localhost:8002/api/files/456/access" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Next Steps

1. **[Alert Management](../alerts/README.md)** - Learn how files integrate with alerts
2. **[Comments & Collaboration](../comments/README.md)** - Use files in team collaboration
3. **[API Reference](../api/files.md)** - Complete file management API
4. **[Security Best Practices](../authentication/security.md)** - Secure file handling

---

*For API details, see [File Management API](../api/files.md)*
