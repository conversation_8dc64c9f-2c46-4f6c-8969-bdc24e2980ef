# Alert Management API

Complete API reference for managing security alerts in AlertAI with authentication, filtering, and advanced features.

## Overview

The Alert Management API provides comprehensive CRUD operations for security alerts with enterprise features including:
- **Authentication required** for all endpoints
- **Role-based access control** with granular permissions
- **Advanced filtering and search** with pagination
- **Real-time updates** with WebSocket support
- **Audit logging** for all operations
- **Rate limiting** for API protection

## Authentication

All alert endpoints require authentication using JWT tokens:

```bash
Authorization: Bearer YOUR_JWT_TOKEN
```

## Base URL

```
http://localhost:8002/api/alerts  # Development
https://api.yourdomain.com/api/alerts  # Production
```

## Endpoints

### List Alerts

Get a paginated list of alerts with filtering and search capabilities.

```http
GET /api/alerts
```

#### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | integer | Page number (1-based) | `?page=2` |
| `per_page` | integer | Items per page (max 100) | `?per_page=50` |
| `sort` | string | Sort field and direction | `?sort=created_at:desc` |
| `severity` | string | Filter by severity | `?severity=high,critical` |
| `status` | string | Filter by status | `?status=open,in_progress` |
| `assigned_to` | integer | Filter by assigned user ID | `?assigned_to=5` |
| `source` | string | Filter by alert source | `?source=SIEM,IDS` |
| `tags` | string | Filter by tags | `?tags=malware,network` |
| `search` | string | Full-text search | `?search=suspicious+activity` |
| `created_after` | datetime | Filter by creation date | `?created_after=2024-12-01` |
| `created_before` | datetime | Filter by creation date | `?created_before=2024-12-31` |

#### Example Request

```bash
curl -X GET "http://localhost:8002/api/alerts?severity=high&status=open&page=1&per_page=25" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Response

```json
{
  "alerts": [
    {
      "id": 123,
      "title": "Suspicious Network Activity",
      "description": "Unusual outbound connections detected from workstation",
      "severity": "high",
      "status": "open",
      "priority": 85,
      "source": "Network Monitoring",
      "assigned_to": {
        "id": 5,
        "name": "John Analyst",
        "email": "<EMAIL>"
      },
      "metadata": {
        "source_ip": "*************",
        "destination_ip": "************",
        "port": 443,
        "protocol": "HTTPS"
      },
      "tags": ["network", "outbound", "suspicious"],
      "created_at": "2024-12-16T10:30:00Z",
      "updated_at": "2024-12-16T11:15:00Z",
      "deadline": "2024-12-16T18:30:00Z",
      "comments_count": 3,
      "files_count": 1,
      "ai_analysis": {
        "confidence": 0.87,
        "threat_level": "high",
        "mitre_techniques": ["T1071.001", "T1041"]
      }
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 25,
    "total": 150,
    "pages": 6,
    "has_next": true,
    "has_prev": false
  },
  "filters_applied": {
    "severity": ["high"],
    "status": ["open"]
  }
}
```

### Get Alert

Retrieve a specific alert by ID with full details.

```http
GET /api/alerts/{id}
```

#### Example Request

```bash
curl -X GET "http://localhost:8002/api/alerts/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Response

```json
{
  "id": 123,
  "title": "Suspicious Network Activity",
  "description": "Unusual outbound connections detected from workstation ************* to external IP ************. Multiple HTTPS connections observed over 10-minute period.",
  "severity": "high",
  "status": "open",
  "priority": 85,
  "source": "Network Monitoring",
  "external_id": "NM-2024-001234",
  "assigned_to": {
    "id": 5,
    "name": "John Analyst",
    "email": "<EMAIL>",
    "role": "analyst"
  },
  "created_by": {
    "id": 1,
    "name": "System",
    "type": "automated"
  },
  "metadata": {
    "source_ip": "*************",
    "destination_ip": "************",
    "port": 443,
    "protocol": "HTTPS",
    "bytes_transferred": 2048576,
    "connection_count": 15,
    "user_agent": "Mozilla/5.0...",
    "hostname": "workstation-01.company.com"
  },
  "tags": ["network", "outbound", "suspicious", "c2"],
  "created_at": "2024-12-16T10:30:00Z",
  "updated_at": "2024-12-16T11:15:00Z",
  "deadline": "2024-12-16T18:30:00Z",
  "sla_status": "within_sla",
  "comments_count": 3,
  "files_count": 1,
  "ai_analysis": {
    "confidence": 0.87,
    "threat_level": "high",
    "mitre_techniques": ["T1071.001", "T1041"],
    "iocs": [
      {
        "type": "ip",
        "value": "************",
        "threat_type": "c2_server"
      }
    ],
    "recommendations": [
      "Block outbound connections to ************",
      "Investigate other systems for similar activity",
      "Check for persistence mechanisms"
    ],
    "analyzed_at": "2024-12-16T10:35:00Z"
  },
  "audit_trail": [
    {
      "action": "created",
      "user": "System",
      "timestamp": "2024-12-16T10:30:00Z"
    },
    {
      "action": "assigned",
      "user": "Team Lead",
      "details": "Assigned to John Analyst",
      "timestamp": "2024-12-16T10:45:00Z"
    }
  ]
}
```

### Create Alert

Create a new security alert.

```http
POST /api/alerts
```

#### Required Permissions
- `alerts:write` permission required

#### Request Body

```json
{
  "title": "Malware Detection on Endpoint",
  "description": "Suspicious executable detected and quarantined on endpoint EP-001",
  "severity": "critical",
  "source": "Endpoint Protection",
  "external_id": "EP-2024-005678",
  "metadata": {
    "hostname": "laptop-001.company.com",
    "file_path": "C:\\Users\\<USER>\\Downloads\\suspicious.exe",
    "file_hash": "a1b2c3d4e5f6...",
    "detection_engine": "ClamAV",
    "quarantine_status": "quarantined"
  },
  "tags": ["malware", "endpoint", "quarantined"],
  "assigned_to": 7,
  "priority": 95,
  "deadline": "2024-12-16T16:00:00Z"
}
```

#### Example Request

```bash
curl -X POST "http://localhost:8002/api/alerts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Malware Detection on Endpoint",
    "description": "Suspicious executable detected and quarantined",
    "severity": "critical",
    "source": "Endpoint Protection",
    "metadata": {
      "hostname": "laptop-001.company.com",
      "file_hash": "a1b2c3d4e5f6..."
    },
    "tags": ["malware", "endpoint"]
  }'
```

#### Response

```json
{
  "id": 124,
  "title": "Malware Detection on Endpoint",
  "description": "Suspicious executable detected and quarantined",
  "severity": "critical",
  "status": "open",
  "priority": 95,
  "source": "Endpoint Protection",
  "created_at": "2024-12-16T12:00:00Z",
  "updated_at": "2024-12-16T12:00:00Z",
  "created_by": {
    "id": 5,
    "name": "John Analyst",
    "email": "<EMAIL>"
  }
}
```

### Update Alert

Update an existing alert.

```http
PUT /api/alerts/{id}
```

#### Required Permissions
- `alerts:write` permission required
- Must be assigned to the alert or have admin role

#### Request Body

```json
{
  "status": "in_progress",
  "priority": 90,
  "notes": "Investigation started - checking for lateral movement",
  "tags": ["malware", "endpoint", "investigating"],
  "metadata": {
    "investigation_status": "in_progress",
    "analyst_notes": "Found additional suspicious files"
  }
}
```

#### Example Request

```bash
curl -X PUT "http://localhost:8002/api/alerts/124" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "status": "in_progress",
    "notes": "Investigation started"
  }'
```

### Assign Alert

Assign an alert to a specific analyst.

```http
POST /api/alerts/{id}/assign
```

#### Required Permissions
- `alerts:write` permission required

#### Request Body

```json
{
  "assigned_to": 7,
  "reason": "Network security specialist",
  "notify": true
}
```

#### Example Request

```bash
curl -X POST "http://localhost:8002/api/alerts/124/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "assigned_to": 7,
    "reason": "Malware analysis expert"
  }'
```

### Delete Alert

Soft delete an alert (admin only).

```http
DELETE /api/alerts/{id}
```

#### Required Permissions
- `alerts:delete` permission required (admin only)

#### Example Request

```bash
curl -X DELETE "http://localhost:8002/api/alerts/124" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### Bulk Operations

#### Bulk Update Alerts

```http
PUT /api/alerts/bulk
```

#### Request Body

```json
{
  "alert_ids": [123, 124, 125],
  "updates": {
    "status": "resolved",
    "resolution_notes": "False positive - legitimate traffic"
  }
}
```

#### Bulk Assign Alerts

```http
POST /api/alerts/bulk/assign
```

#### Request Body

```json
{
  "alert_ids": [123, 124, 125],
  "assigned_to": 7,
  "reason": "Batch assignment for investigation"
}
```

## Alert Statistics

### Get Alert Statistics

```http
GET /api/alerts/stats
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `period` | string | Time period (7d, 30d, 90d) |
| `group_by` | string | Group by field (severity, status, source) |

#### Example Request

```bash
curl -X GET "http://localhost:8002/api/alerts/stats?period=30d&group_by=severity" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Response

```json
{
  "total_alerts": 1250,
  "period": "30d",
  "by_severity": {
    "critical": 45,
    "high": 180,
    "medium": 520,
    "low": 505
  },
  "by_status": {
    "open": 125,
    "in_progress": 78,
    "resolved": 1047
  },
  "by_source": {
    "SIEM": 450,
    "IDS": 320,
    "Endpoint Protection": 280,
    "Network Monitoring": 200
  },
  "resolution_metrics": {
    "average_resolution_time": "4.2 hours",
    "median_resolution_time": "2.8 hours",
    "sla_compliance": 0.94
  },
  "trends": {
    "daily_average": 41.7,
    "week_over_week_change": 0.12,
    "month_over_month_change": -0.05
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid alert data",
    "details": {
      "field": "severity",
      "issue": "Must be one of: low, medium, high, critical"
    }
  },
  "timestamp": "2024-12-16T12:00:00Z"
}
```

### Common Error Codes

| HTTP Code | Error Code | Description |
|-----------|------------|-------------|
| 400 | `VALIDATION_ERROR` | Invalid request data |
| 401 | `UNAUTHORIZED` | Authentication required |
| 403 | `FORBIDDEN` | Insufficient permissions |
| 404 | `ALERT_NOT_FOUND` | Alert does not exist |
| 409 | `CONFLICT` | Alert conflict (e.g., already assigned) |
| 422 | `INVALID_STATUS_TRANSITION` | Invalid status change |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many requests |

## Rate Limiting

Alert API endpoints have the following rate limits:

- **General endpoints**: 100 requests per minute per user
- **Bulk operations**: 10 requests per minute per user
- **Statistics endpoints**: 20 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## WebSocket Updates

Real-time alert updates are available via WebSocket:

```javascript
const ws = new WebSocket('ws://localhost:8002/ws/alerts');

ws.onmessage = function(event) {
  const update = JSON.parse(event.data);
  console.log('Alert update:', update);
};
```

## SDK Examples

### Python SDK

```python
import requests

class AlertAIClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_alerts(self, **filters):
        response = requests.get(
            f'{self.base_url}/api/alerts',
            headers=self.headers,
            params=filters
        )
        return response.json()
    
    def create_alert(self, alert_data):
        response = requests.post(
            f'{self.base_url}/api/alerts',
            headers=self.headers,
            json=alert_data
        )
        return response.json()
    
    def update_alert(self, alert_id, updates):
        response = requests.put(
            f'{self.base_url}/api/alerts/{alert_id}',
            headers=self.headers,
            json=updates
        )
        return response.json()

# Usage
client = AlertAIClient('http://localhost:8002', 'your_token')
alerts = client.get_alerts(severity='high', status='open')
```

### JavaScript SDK

```javascript
class AlertAIClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getAlerts(filters = {}) {
        const params = new URLSearchParams(filters);
        const response = await fetch(
            `${this.baseUrl}/api/alerts?${params}`,
            { headers: this.headers }
        );
        return response.json();
    }
    
    async createAlert(alertData) {
        const response = await fetch(`${this.baseUrl}/api/alerts`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(alertData)
        });
        return response.json();
    }
    
    async updateAlert(alertId, updates) {
        const response = await fetch(`${this.baseUrl}/api/alerts/${alertId}`, {
            method: 'PUT',
            headers: this.headers,
            body: JSON.stringify(updates)
        });
        return response.json();
    }
}

// Usage
const client = new AlertAIClient('http://localhost:8002', 'your_token');
const alerts = await client.getAlerts({ severity: 'high', status: 'open' });
```

## Next Steps

- **[Comments API](./comments.md)** - Add comments and collaborate on alerts
- **[File Management API](./files.md)** - Attach evidence files to alerts
- **[AI Chat API](./chat.md)** - Use AI analysis for alerts
- **[Authentication API](./authentication.md)** - Manage API authentication

---

*For more examples and integration guides, see the [API Overview](./README.md)*
