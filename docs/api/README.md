# AlertAI API Reference

Complete API documentation for AlertAI's REST API with authentication, rate limiting, and comprehensive security features.

## Overview

The AlertAI API provides programmatic access to all platform features including:
- **Authentication and user management**
- **Alert creation, retrieval, and management**
- **Comment and collaboration features**
- **File upload and management**
- **AI-powered analysis and chat**
- **Audit logging and compliance reporting**

## Base URL

```
http://localhost:8002  # Development
https://api.yourdomain.com  # Production
```

## Authentication

All API endpoints (except public health checks) require authentication using JWT tokens.

### Getting a Token

```bash
POST /api/auth/token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=SecurePassword123
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### Using the Token

Include the token in the Authorization header:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default limit**: 100 requests per minute per user
- **Authentication endpoints**: 5 requests per minute per IP
- **File upload endpoints**: 10 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Response Format

### Success Response

```json
{
  "data": {
    // Response data
  },
  "message": "Success",
  "timestamp": "2024-12-16T10:30:00Z"
}
```

### Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-12-16T10:30:00Z"
}
```

### Pagination

List endpoints support pagination:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 50,
    "total": 150,
    "pages": 3,
    "has_next": true,
    "has_prev": false
  }
}
```

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/init-system` | Initialize system with admin user | No |
| POST | `/api/auth/token` | Login and get access token | No |
| POST | `/api/auth/register` | Register new user | Yes (Admin) |
| GET | `/api/auth/me` | Get current user info | Yes |
| POST | `/api/auth/logout` | Logout and invalidate token | Yes |
| POST | `/api/auth/change-password` | Change user password | Yes |

### User Management Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/users` | List all users | Yes (Admin) |
| GET | `/api/users/{id}` | Get specific user | Yes (Admin) |
| PUT | `/api/users/{id}` | Update user information | Yes (Admin) |
| DELETE | `/api/users/{id}` | Deactivate user | Yes (Admin) |
| GET | `/api/users/{id}/permissions` | Get user permissions | Yes (Admin) |
| POST | `/api/users/{id}/permissions` | Grant permissions | Yes (Admin) |

### Alert Management Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/alerts` | List alerts with pagination | Yes |
| GET | `/api/alerts/{id}` | Get specific alert | Yes |
| POST | `/api/alerts` | Create new alert | Yes (Analyst+) |
| PUT | `/api/alerts/{id}` | Update alert | Yes (Analyst+) |
| DELETE | `/api/alerts/{id}` | Delete alert (soft delete) | Yes (Admin) |
| POST | `/api/alerts/{id}/assign` | Assign alert to analyst | Yes (Analyst+) |

### Comment Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/alerts/{id}/comments` | Get alert comments | Yes |
| POST | `/api/alerts/{id}/comments` | Add comment to alert | Yes (Analyst+) |
| PUT | `/api/comments/{id}` | Update comment | Yes (Owner/Admin) |
| DELETE | `/api/comments/{id}` | Delete comment | Yes (Owner/Admin) |
| POST | `/api/comments/beautify` | AI-beautify comment text | Yes (Analyst+) |

### File Management Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/alerts/{id}/upload` | Upload file to alert | Yes (Analyst+) |
| GET | `/api/alerts/{id}/files` | Get alert files | Yes |
| GET | `/api/files/{id}` | Get file metadata | Yes |
| GET | `/api/files/{id}/download` | Download file | Yes |
| DELETE | `/api/files/{id}` | Delete file | Yes (Owner/Admin) |

### AI Chat Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/alerts/{id}/chat` | Chat with AI about alert | Yes (Analyst+) |
| GET | `/api/alerts/{id}/ai-findings` | Get AI analysis results | Yes |
| POST | `/api/ai/analyze` | Request AI analysis | Yes (Analyst+) |

### Audit and Monitoring Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/audit` | Get audit logs | Yes (Admin/Analyst) |
| GET | `/api/audit/stats` | Get audit statistics | Yes (Admin) |
| POST | `/api/audit/export` | Export audit logs | Yes (Admin) |
| DELETE | `/api/audit/cleanup` | Clean up old logs | Yes (Admin) |

### System Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/health` | System health check | No |
| GET | `/api/version` | Get API version | No |
| GET | `/api/stats` | Get system statistics | Yes (Admin) |

## Error Codes

| HTTP Code | Error Code | Description |
|-----------|------------|-------------|
| 400 | `BAD_REQUEST` | Invalid request format or parameters |
| 401 | `UNAUTHORIZED` | Authentication required or invalid token |
| 403 | `FORBIDDEN` | Insufficient permissions |
| 404 | `NOT_FOUND` | Resource not found |
| 409 | `CONFLICT` | Resource conflict (e.g., duplicate email) |
| 422 | `VALIDATION_ERROR` | Input validation failed |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many requests |
| 500 | `INTERNAL_ERROR` | Server error |

## Common Parameters

### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | integer | Page number (1-based) | `?page=2` |
| `per_page` | integer | Items per page (max 100) | `?per_page=50` |
| `sort` | string | Sort field and direction | `?sort=created_at:desc` |
| `filter` | string | Filter criteria | `?filter=status:open` |
| `search` | string | Search query | `?search=malware` |

### Common Headers

| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | Yes* | Bearer token for authentication |
| `Content-Type` | Yes** | Request content type |
| `Accept` | No | Response format (default: application/json) |
| `User-Agent` | No | Client identification |

*Required for authenticated endpoints
**Required for POST/PUT requests with body

## SDK and Libraries

### Python SDK

```python
import requests

class AlertAIClient:
    def __init__(self, base_url, token=None):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        if token:
            self.session.headers.update({
                'Authorization': f'Bearer {token}'
            })
    
    def login(self, email, password):
        response = self.session.post(
            f'{self.base_url}/api/auth/token',
            data={'username': email, 'password': password}
        )
        if response.status_code == 200:
            self.token = response.json()['access_token']
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
        return response.json()
    
    def get_alerts(self, page=1, per_page=50):
        response = self.session.get(
            f'{self.base_url}/api/alerts',
            params={'page': page, 'per_page': per_page}
        )
        return response.json()
    
    def create_alert(self, alert_data):
        response = self.session.post(
            f'{self.base_url}/api/alerts',
            json=alert_data
        )
        return response.json()

# Usage
client = AlertAIClient('http://localhost:8002')
client.login('<EMAIL>', 'password')
alerts = client.get_alerts()
```

### JavaScript/Node.js SDK

```javascript
class AlertAIClient {
    constructor(baseUrl, token = null) {
        this.baseUrl = baseUrl;
        this.token = token;
    }
    
    async login(email, password) {
        const response = await fetch(`${this.baseUrl}/api/auth/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `username=${email}&password=${password}`
        });
        
        if (response.ok) {
            const data = await response.json();
            this.token = data.access_token;
            return data;
        }
        throw new Error('Login failed');
    }
    
    async getAlerts(page = 1, perPage = 50) {
        const response = await fetch(
            `${this.baseUrl}/api/alerts?page=${page}&per_page=${perPage}`,
            {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            }
        );
        return response.json();
    }
    
    async createAlert(alertData) {
        const response = await fetch(`${this.baseUrl}/api/alerts`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(alertData)
        });
        return response.json();
    }
}

// Usage
const client = new AlertAIClient('http://localhost:8002');
await client.login('<EMAIL>', 'password');
const alerts = await client.getAlerts();
```

## Documentation Sections

- **[Authentication API](./authentication.md)** - Detailed authentication endpoints
- **[Alert Management API](./alerts.md)** - Alert CRUD operations
- **[User Management API](./users.md)** - User administration
- **[File Management API](./files.md)** - File upload and management
- **[AI Chat API](./chat.md)** - AI-powered analysis
- **[Audit API](./audit.md)** - Audit logging and compliance
- **[Webhook API](./webhooks.md)** - Webhook integration

## Interactive Documentation

When running in development mode, interactive API documentation is available at:
- **Swagger UI**: `http://localhost:8002/docs`
- **ReDoc**: `http://localhost:8002/redoc`

---

*For specific endpoint details, see the individual API documentation sections.*
